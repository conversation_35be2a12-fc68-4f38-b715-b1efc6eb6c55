/**
 * Lexical Editor Component
 * Migrated from TipTap to Lexical for better performance and modern architecture
 */

import { useImperativeHandle, forwardRef, useCallback, useEffect } from 'react';
import { $getRoot, $createParagraphNode, $createTextNode, EditorState, $getSelection, $isRangeSelection, KEY_ENTER_COMMAND, COMMAND_PRIORITY_HIGH, INSERT_PARAGRAPH_COMMAND, ParagraphNode, $isParagraphNode } from 'lexical';
import { LexicalComposer } from '@lexical/react/LexicalComposer';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin';
import { ContentEditable } from '@lexical/react/LexicalContentEditable';
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin';
import { AutoFocusPlugin } from '@lexical/react/LexicalAutoFocusPlugin';
import { OnChangePlugin } from '@lexical/react/LexicalOnChangePlugin';
import { LinkPlugin } from '@lexical/react/LexicalLinkPlugin';
import { ListPlugin } from '@lexical/react/LexicalListPlugin';
import { MarkdownShortcutPlugin } from '@lexical/react/LexicalMarkdownShortcutPlugin';
import { TRANSFORMERS, $convertToMarkdownString, $convertFromMarkdownString, ElementTransformer, TextFormatTransformer, CHECK_LIST } from '@lexical/markdown';

import { LexicalErrorBoundary } from '@lexical/react/LexicalErrorBoundary';
import { HeadingNode, QuoteNode } from '@lexical/rich-text';
import { ListItemNode, ListNode, $isListItemNode, $isListNode } from '@lexical/list';
import { CodeHighlightNode, CodeNode } from '@lexical/code';
import { AutoLinkNode, LinkNode } from '@lexical/link';
import { use100vh } from 'react-div-100vh';
import useMounted from 'libs/web/hooks/use-mounted';
import useI18n from 'libs/web/hooks/use-i18n';

// Import custom plugins and nodes
import SlashCommandsPlugin from './plugins/slash-commands-plugin';
import FloatingToolbarPlugin from './plugins/floating-toolbar-plugin';
import { CheckListPlugin } from '@lexical/react/LexicalCheckListPlugin';
import HighlightPlugin from './plugins/highlight-plugin';
import { TabIndentationPlugin } from '@lexical/react/LexicalTabIndentationPlugin';
import ImagePlugin from './plugins/image-plugin';
import IMEPlugin from './plugins/ime-plugin';
import { ImageNode, $createImageNode, $isImageNode } from './nodes/image-node';
import { HorizontalRuleNode, $isHorizontalRuleNode, $createHorizontalRuleNode } from '@lexical/react/LexicalHorizontalRuleNode';
import { HorizontalRulePlugin } from '@lexical/react/LexicalHorizontalRulePlugin';

// 使用Lexical原生的ListItemNode，不需要自定义TaskList节点
// 使用Lexical内置的highlight格式，不需要自定义HighlightNode

export interface LexicalEditorProps {
    readOnly?: boolean;
    isPreview?: boolean;
    value?: string;
    onChange?: (value: () => string) => void;
    onCreateLink?: (title: string) => Promise<string>;
    onSearchLink?: (term: string) => Promise<any[]>;
    onClickLink?: (href: string, event: any) => void;
    onHoverLink?: (event: any) => boolean;
    className?: string;
}

export interface LexicalEditorRef {
    focusAtEnd: () => void;
    focusAtStart: () => void;
}

const theme = {
    ltr: 'ltr',
    rtl: 'rtl',
    placeholder: 'editor-placeholder',
    paragraph: 'editor-paragraph',
    quote: 'editor-quote',
    heading: {
        h1: 'editor-heading-h1',
        h2: 'editor-heading-h2',
        h3: 'editor-heading-h3',
        h4: 'editor-heading-h4',
        h5: 'editor-heading-h5',
        h6: 'editor-heading-h6',
    },
    list: {
        nested: {
            listitem: 'editor-nested-listitem',
        },
        ol: 'editor-list-ol',
        ul: 'editor-list-ul',
        listitem: 'editor-listitem',
        checklist: 'PlaygroundEditorTheme__checklist',
        listitemChecked: 'PlaygroundEditorTheme__listItemChecked',
        listitemUnchecked: 'PlaygroundEditorTheme__listItemUnchecked',
    },
    image: 'editor-image',
    link: 'editor-link',
    text: {
        bold: 'editor-text-bold',
        italic: 'editor-text-italic',
        overflowed: 'editor-text-overflowed',
        hashtag: 'editor-text-hashtag',
        underline: 'editor-text-underline',
        strikethrough: 'editor-text-strikethrough',
        underlineStrikethrough: 'editor-text-underlineStrikethrough',
        code: 'editor-text-code',
        highlight: 'editor-text-highlight',
    },
    code: 'editor-code',
    codeHighlight: {
        atrule: 'editor-tokenAttr',
        attr: 'editor-tokenAttr',
        boolean: 'editor-tokenProperty',
        builtin: 'editor-tokenSelector',
        cdata: 'editor-tokenComment',
        char: 'editor-tokenSelector',
        class: 'editor-tokenFunction',
        'class-name': 'editor-tokenFunction',
        comment: 'editor-tokenComment',
        constant: 'editor-tokenProperty',
        deleted: 'editor-tokenProperty',
        doctype: 'editor-tokenComment',
        entity: 'editor-tokenOperator',
        function: 'editor-tokenFunction',
        important: 'editor-tokenVariable',
        inserted: 'editor-tokenSelector',
        keyword: 'editor-tokenAttr',
        namespace: 'editor-tokenVariable',
        number: 'editor-tokenProperty',
        operator: 'editor-tokenOperator',
        prolog: 'editor-tokenComment',
        property: 'editor-tokenProperty',
        punctuation: 'editor-tokenPunctuation',
        regex: 'editor-tokenVariable',
        selector: 'editor-tokenSelector',
        string: 'editor-tokenSelector',
        symbol: 'editor-tokenProperty',
        tag: 'editor-tokenProperty',
        url: 'editor-tokenOperator',
        variable: 'editor-tokenVariable',
    },
};

function Placeholder() {
    const { t } = useI18n();
    return <div className="editor-placeholder">{t('Start writing...')}</div>;
}

const LexicalEditor = forwardRef<LexicalEditorRef, LexicalEditorProps>(({
    readOnly = false,
    value = '',
    onChange,
    onClickLink,
    onHoverLink,
    className = '',
}, ref) => {
    const height = use100vh();
    const mounted = useMounted();

    const initialConfig = {
        namespace: 'LexicalEditor',
        theme,
        onError(error: Error) {
            console.error('Lexical Error:', error);
        },
        nodes: [
            HeadingNode,
            ListNode,
            ListItemNode,
            QuoteNode,
            CodeNode,
            CodeHighlightNode,
            AutoLinkNode,
            LinkNode,
            // Lexical原生的ListItemNode已经支持checkbox功能
            // 使用Lexical内置的highlight格式，不需要自定义HighlightNode
            ImageNode,
            HorizontalRuleNode,
        ],
        editable: !readOnly,
        // 设置初始内容
        editorState: value ? undefined : null, // 让Lexical自己处理初始内容
    };

    // 创建自定义transformers，包含图片支持
    const IMAGE_TRANSFORMER: ElementTransformer = {
        dependencies: [ImageNode],
        export: (node) => {
            if (!$isImageNode(node)) {
                return null;
            }
            return `![${node.getAltText()}](${node.getSrc()})`;
        },
        regExp: /!\[([^\]]*)\]\(([^)]+)\)/,
        replace: (parentNode, children, match) => {
            const [, altText, src] = match;
            const imageNode = $createImageNode({
                altText,
                src,
                maxWidth: 800, // 设置最大宽度
            });
            children.forEach(child => child.remove());
            parentNode.append(imageNode);
        },
        type: 'element',
    };

    // 创建自定义的下划线转换器，使用 <u>text</u> 语法
    const UNDERLINE_TRANSFORMER: TextFormatTransformer = {
        format: ['underline'],
        tag: '<u>',
        type: 'text-format',
    };

    // 暂时移除段落缩进转换器，因为它与 Lexical 的内部状态管理冲突
    // 段落缩进功能在编辑器中正常工作，但 markdown 序列化不支持
    // 如果需要保存缩进，建议使用 HTML 格式或 JSON 格式

    // 创建水平分割线转换器
    const HR_TRANSFORMER: ElementTransformer = {
        dependencies: [HorizontalRuleNode],
        export: (node) => {
            return $isHorizontalRuleNode(node) ? '---' : null;
        },
        regExp: /^(---|\*\*\*|___)\s?$/,
        replace: (parentNode, children, match, isImport) => {
            const line = $createHorizontalRuleNode();
            if (isImport || parentNode.getNextSibling() != null) {
                parentNode.replace(line);
            } else {
                parentNode.insertBefore(line);
            }
            line.selectNext();
        },
        type: 'element',
    };

    // 重新排序transformers，确保CHECK_LIST优先级高于UNORDERED_LIST
    const customTransformers = [
        // 首先是CHECK_LIST，确保checkbox优先匹配
        CHECK_LIST,
        // 然后是其他TRANSFORMERS（但要排除重复的CHECK_LIST）
        ...TRANSFORMERS.filter(t => t !== CHECK_LIST),
        // 最后是自定义的转换器
        HR_TRANSFORMER,
        UNDERLINE_TRANSFORMER,
        IMAGE_TRANSFORMER
    ];

    // 移除了自动完成插件 - 现在通过正确的transformer优先级来解决问题

    const handleChange = useCallback((editorState: EditorState, _editor: any, tags: Set<string>) => {
        if (onChange) {
            // 模仿TipTap的简单检查：忽略历史合并和内容同步触发的更新
            if (tags.has('history-merge') || tags.has('content-sync')) {
                return;
            }

            editorState.read(() => {
                try {
                    // 使用Lexical的官方transformers进行markdown转换
                    const markdownContent = $convertToMarkdownString(customTransformers);

                    // 调试：检查是否包含checkbox语法
                    if (markdownContent.includes('[ ]') || markdownContent.includes('[x]')) {
                        console.log('🔍 Checkbox detected in markdown');
                    }

                    // 不做任何额外处理，保持Lexical原生的markdown输出
                    // Lexical的transformers已经正确处理了列表、checkbox等格式

                    // 简单的内容变化检查
                    if (markdownContent !== value) {
                        console.log('🔍 Content changed, calling onChange');
                        onChange(() => markdownContent);
                    }
                } catch (error) {
                    console.error('🔍 Error in markdown conversion:', error);
                    // 如果转换出错，保持原有内容不变
                }
            });
        }
    }, [onChange, value]);

    // 列表退出处理插件 - 处理Enter+Enter退出列表的逻辑
    const ListExitPlugin = () => {
        const [editor] = useLexicalComposerContext();

        useEffect(() => {
            return editor.registerCommand(
                KEY_ENTER_COMMAND,
                (event: KeyboardEvent | null) => {
                    const selection = $getSelection();
                    if (!$isRangeSelection(selection)) {
                        return false;
                    }

                    const anchorNode = selection.anchor.getNode();

                    // 检查是否在空的列表项中
                    if ($isListItemNode(anchorNode)) {
                        const textContent = anchorNode.getTextContent().trim();

                        if (textContent === '') {
                            const listNode = anchorNode.getParent();

                            if ($isListNode(listNode)) {
                                // 如果是空的列表项，退出列表
                                event?.preventDefault();

                                // 创建新段落并在列表后插入
                                const paragraph = $createParagraphNode();
                                listNode.insertAfter(paragraph);

                                // 删除空的列表项
                                anchorNode.remove();

                                // 选中新段落
                                paragraph.select();

                                return true;
                            }
                        }
                    }

                    return false;
                },
                COMMAND_PRIORITY_HIGH
            );
        }, [editor]);

        return null;
    };

    // 内容同步组件 - 模仿TipTap的方式
    const ContentSyncPlugin = () => {
        const [editor] = useLexicalComposerContext();

        useEffect(() => {
            if (editor && value !== undefined && mounted) {
                editor.getEditorState().read(() => {
                    const root = $getRoot();
                    const currentContent = root.getTextContent();

                    // 只有当内容真的不同时才更新
                    if (value !== currentContent) {
                        editor.update(() => {
                            // 使用Lexical官方的markdown解析器来正确渲染markdown内容
                            if (value.trim()) {
                                // 不要清理双换行符！保持原始markdown格式
                                // 双换行符在markdown中有重要意义（段落分隔、列表退出等）

                                // 使用自定义的transformers解析内容
                                $convertFromMarkdownString(value, customTransformers);
                            } else {
                                // 空内容时清空并创建一个空段落
                                const root = $getRoot();
                                root.clear();
                                const paragraph = $createParagraphNode();
                                root.append(paragraph);
                            }
                        }, { tag: 'content-sync' });
                    }
                });
            }
        }, [editor, value, mounted]);

        return null;
    };

    useImperativeHandle(ref, () => ({
        focusAtEnd: () => {
            // TODO: Implement focus at end
        },
        focusAtStart: () => {
            // TODO: Implement focus at start
        },
    }));

    if (!mounted) {
        return null;
    }

    return (
        <div className={`lexical-editor ${className}`}>
            <LexicalComposer initialConfig={initialConfig}>
                <div className="editor-container">
                    <RichTextPlugin
                        contentEditable={
                            <ContentEditable
                                className="editor-input focus:outline-none w-full"
                                spellCheck={false}
                            />
                        }
                        placeholder={<Placeholder />}
                        ErrorBoundary={LexicalErrorBoundary}
                    />
                    <HistoryPlugin />
                    <AutoFocusPlugin />
                    <LinkPlugin />
                    <ListPlugin />
                    <MarkdownShortcutPlugin transformers={customTransformers} />
                    <SlashCommandsPlugin />
                    <FloatingToolbarPlugin />
                    <CheckListPlugin />
                    <HighlightPlugin />
                    <TabIndentationPlugin />
                    <ImagePlugin />
                    <HorizontalRulePlugin />
                    <IMEPlugin enabled={true} debug={process.env.NODE_ENV === 'development'} />

                    <ListExitPlugin />

                    {/* 内容同步和onChange监听器 */}
                    <ContentSyncPlugin />
                    <OnChangePlugin
                        onChange={handleChange}
                        ignoreHistoryMergeTagChange={true}
                        ignoreSelectionChange={true}
                    />
                </div>
            </LexicalComposer>
            <style jsx global>{`
                .lexical-editor {
                    position: relative;
                }

                .editor-container {
                    position: relative;
                }

                .editor-input {
                    outline: none;
                    padding: 1rem 0;
                    min-height: calc(${height ? height + 'px' : '100vh'} - 14rem);
                    padding-bottom: 10rem;
                    width: 100%;
                    max-width: none;
                    line-height: 1.7;
                    font-size: 1rem;
                    color: inherit;
                    -webkit-spellcheck: false;
                    -moz-spellcheck: false;
                    -ms-spellcheck: false;
                    spellcheck: false;
                }

                /* 缩进样式支持 */
                .editor-input p[style*="margin-left"],
                .editor-input h1[style*="margin-left"],
                .editor-input h2[style*="margin-left"],
                .editor-input h3[style*="margin-left"],
                .editor-input h4[style*="margin-left"],
                .editor-input h5[style*="margin-left"],
                .editor-input h6[style*="margin-left"],
                .editor-input li[style*="margin-left"] {
                    transition: margin-left 0.2s ease;
                }

                .editor-placeholder {
                    color: #999;
                    overflow: hidden;
                    position: absolute;
                    text-overflow: ellipsis;
                    top: 1rem;
                    left: 0;
                    font-size: 1rem;
                    user-select: none;
                    display: inline-block;
                    pointer-events: none;
                }

                .editor-paragraph {
                    margin: 1rem 0;
                    line-height: 1.7;
                }

                .editor-heading-h1 {
                    font-size: 2.8em;
                    font-weight: bold;
                    margin: 1.5rem 0 1rem 0;
                    line-height: 1.2;
                }

                .editor-heading-h2 {
                    font-size: 2.2em;
                    font-weight: bold;
                    margin: 1.4rem 0 0.8rem 0;
                    line-height: 1.3;
                }

                .editor-heading-h3 {
                    font-size: 1.8em;
                    font-weight: bold;
                    margin: 1.3rem 0 0.6rem 0;
                    line-height: 1.4;
                }

                .editor-heading-h4 {
                    font-size: 1.5em;
                    font-weight: bold;
                    margin: 1.2rem 0 0.5rem 0;
                    line-height: 1.4;
                }

                .editor-heading-h5 {
                    font-size: 1.3em;
                    font-weight: bold;
                    margin: 1.1rem 0 0.4rem 0;
                    line-height: 1.5;
                }

                .editor-heading-h6 {
                    font-size: 1.2em;
                    font-weight: bold;
                    margin: 1rem 0 0.3rem 0;
                    line-height: 1.5;
                }

                .editor-quote {
                    margin: 1rem 0;
                    padding-left: 1rem;
                    border-left: 4px solid #ccc;
                    font-style: italic;
                    color: #666;
                }

                .editor-list-ol,
                .editor-list-ul {
                    margin: 1rem 0;
                    padding-left: 2rem;
                }

                .editor-listitem {
                    margin: 0.5rem 0;
                }

                .editor-link {
                    color: #3b82f6;
                    text-decoration: underline;
                    cursor: pointer;
                }

                .editor-link:hover {
                    color: #1d4ed8;
                }

                .editor-text-bold {
                    font-weight: bold;
                }

                .editor-text-italic {
                    font-style: italic;
                }

                .editor-text-underline {
                    text-decoration: underline;
                }

                .editor-text-strikethrough {
                    text-decoration: line-through;
                }

                .editor-text-code {
                    background-color: #e4e4e7;
                    color: black;
                    padding: 0.2rem 0.4rem;
                    border-radius: 0.25rem;
                    font-family: monospace;
                    font-size: 0.9em;
                }

                .editor-code {
                    background-color: #e4e4e7;
                    color: black;
                    border: 1px solid #e9ecef;
                    border-radius: 0.375rem;
                    padding: 1rem;
                    margin: 1rem 0;
                    font-family: 'Courier New', Courier, monospace;
                    font-size: 0.9em;
                    line-height: 1.4;
                    overflow-x: auto;
                }

                /* 深色主题下的代码样式 */
                [data-theme="dark"] .editor-text-code {
                    background-color: #3f3f46;
                    color: white;
                }

                [data-theme="dark"] .editor-code {
                    background-color: #3f3f46;
                    color: white;
                    border-color: #4b5563;
                }

                /* List Styles - 仿照Lexical官方样式，完全移除嵌套列表的原始符号 */
                .editor-container ul {
                    list-style-type: disc;
                    padding-left: 1.5rem;
                    margin: 0.5rem 0;
                }

                .editor-container ol {
                    list-style-type: decimal;
                    padding-left: 1.5rem;
                    margin: 0.5rem 0;
                }

                .editor-container li {
                    margin: 0.25rem 0;
                    line-height: 1.6;
                    padding-left: 0.25rem;
                }

                /* 嵌套列表样式 - 完全移除原始符号，使用自定义编号 */
                .editor-container ul ul {
                    list-style: none; /* 完全移除列表符号 */
                    margin: 0.25rem 0;
                    padding-left: 1.5rem;
                    counter-reset: nested-list;
                }

                .editor-container ul ul li {
                    counter-increment: nested-list;
                    position: relative;
                    margin-left: 0;
                }

                .editor-container ul ul li::before {
                    content: counter(nested-list, upper-alpha) ". ";
                    position: absolute;
                    left: -1.5rem;
                    font-weight: normal;
                    color: inherit;
                }

                .editor-container ul ul ul {
                    list-style: none;
                    margin: 0.25rem 0;
                    padding-left: 1.5rem;
                    counter-reset: nested-list-3;
                }

                .editor-container ul ul ul li {
                    counter-increment: nested-list-3;
                }

                .editor-container ul ul ul li::before {
                    content: counter(nested-list-3, lower-roman) ". ";
                    left: -1.5rem;
                }

                .editor-container ol ol {
                    list-style: none;
                    margin: 0.25rem 0;
                    padding-left: 1.5rem;
                    counter-reset: nested-list;
                }

                .editor-container ol ol li {
                    counter-increment: nested-list;
                    position: relative;
                    margin-left: 0;
                }

                .editor-container ol ol li::before {
                    content: counter(nested-list, upper-alpha) ". ";
                    position: absolute;
                    left: -1.5rem;
                    font-weight: normal;
                    color: inherit;
                }

                .editor-container ol ol ol {
                    list-style: none;
                    margin: 0.25rem 0;
                    padding-left: 1.5rem;
                    counter-reset: nested-list-3;
                }

                .editor-container ol ol ol li {
                    counter-increment: nested-list-3;
                }

                .editor-container ol ol ol li::before {
                    content: counter(nested-list-3, lower-roman) ". ";
                    left: -1.5rem;
                }



                /* Task List Styles */
                .task-list {
                    list-style: none;
                    padding-left: 0;
                    margin: 1rem 0;
                }

                .task-item {
                    display: flex;
                    align-items: flex-start;
                    margin: 0.5rem 0;
                    list-style: none;
                }

                .task-checkbox {
                    margin-right: 0.5rem;
                    margin-top: 0.125rem;
                    cursor: pointer;
                    width: 1rem;
                    height: 1rem;
                    border: 1px solid #d1d5db;
                    border-radius: 0.25rem;
                    background: white;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-shrink: 0;
                    appearance: none;
                    -webkit-appearance: none;
                }

                .task-checkbox:checked {
                    background-color: #3b82f6;
                    border-color: #3b82f6;
                    color: white;
                }

                .task-checkbox:checked::after {
                    content: '✓';
                    font-size: 0.75rem;
                    font-weight: bold;
                    color: white;
                }

                .task-content {
                    flex: 1;
                    line-height: 1.7;
                }

                .task-item[data-checked="true"] .task-content {
                    text-decoration: line-through;
                    opacity: 0.6;
                }

                /* Highlight Styles - 支持Lexical内置的highlight格式 */
                .lexical-highlight,
                mark,
                .editor-text-highlight {
                    background-color: #eab834 !important;
                    color: black !important;
                    padding: 0.1rem 0.2rem;
                    border-radius: 0.125rem;
                }

                /* 深色主题下的高亮样式 - 支持多种主题类名 */
                .dark .lexical-highlight,
                .dark mark,
                .dark .editor-text-highlight,
                [data-theme="dark"] .lexical-highlight,
                [data-theme="dark"] mark,
                [data-theme="dark"] .editor-text-highlight,
                html.dark .lexical-highlight,
                html.dark mark,
                html.dark .editor-text-highlight {
                    background-color: #3185eb !important;
                    color: white !important;
                }

                /* Image Styles */
                .editor-image img {
                    max-width: 100%;
                    height: auto;
                    border-radius: 0.5rem;
                    margin: 1rem 0;
                }

                /* Dark mode support */
                @media (prefers-color-scheme: dark) {
                    .editor-placeholder {
                        color: #6b7280;
                    }

                    .editor-quote {
                        border-left-color: #4b5563;
                        color: #9ca3af;
                    }

                    .editor-text-code {
                        background-color: #374151;
                        color: #f9fafb;
                    }

                    .editor-code {
                        background-color: #1f2937;
                        border-color: #374151;
                        color: #f9fafb;
                    }


                }

                /* 列表项符号样式 - 灰色（除了checkbox） */
                .lexical-editor ul:not([data-lexical-list-type="check"]) li::marker {
                    color: #6b7280;
                }

                .lexical-editor ol li::marker {
                    color: #6b7280;
                }

                /* Lexical原生CheckList样式 - 基于PlaygroundEditorTheme.css */
                .lexical-editor .PlaygroundEditorTheme__listItemChecked,
                .lexical-editor .PlaygroundEditorTheme__listItemUnchecked {
                    position: relative;
                    margin-left: 0.5em;
                    margin-right: 0.5em;
                    padding-left: 1.5em;
                    padding-right: 1.5em;
                    list-style-type: none;
                    outline: none;
                    display: block;
                    min-height: 1.5em;
                }

                .lexical-editor .PlaygroundEditorTheme__listItemChecked > *,
                .lexical-editor .PlaygroundEditorTheme__listItemUnchecked > * {
                    margin-left: 0.01em;
                }

                .lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before,
                .lexical-editor .PlaygroundEditorTheme__listItemChecked:before {
                    content: '';
                    width: 0.9em;
                    height: 0.9em;
                    top: 50%;
                    left: 0;
                    cursor: pointer;
                    display: block;
                    background-size: cover;
                    position: absolute;
                    transform: translateY(-50%);
                }

                .lexical-editor .PlaygroundEditorTheme__listItemChecked {
                    text-decoration: line-through;
                }

                .lexical-editor .PlaygroundEditorTheme__listItemUnchecked:focus:before,
                .lexical-editor .PlaygroundEditorTheme__listItemChecked:focus:before {
                    box-shadow: 0 0 0 2px #a6cdfe;
                    border-radius: 2px;
                }

                .lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before {
                    border: 1px solid #999;
                    border-radius: 2px;
                }

                .lexical-editor .PlaygroundEditorTheme__listItemChecked:before {
                    border: 1px solid rgb(61, 135, 245);
                    border-radius: 2px;
                    background-color: #3d87f5;
                    background-repeat: no-repeat;
                }

                .lexical-editor .PlaygroundEditorTheme__listItemChecked:after {
                    content: '';
                    cursor: pointer;
                    border-color: #fff;
                    border-style: solid;
                    position: absolute;
                    display: block;
                    top: 45%;
                    width: 0.2em;
                    left: 0.35em;
                    height: 0.4em;
                    transform: translateY(-50%) rotate(45deg);
                    border-width: 0 0.1em 0.1em 0;
                }

                /* 移除了CSS伪装的checkbox - 现在使用真正的Lexical CheckList功能 */
            `}</style>
        </div>
    );
});

LexicalEditor.displayName = 'LexicalEditor';

export default LexicalEditor;
