"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./libs/web/state/lexical-editor.ts":
/*!******************************************!*\
  !*** ./libs/web/state/lexical-editor.ts ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/src/_object_spread.mjs */ \"./node_modules/@swc/helpers/src/_object_spread.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @swc/helpers/src/_object_spread_props.mjs */ \"./node_modules/@swc/helpers/src/_object_spread_props.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var libs_web_state_note__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! libs/web/state/note */ \"./libs/web/state/note.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var libs_shared_note__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! libs/shared/note */ \"./libs/shared/note.ts\");\n/* harmony import */ var libs_web_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! libs/web/hooks/use-toast */ \"./libs/web/hooks/use-toast.ts\");\n/* harmony import */ var libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! libs/web/cache/note */ \"./libs/web/cache/note.ts\");\n/* harmony import */ var unstated_next__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! unstated-next */ \"./node_modules/unstated-next/dist/unstated-next.mjs\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash */ \"./node_modules/lodash/lodash.js\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var libs_shared_markdown_parse_markdown_title__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! libs/shared/markdown/parse-markdown-title */ \"./libs/shared/markdown/parse-markdown-title.ts\");\n/* harmony import */ var libs_web_utils_ime_state_manager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! libs/web/utils/ime-state-manager */ \"./libs/web/utils/ime-state-manager.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar ROOT_ID = \"root\";\nvar useLexicalEditor = function(initNote) {\n    // Use initNote if provided, otherwise try to get from NoteState\n    var note = initNote;\n    var createNoteWithTitle, updateNote, createNote;\n    try {\n        var noteState = libs_web_state_note__WEBPACK_IMPORTED_MODULE_0__[\"default\"].useContainer();\n        createNoteWithTitle = noteState.createNoteWithTitle;\n        updateNote = noteState.updateNote;\n        createNote = noteState.createNote;\n        // Only use noteState.note if no initNote is provided\n        if (!note) {\n            note = noteState.note;\n        }\n    } catch (error) {\n        // If NoteState is not available, we'll work with just the initNote\n        console.warn(\"NoteState not available in LexicalEditorState, using initNote only\");\n        createNoteWithTitle = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n        updateNote = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n        createNote = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n    }\n    var router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    var toast = (0,libs_web_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    var editorEl = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // 🔧 新增：快照状态管理\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null), noteSnapshot = ref[0], setNoteSnapshot = ref[1];\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\"), currentEditorContent = ref1[0], setCurrentEditorContent = ref1[1];\n    // Manual save function for IndexedDB\n    var saveToIndexedDB = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function(data) {\n            var existingNote, baseNote, updatedNote;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                            2\n                        ];\n                        return [\n                            4,\n                            libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                        ];\n                    case 1:\n                        existingNote = _state.sent();\n                        baseNote = existingNote || note;\n                        updatedNote = (0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_11__[\"default\"])({}, baseNote, data);\n                        return [\n                            4,\n                            libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].setItem(note.id, updatedNote)\n                        ];\n                    case 2:\n                        _state.sent();\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function(data) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        note\n    ]);\n    var syncToServer = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function() {\n        var isNew, localNote, noteToSave, noteData, item, noteUrl, updatedNote, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        false\n                    ];\n                    isNew = (0,lodash__WEBPACK_IMPORTED_MODULE_6__.has)(router.query, \"new\");\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        11,\n                        ,\n                        12\n                    ]);\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                    ];\n                case 2:\n                    localNote = _state.sent();\n                    noteToSave = localNote || note;\n                    if (!isNew) return [\n                        3,\n                        7\n                    ];\n                    noteData = (0,_swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_12__[\"default\"])((0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_11__[\"default\"])({}, noteToSave), {\n                        pid: router.query.pid || ROOT_ID\n                    });\n                    return [\n                        4,\n                        createNote(noteData)\n                    ];\n                case 3:\n                    item = _state.sent();\n                    if (!item) return [\n                        3,\n                        6\n                    ];\n                    noteUrl = \"/\".concat(item.id);\n                    if (!(router.asPath !== noteUrl)) return [\n                        3,\n                        5\n                    ];\n                    return [\n                        4,\n                        router.replace(noteUrl, undefined, {\n                            shallow: true\n                        })\n                    ];\n                case 4:\n                    _state.sent();\n                    _state.label = 5;\n                case 5:\n                    toast(\"Note saved to server\", \"success\");\n                    return [\n                        2,\n                        true\n                    ];\n                case 6:\n                    return [\n                        3,\n                        10\n                    ];\n                case 7:\n                    return [\n                        4,\n                        updateNote(noteToSave)\n                    ];\n                case 8:\n                    updatedNote = _state.sent();\n                    if (!updatedNote) return [\n                        3,\n                        10\n                    ];\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].setItem(updatedNote.id, updatedNote)\n                    ];\n                case 9:\n                    _state.sent();\n                    toast(\"Note updated on server\", \"success\");\n                    return [\n                        2,\n                        true\n                    ];\n                case 10:\n                    return [\n                        3,\n                        12\n                    ];\n                case 11:\n                    error = _state.sent();\n                    toast(\"Failed to save note to server\", \"error\");\n                    return [\n                        2,\n                        false\n                    ];\n                case 12:\n                    return [\n                        2,\n                        false\n                    ];\n            }\n        });\n    }), [\n        note,\n        router,\n        createNote,\n        updateNote,\n        toast\n    ]);\n    var onCreateLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function(title) {\n            var result;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!createNoteWithTitle) return [\n                            2,\n                            \"\"\n                        ];\n                        return [\n                            4,\n                            createNoteWithTitle(title)\n                        ];\n                    case 1:\n                        result = _state.sent();\n                        if (result === null || result === void 0 ? void 0 : result.id) {\n                            return [\n                                2,\n                                \"/\".concat(result.id)\n                            ];\n                        }\n                        return [\n                            2,\n                            \"\"\n                        ];\n                }\n            });\n        });\n        return function(title) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        createNoteWithTitle\n    ]);\n    var onSearchLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function(term) {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    []\n                ];\n            });\n        });\n        return function(term) {\n            return _ref.apply(this, arguments);\n        };\n    }(), []);\n    var onClickLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(href, event) {\n        if ((0,libs_shared_note__WEBPACK_IMPORTED_MODULE_3__.isNoteLink)(href)) {\n            event.preventDefault();\n            router.push(href);\n        } else {\n            window.open(href, \"_blank\", \"noopener,noreferrer\");\n        }\n    }, [\n        router\n    ]);\n    var onUploadImage = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function(_file, _id) {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(this, function(_state) {\n                // Image upload is disabled in PostgreSQL version\n                toast(\"Image upload is not supported in this version\", \"error\");\n                throw new Error(\"Image upload is not supported\");\n            });\n        });\n        return function(_file, _id) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        toast\n    ]);\n    var onHoverLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(event) {\n        return true;\n    }, []);\n    var ref2 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(), backlinks = ref2[0], setBackLinks = ref2[1];\n    var getBackLinks = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function() {\n        var linkNotes;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    console.log(note === null || note === void 0 ? void 0 : note.id);\n                    linkNotes = [];\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        linkNotes\n                    ];\n                    setBackLinks([]);\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].iterate(function(value) {\n                            var ref;\n                            if ((ref = value.linkIds) === null || ref === void 0 ? void 0 : ref.includes((note === null || note === void 0 ? void 0 : note.id) || \"\")) {\n                                linkNotes.push(value);\n                            }\n                        })\n                    ];\n                case 1:\n                    _state.sent();\n                    setBackLinks(linkNotes);\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        note === null || note === void 0 ? void 0 : note.id\n    ]);\n    // 🔧 快照初始化逻辑 - 打开笔记时设置快照\n    var initializeSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function() {\n        var cachedNote, snapshotContent, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) {\n                        // 新建笔记：快照为空值\n                        setNoteSnapshot(null);\n                        setCurrentEditorContent(\"\");\n                        console.log(\"\\uD83D\\uDD27 新建笔记：快照设置为空值\");\n                        return [\n                            2\n                        ];\n                    }\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        3,\n                        ,\n                        4\n                    ]);\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                    ];\n                case 2:\n                    cachedNote = _state.sent();\n                    snapshotContent = (cachedNote === null || cachedNote === void 0 ? void 0 : cachedNote.content) || \"\";\n                    setNoteSnapshot(snapshotContent);\n                    setCurrentEditorContent(snapshotContent);\n                    console.log(\"\\uD83D\\uDD27 已存在笔记：快照设置为缓存内容\", {\n                        noteId: note.id,\n                        hasSnapshot: !!snapshotContent\n                    });\n                    return [\n                        3,\n                        4\n                    ];\n                case 3:\n                    error = _state.sent();\n                    console.error(\"\\uD83D\\uDD27 快照初始化失败:\", error);\n                    // 失败时设置为空快照\n                    setNoteSnapshot(null);\n                    setCurrentEditorContent(\"\");\n                    return [\n                        3,\n                        4\n                    ];\n                case 4:\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        note === null || note === void 0 ? void 0 : note.id\n    ]);\n    // 当笔记ID变化时初始化快照\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n        initializeSnapshot();\n    }, [\n        initializeSnapshot\n    ]);\n    // 🔧 修复：恢复编辑器基本功能 - 更新JSON内容状态\n    var originalOnEditorChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(value) {\n        var jsonContent = value(); // 这里是JSON格式的内容\n        // 更新当前编辑器JSON内容状态\n        setCurrentEditorContent(jsonContent);\n        console.log(\"\\uD83D\\uDD27 编辑器JSON内容更新:\", {\n            contentLength: jsonContent.length\n        });\n    // 保存逻辑现在完全由SaveButton的快照对比机制处理\n    }, []);\n    // 使用智能onChange包装器 - 基于输入状态智能处理\n    var onEditorChange = (0,libs_web_utils_ime_state_manager__WEBPACK_IMPORTED_MODULE_8__.createSmartOnChange)(originalOnEditorChange, {\n        delay: 200 // 快速输入结束后200ms执行\n    });\n    // Function to handle title changes specifically\n    var onTitleChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(title) {\n        var ref;\n        (ref = saveToIndexedDB({\n            title: title,\n            updated_at: new Date().toISOString()\n        })) === null || ref === void 0 ? void 0 : ref.catch(function(v) {\n            return console.error(\"Error whilst saving title to IndexedDB: %O\", v);\n        });\n    }, [\n        saveToIndexedDB\n    ]);\n    // 🔧 新增：快照对比功能 - 供SaveButton使用\n    var compareWithSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        // 如果是新建笔记（快照为null），任何内容都算作变化\n        if (noteSnapshot === null) {\n            return currentEditorContent.trim() !== \"\";\n        }\n        // 已存在笔记：比较当前内容与快照\n        return currentEditorContent !== noteSnapshot;\n    }, [\n        noteSnapshot,\n        currentEditorContent\n    ]);\n    // 🔧 新增：获取当前编辑器状态 - 供SaveButton使用\n    var getEditorState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        return {\n            hasChanges: compareWithSnapshot(),\n            currentContent: currentEditorContent,\n            snapshot: noteSnapshot,\n            isNewNote: noteSnapshot === null\n        };\n    }, [\n        compareWithSnapshot,\n        currentEditorContent,\n        noteSnapshot\n    ]);\n    // 🔧 新增：保存当前内容到IndexedDB - 供SaveButton调用\n    var saveCurrentContent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function() {\n        var title, titleInput, parsed, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        false\n                    ];\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        3,\n                        ,\n                        4\n                    ]);\n                    if (note === null || note === void 0 ? void 0 : note.isDailyNote) {\n                        title = note.title;\n                    } else {\n                        titleInput = document.querySelector(\"h1 textarea\");\n                        if (titleInput && titleInput.value) {\n                            title = titleInput.value.trim();\n                        } else {\n                            parsed = (0,libs_shared_markdown_parse_markdown_title__WEBPACK_IMPORTED_MODULE_7__.parseMarkdownTitle)(currentEditorContent);\n                            title = parsed.title || \"Untitled\";\n                        }\n                    }\n                    // 保存到IndexedDB\n                    return [\n                        4,\n                        saveToIndexedDB({\n                            content: currentEditorContent,\n                            title: title,\n                            updated_at: new Date().toISOString()\n                        })\n                    ];\n                case 2:\n                    _state.sent();\n                    console.log(\"\\uD83D\\uDD27 内容已保存到IndexedDB\", {\n                        noteId: note.id,\n                        title: title,\n                        contentLength: currentEditorContent.length\n                    });\n                    return [\n                        2,\n                        true\n                    ];\n                case 3:\n                    error = _state.sent();\n                    console.error(\"\\uD83D\\uDD27 保存到IndexedDB失败:\", error);\n                    return [\n                        2,\n                        false\n                    ];\n                case 4:\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        note,\n        currentEditorContent,\n        saveToIndexedDB\n    ]);\n    return {\n        onCreateLink: onCreateLink,\n        onSearchLink: onSearchLink,\n        onClickLink: onClickLink,\n        onUploadImage: onUploadImage,\n        onHoverLink: onHoverLink,\n        getBackLinks: getBackLinks,\n        onEditorChange: onEditorChange,\n        onTitleChange: onTitleChange,\n        saveToIndexedDB: saveToIndexedDB,\n        syncToServer: syncToServer,\n        backlinks: backlinks,\n        editorEl: editorEl,\n        note: note,\n        // 🔧 新增：快照对比相关功能\n        getEditorState: getEditorState,\n        saveCurrentContent: saveCurrentContent,\n        compareWithSnapshot: compareWithSnapshot\n    };\n};\nvar LexicalEditorState = (0,unstated_next__WEBPACK_IMPORTED_MODULE_13__.createContainer)(useLexicalEditor);\n/* harmony default export */ __webpack_exports__[\"default\"] = (LexicalEditorState);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./libs/web/state/lexical-editor.ts\n"));

/***/ })

});