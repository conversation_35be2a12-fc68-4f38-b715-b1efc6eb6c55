{"version": 3, "names": ["Plug<PERSON><PERSON><PERSON>", "constructor", "file", "key", "options", "_map", "Map", "opts", "cwd", "filename", "set", "val", "get", "availableHelper", "name", "versionRange", "addHelper", "addImport", "buildCodeFrameError", "node", "msg", "_Error", "prototype", "getModuleName"], "sources": ["../../src/transformation/plugin-pass.ts"], "sourcesContent": ["import type File from \"./file/file\";\nimport type { NodeLocation } from \"./file/file\";\n\nexport default class PluginPass {\n  _map: Map<unknown, unknown> = new Map();\n  key: string | undefined | null;\n  file: File;\n  opts: any;\n\n  // The working directory that <PERSON><PERSON>'s programmatic options are loaded\n  // relative to.\n  cwd: string;\n\n  // The absolute path of the file being compiled.\n  filename: string | void;\n\n  constructor(file: File, key?: string | null, options?: any | null) {\n    this.key = key;\n    this.file = file;\n    this.opts = options || {};\n    this.cwd = file.opts.cwd;\n    this.filename = file.opts.filename;\n  }\n\n  set(key: unknown, val: unknown) {\n    this._map.set(key, val);\n  }\n\n  get(key: unknown): any {\n    return this._map.get(key);\n  }\n\n  availableHelper(name: string, versionRange?: string | null) {\n    return this.file.availableHelper(name, versionRange);\n  }\n\n  addHelper(name: string) {\n    return this.file.addHelper(name);\n  }\n\n  addImport() {\n    return this.file.addImport();\n  }\n\n  buildCodeFrameError(\n    node: NodeLocation | undefined | null,\n    msg: string,\n    _Error?: typeof Error,\n  ) {\n    return this.file.buildCodeFrameError(node, msg, _Error);\n  }\n}\n\nif (!process.env.BABEL_8_BREAKING) {\n  (PluginPass as any).prototype.getModuleName = function getModuleName():\n    | string\n    | undefined {\n    return this.file.getModuleName();\n  };\n}\n"], "mappings": ";;;;;;;AAGe,MAAMA,UAAN,CAAiB;EAa9BC,WAAW,CAACC,IAAD,EAAaC,GAAb,EAAkCC,OAAlC,EAAwD;IAAA,KAZnEC,IAYmE,GAZrC,IAAIC,GAAJ,EAYqC;IAAA,KAXnEH,GAWmE;IAAA,KAVnED,IAUmE;IAAA,KATnEK,IASmE;IAAA,KALnEC,GAKmE;IAAA,KAFnEC,QAEmE;IACjE,KAAKN,GAAL,GAAWA,GAAX;IACA,KAAKD,IAAL,GAAYA,IAAZ;IACA,KAAKK,IAAL,GAAYH,OAAO,IAAI,EAAvB;IACA,KAAKI,GAAL,GAAWN,IAAI,CAACK,IAAL,CAAUC,GAArB;IACA,KAAKC,QAAL,GAAgBP,IAAI,CAACK,IAAL,CAAUE,QAA1B;EACD;;EAEDC,GAAG,CAACP,GAAD,EAAeQ,GAAf,EAA6B;IAC9B,KAAKN,IAAL,CAAUK,GAAV,CAAcP,GAAd,EAAmBQ,GAAnB;EACD;;EAEDC,GAAG,CAACT,GAAD,EAAoB;IACrB,OAAO,KAAKE,IAAL,CAAUO,GAAV,CAAcT,GAAd,CAAP;EACD;;EAEDU,eAAe,CAACC,IAAD,EAAeC,YAAf,EAA6C;IAC1D,OAAO,KAAKb,IAAL,CAAUW,eAAV,CAA0BC,IAA1B,EAAgCC,YAAhC,CAAP;EACD;;EAEDC,SAAS,CAACF,IAAD,EAAe;IACtB,OAAO,KAAKZ,IAAL,CAAUc,SAAV,CAAoBF,IAApB,CAAP;EACD;;EAEDG,SAAS,GAAG;IACV,OAAO,KAAKf,IAAL,CAAUe,SAAV,EAAP;EACD;;EAEDC,mBAAmB,CACjBC,IADiB,EAEjBC,GAFiB,EAGjBC,MAHiB,EAIjB;IACA,OAAO,KAAKnB,IAAL,CAAUgB,mBAAV,CAA8BC,IAA9B,EAAoCC,GAApC,EAAyCC,MAAzC,CAAP;EACD;;AA/C6B;;;AAkDG;EAChCrB,UAAD,CAAoBsB,SAApB,CAA8BC,aAA9B,GAA8C,SAASA,aAAT,GAEhC;IACZ,OAAO,KAAKrB,IAAL,CAAUqB,aAAV,EAAP;EACD,CAJD;AAKD"}