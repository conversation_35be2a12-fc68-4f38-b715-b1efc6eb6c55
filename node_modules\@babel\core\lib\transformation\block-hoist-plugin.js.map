{"version": 3, "names": ["LOADED_PLUGIN", "loadBlockHoistPlugin", "Plugin", "blockHoistPlugin", "visitor", "traverse", "explode", "priority", "bodyNode", "_blockHoist", "stableSort", "body", "buckets", "Object", "create", "i", "length", "n", "p", "bucket", "push", "keys", "map", "k", "sort", "a", "b", "index", "key", "name", "Block", "exit", "node", "max", "hasChange", "slice"], "sources": ["../../src/transformation/block-hoist-plugin.ts"], "sourcesContent": ["import traverse from \"@babel/traverse\";\nimport type { Statement } from \"@babel/types\";\nimport type { PluginObject } from \"../config\";\nimport Plugin from \"../config/plugin\";\n\nlet LOADED_PLUGIN: Plugin | void;\n\nexport default function loadBlockHoistPlugin(): Plugin {\n  if (!LOADED_PLUGIN) {\n    // cache the loaded blockHoist plugin plugin\n    LOADED_PLUGIN = new Plugin(\n      {\n        ...blockHoistPlugin,\n        visitor: traverse.explode(blockHoistPlugin.visitor),\n      },\n      {},\n    );\n  }\n\n  return LOADED_PLUGIN;\n}\nfunction priority(bodyNode: Statement & { _blockHoist?: number | true }) {\n  const priority = bodyNode?._blockHoist;\n  if (priority == null) return 1;\n  if (priority === true) return 2;\n  return priority;\n}\n\nfunction stableSort(body: Statement[]) {\n  // By default, we use priorities of 0-4.\n  const buckets = Object.create(null);\n\n  // By collecting into buckets, we can guarantee a stable sort.\n  for (let i = 0; i < body.length; i++) {\n    const n = body[i];\n    const p = priority(n);\n\n    // In case some plugin is setting an unexpected priority.\n    const bucket = buckets[p] || (buckets[p] = []);\n    bucket.push(n);\n  }\n\n  // Sort our keys in descending order. Keys are unique, so we don't have to\n  // worry about stability.\n  const keys = Object.keys(buckets)\n    .map(k => +k)\n    .sort((a, b) => b - a);\n\n  let index = 0;\n  for (const key of keys) {\n    const bucket = buckets[key];\n    for (const n of bucket) {\n      body[index++] = n;\n    }\n  }\n  return body;\n}\n\nconst blockHoistPlugin: PluginObject = {\n  /**\n   * [Please add a description.]\n   *\n   * Priority:\n   *\n   *  - 0 We want this to be at the **very** bottom\n   *  - 1 Default node position\n   *  - 2 Priority over normal nodes\n   *  - 3 We want this to be at the **very** top\n   *  - 4 Reserved for the helpers used to implement module imports.\n   */\n\n  name: \"internal.blockHoist\",\n\n  visitor: {\n    Block: {\n      exit({ node }) {\n        const { body } = node;\n\n        // Largest SMI\n        let max = 2 ** 30 - 1;\n        let hasChange = false;\n        for (let i = 0; i < body.length; i++) {\n          const n = body[i];\n          const p = priority(n);\n          if (p > max) {\n            hasChange = true;\n            break;\n          }\n          max = p;\n        }\n        if (!hasChange) return;\n\n        // My kingdom for a stable sort!\n        node.body = stableSort(body.slice());\n      },\n    },\n  },\n};\n"], "mappings": ";;;;;;;AAAA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AAGA;;AAEA,IAAIA,aAAJ;;AAEe,SAASC,oBAAT,GAAwC;EACrD,IAAI,CAACD,aAAL,EAAoB;IAElBA,aAAa,GAAG,IAAIE,eAAJ,mBAETC,gBAFS;MAGZC,OAAO,EAAEC,mBAAA,CAASC,OAAT,CAAiBH,gBAAgB,CAACC,OAAlC;IAHG,IAKd,EALc,CAAhB;EAOD;;EAED,OAAOJ,aAAP;AACD;;AACD,SAASO,QAAT,CAAkBC,QAAlB,EAAyE;EACvE,MAAMD,QAAQ,GAAGC,QAAH,oBAAGA,QAAQ,CAAEC,WAA3B;EACA,IAAIF,QAAQ,IAAI,IAAhB,EAAsB,OAAO,CAAP;EACtB,IAAIA,QAAQ,KAAK,IAAjB,EAAuB,OAAO,CAAP;EACvB,OAAOA,QAAP;AACD;;AAED,SAASG,UAAT,CAAoBC,IAApB,EAAuC;EAErC,MAAMC,OAAO,GAAGC,MAAM,CAACC,MAAP,CAAc,IAAd,CAAhB;;EAGA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,IAAI,CAACK,MAAzB,EAAiCD,CAAC,EAAlC,EAAsC;IACpC,MAAME,CAAC,GAAGN,IAAI,CAACI,CAAD,CAAd;IACA,MAAMG,CAAC,GAAGX,QAAQ,CAACU,CAAD,CAAlB;IAGA,MAAME,MAAM,GAAGP,OAAO,CAACM,CAAD,CAAP,KAAeN,OAAO,CAACM,CAAD,CAAP,GAAa,EAA5B,CAAf;IACAC,MAAM,CAACC,IAAP,CAAYH,CAAZ;EACD;;EAID,MAAMI,IAAI,GAAGR,MAAM,CAACQ,IAAP,CAAYT,OAAZ,EACVU,GADU,CACNC,CAAC,IAAI,CAACA,CADA,EAEVC,IAFU,CAEL,CAACC,CAAD,EAAIC,CAAJ,KAAUA,CAAC,GAAGD,CAFT,CAAb;EAIA,IAAIE,KAAK,GAAG,CAAZ;;EACA,KAAK,MAAMC,GAAX,IAAkBP,IAAlB,EAAwB;IACtB,MAAMF,MAAM,GAAGP,OAAO,CAACgB,GAAD,CAAtB;;IACA,KAAK,MAAMX,CAAX,IAAgBE,MAAhB,EAAwB;MACtBR,IAAI,CAACgB,KAAK,EAAN,CAAJ,GAAgBV,CAAhB;IACD;EACF;;EACD,OAAON,IAAP;AACD;;AAED,MAAMR,gBAA8B,GAAG;EAarC0B,IAAI,EAAE,qBAb+B;EAerCzB,OAAO,EAAE;IACP0B,KAAK,EAAE;MACLC,IAAI,CAAC;QAAEC;MAAF,CAAD,EAAW;QACb,MAAM;UAAErB;QAAF,IAAWqB,IAAjB;QAGA,IAAIC,GAAG,GAAG,YAAK,EAAL,IAAU,CAApB;QACA,IAAIC,SAAS,GAAG,KAAhB;;QACA,KAAK,IAAInB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGJ,IAAI,CAACK,MAAzB,EAAiCD,CAAC,EAAlC,EAAsC;UACpC,MAAME,CAAC,GAAGN,IAAI,CAACI,CAAD,CAAd;UACA,MAAMG,CAAC,GAAGX,QAAQ,CAACU,CAAD,CAAlB;;UACA,IAAIC,CAAC,GAAGe,GAAR,EAAa;YACXC,SAAS,GAAG,IAAZ;YACA;UACD;;UACDD,GAAG,GAAGf,CAAN;QACD;;QACD,IAAI,CAACgB,SAAL,EAAgB;QAGhBF,IAAI,CAACrB,IAAL,GAAYD,UAAU,CAACC,IAAI,CAACwB,KAAL,EAAD,CAAtB;MACD;;IApBI;EADA;AAf4B,CAAvC"}