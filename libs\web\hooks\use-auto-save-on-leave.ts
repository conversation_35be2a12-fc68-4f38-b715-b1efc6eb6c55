/**
 * Auto Save on Leave Hook
 *
 * Copyright (c) 2025 waycaan
 * Licensed under the MIT License
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 */

import { useEffect, useCallback, useRef } from 'react';
import { useRouter } from 'next/router';

interface UseAutoSaveOnLeaveOptions {
    enabled?: boolean;
}

const useAutoSaveOnLeave = (options: UseAutoSaveOnLeaveOptions = {}) => {
    const { enabled = true } = options;
    const router = useRouter();
    const isAutoSavingRef = useRef(false);


    const shouldAutoSave = useCallback(() => {
        if (typeof window !== 'undefined' && (window as any).saveButtonStatus) {
            return (window as any).saveButtonStatus === 'save';
        }
        return false;
    }, []);

    const performAutoSave = useCallback(async () => {
        if (typeof window !== 'undefined' && (window as any).saveButtonAutoSave) {
            try {
                await (window as any).saveButtonAutoSave();
                return true;
            } catch (error) {
                return false;
            }
        }
        return false;
    }, []);

    // 🔧 重构：页面关闭/刷新处理 - 弹窗提示机制
    const handleBeforeUnload = useCallback((event: BeforeUnloadEvent) => {
        if (!enabled) return;

        if (shouldAutoSave()) {
            console.log('🔧 检测到页面关闭/刷新，有未保存内容，显示弹窗提示');

            // 阻止默认行为，显示确认对话框
            event.preventDefault();
            event.returnValue = '您有未保存的更改。确定要离开吗？';

            // 使用延迟检测用户选择
            setTimeout(() => {
                // 如果能执行到这里，说明用户选择了"取消"，自动保存
                console.log('🔧 用户选择取消离开，执行自动保存');
                performAutoSave();
            }, 100);

            return '您有未保存的更改。确定要离开吗？';
        }
    }, [enabled, shouldAutoSave, performAutoSave]);

    // 🔧 重构：笔记跳转处理 - 区分笔记ID变化
    const handleRouteChangeStart = useCallback(async (url: string) => {
        if (!enabled || isAutoSavingRef.current) return;

        if (shouldAutoSave()) {
            console.log('🔧 检测到路由跳转，有未保存内容', { targetUrl: url });

            // 检查是否是笔记ID变化（笔记跳转）
            const isNoteNavigation = url.match(/^\/[a-zA-Z0-9-]+$/) || url === '/';

            if (isNoteNavigation) {
                // 笔记跳转：直接自动保存
                console.log('🔧 笔记跳转，执行自动保存');
                isAutoSavingRef.current = true;

                // 阻止路由跳转
                router.events.emit('routeChangeError', new Error('Auto-saving before route change'), url);

                try {
                    const success = await performAutoSave();
                    isAutoSavingRef.current = false;

                    if (success) {
                        console.log('🔧 自动保存成功，继续跳转');
                        router.push(url);
                    } else {
                        console.log('🔧 自动保存失败，询问用户');
                        const confirmed = window.confirm('自动保存失败。是否强制离开？');
                        if (confirmed) {
                            router.push(url);
                        }
                    }
                } catch (error) {
                    console.error('🔧 自动保存出错:', error);
                    isAutoSavingRef.current = false;
                    const confirmed = window.confirm('自动保存出错。是否强制离开？');
                    if (confirmed) {
                        router.push(url);
                    }
                }
            } else {
                // 非笔记跳转：弹窗提示
                console.log('🔧 非笔记跳转，显示弹窗提示');
                router.events.emit('routeChangeError', new Error('User confirmation required'), url);

                const confirmed = window.confirm('您有未保存的更改。确定要离开吗？');
                if (confirmed) {
                    console.log('🔧 用户确认强制离开');
                    router.push(url);
                } else {
                    console.log('🔧 用户取消离开，执行自动保存');
                    await performAutoSave();
                }
            }
        }
    }, [enabled, shouldAutoSave, performAutoSave, router]);



    useEffect(() => {
        if (!enabled) return;

        window.addEventListener('beforeunload', handleBeforeUnload);

        return () => {
            window.removeEventListener('beforeunload', handleBeforeUnload);
        };
    }, [enabled, handleBeforeUnload]);

    useEffect(() => {
        if (!enabled) return;

        router.events.on('routeChangeStart', handleRouteChangeStart);
        return () => {
            router.events.off('routeChangeStart', handleRouteChangeStart);
        };
    }, [enabled, handleRouteChangeStart, router.events]);

    return {
        shouldAutoSave,
        performAutoSave,
    };
};

export default useAutoSaveOnLeave;
