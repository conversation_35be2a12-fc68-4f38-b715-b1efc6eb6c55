"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./components/editor/lexical-editor.tsx":
/*!**********************************************!*\
  !*** ./components/editor/lexical-editor.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @swc/helpers/src/_sliced_to_array.mjs */ \"./node_modules/@swc/helpers/src/_sliced_to_array.mjs\");\n/* harmony import */ var _swc_helpers_src_to_consumable_array_mjs__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @swc/helpers/src/_to_consumable_array.mjs */ \"./node_modules/@swc/helpers/src/_to_consumable_array.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! lexical */ \"./node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalComposer__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @lexical/react/LexicalComposer */ \"./node_modules/@lexical/react/LexicalComposer.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"./node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalRichTextPlugin__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @lexical/react/LexicalRichTextPlugin */ \"./node_modules/@lexical/react/LexicalRichTextPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalContentEditable__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @lexical/react/LexicalContentEditable */ \"./node_modules/@lexical/react/LexicalContentEditable.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalHistoryPlugin__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @lexical/react/LexicalHistoryPlugin */ \"./node_modules/@lexical/react/LexicalHistoryPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalAutoFocusPlugin__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @lexical/react/LexicalAutoFocusPlugin */ \"./node_modules/@lexical/react/LexicalAutoFocusPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalOnChangePlugin__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! @lexical/react/LexicalOnChangePlugin */ \"./node_modules/@lexical/react/LexicalOnChangePlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalLinkPlugin__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @lexical/react/LexicalLinkPlugin */ \"./node_modules/@lexical/react/LexicalLinkPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalListPlugin__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @lexical/react/LexicalListPlugin */ \"./node_modules/@lexical/react/LexicalListPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalMarkdownShortcutPlugin__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @lexical/react/LexicalMarkdownShortcutPlugin */ \"./node_modules/@lexical/react/LexicalMarkdownShortcutPlugin.dev.mjs\");\n/* harmony import */ var _lexical_markdown__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @lexical/markdown */ \"./node_modules/@lexical/markdown/LexicalMarkdown.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalErrorBoundary__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @lexical/react/LexicalErrorBoundary */ \"./node_modules/@lexical/react/LexicalErrorBoundary.dev.mjs\");\n/* harmony import */ var _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @lexical/rich-text */ \"./node_modules/@lexical/rich-text/LexicalRichText.dev.mjs\");\n/* harmony import */ var _lexical_list__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @lexical/list */ \"./node_modules/@lexical/list/LexicalList.dev.mjs\");\n/* harmony import */ var _lexical_code__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @lexical/code */ \"./node_modules/@lexical/code/LexicalCode.dev.mjs\");\n/* harmony import */ var _lexical_link__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @lexical/link */ \"./node_modules/@lexical/link/LexicalLink.dev.mjs\");\n/* harmony import */ var react_div_100vh__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-div-100vh */ \"./node_modules/react-div-100vh/dist/esm/index.js\");\n/* harmony import */ var libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! libs/web/hooks/use-mounted */ \"./libs/web/hooks/use-mounted.ts\");\n/* harmony import */ var libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! libs/web/hooks/use-i18n */ \"./libs/web/hooks/use-i18n.tsx\");\n/* harmony import */ var _plugins_slash_commands_plugin__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./plugins/slash-commands-plugin */ \"./components/editor/plugins/slash-commands-plugin.tsx\");\n/* harmony import */ var _plugins_floating_toolbar_plugin__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./plugins/floating-toolbar-plugin */ \"./components/editor/plugins/floating-toolbar-plugin.tsx\");\n/* harmony import */ var _lexical_react_LexicalCheckListPlugin__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @lexical/react/LexicalCheckListPlugin */ \"./node_modules/@lexical/react/LexicalCheckListPlugin.dev.mjs\");\n/* harmony import */ var _plugins_highlight_plugin__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./plugins/highlight-plugin */ \"./components/editor/plugins/highlight-plugin.tsx\");\n/* harmony import */ var _lexical_react_LexicalTabIndentationPlugin__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @lexical/react/LexicalTabIndentationPlugin */ \"./node_modules/@lexical/react/LexicalTabIndentationPlugin.dev.mjs\");\n/* harmony import */ var _plugins_image_plugin__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./plugins/image-plugin */ \"./components/editor/plugins/image-plugin.tsx\");\n/* harmony import */ var _plugins_ime_plugin__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./plugins/ime-plugin */ \"./components/editor/plugins/ime-plugin.tsx\");\n/* harmony import */ var _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./nodes/image-node */ \"./components/editor/nodes/image-node.tsx\");\n/* harmony import */ var _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @lexical/react/LexicalHorizontalRuleNode */ \"./node_modules/@lexical/react/LexicalHorizontalRuleNode.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalHorizontalRulePlugin__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @lexical/react/LexicalHorizontalRulePlugin */ \"./node_modules/@lexical/react/LexicalHorizontalRulePlugin.dev.mjs\");\n/**\n * Lexical Editor Component\n * Migrated from TipTap to Lexical for better performance and modern architecture\n */ \n\nvar _this = undefined;\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Import custom plugins and nodes\n\n\n\n\n\n\n\n\n\n\nvar theme = {\n    ltr: \"ltr\",\n    rtl: \"rtl\",\n    placeholder: \"editor-placeholder\",\n    paragraph: \"editor-paragraph\",\n    quote: \"editor-quote\",\n    heading: {\n        h1: \"editor-heading-h1\",\n        h2: \"editor-heading-h2\",\n        h3: \"editor-heading-h3\",\n        h4: \"editor-heading-h4\",\n        h5: \"editor-heading-h5\",\n        h6: \"editor-heading-h6\"\n    },\n    list: {\n        nested: {\n            listitem: \"editor-nested-listitem\"\n        },\n        ol: \"editor-list-ol\",\n        ul: \"editor-list-ul\",\n        listitem: \"editor-listitem\",\n        checklist: \"PlaygroundEditorTheme__checklist\",\n        listitemChecked: \"PlaygroundEditorTheme__listItemChecked\",\n        listitemUnchecked: \"PlaygroundEditorTheme__listItemUnchecked\"\n    },\n    // Lexical 0.32.1 原生缩进支持\n    indent: \"lexical-indent\",\n    image: \"editor-image\",\n    link: \"editor-link\",\n    text: {\n        bold: \"editor-text-bold\",\n        italic: \"editor-text-italic\",\n        overflowed: \"editor-text-overflowed\",\n        hashtag: \"editor-text-hashtag\",\n        underline: \"editor-text-underline\",\n        strikethrough: \"editor-text-strikethrough\",\n        underlineStrikethrough: \"editor-text-underlineStrikethrough\",\n        code: \"editor-text-code\",\n        highlight: \"editor-text-highlight\"\n    },\n    code: \"editor-code\",\n    codeHighlight: {\n        atrule: \"editor-tokenAttr\",\n        attr: \"editor-tokenAttr\",\n        boolean: \"editor-tokenProperty\",\n        builtin: \"editor-tokenSelector\",\n        cdata: \"editor-tokenComment\",\n        char: \"editor-tokenSelector\",\n        class: \"editor-tokenFunction\",\n        \"class-name\": \"editor-tokenFunction\",\n        comment: \"editor-tokenComment\",\n        constant: \"editor-tokenProperty\",\n        deleted: \"editor-tokenProperty\",\n        doctype: \"editor-tokenComment\",\n        entity: \"editor-tokenOperator\",\n        function: \"editor-tokenFunction\",\n        important: \"editor-tokenVariable\",\n        inserted: \"editor-tokenSelector\",\n        keyword: \"editor-tokenAttr\",\n        namespace: \"editor-tokenVariable\",\n        number: \"editor-tokenProperty\",\n        operator: \"editor-tokenOperator\",\n        prolog: \"editor-tokenComment\",\n        property: \"editor-tokenProperty\",\n        punctuation: \"editor-tokenPunctuation\",\n        regex: \"editor-tokenVariable\",\n        selector: \"editor-tokenSelector\",\n        string: \"editor-tokenSelector\",\n        symbol: \"editor-tokenProperty\",\n        tag: \"editor-tokenProperty\",\n        url: \"editor-tokenOperator\",\n        variable: \"editor-tokenVariable\"\n    }\n};\nfunction Placeholder() {\n    _s();\n    var t = (0,libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__[\"default\"])().t;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"editor-placeholder\",\n        children: t(\"Start writing...\")\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n        lineNumber: 138,\n        columnNumber: 12\n    }, this);\n}\n_s(Placeholder, \"82N5KF9nLzZ6+2WH7KIjzIXRkLw=\", false, function() {\n    return [\n        libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    ];\n});\n_c = Placeholder;\nvar LexicalEditor = /*#__PURE__*/ _s1((0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(_c1 = _s1(function(param, ref) {\n    var _readOnly = param.readOnly, readOnly = _readOnly === void 0 ? false : _readOnly, _value = param.value, value = _value === void 0 ? \"\" : _value, onChange = param.onChange, onClickLink = param.onClickLink, onHoverLink = param.onHoverLink, _className = param.className, className = _className === void 0 ? \"\" : _className;\n    _s1();\n    var _s = $RefreshSig$(), _s2 = $RefreshSig$();\n    var height = (0,react_div_100vh__WEBPACK_IMPORTED_MODULE_4__.use100vh)();\n    var mounted = (0,libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    var initialConfig = {\n        namespace: \"LexicalEditor\",\n        theme: theme,\n        onError: function onError(error) {\n            console.error(\"Lexical Error:\", error);\n        },\n        nodes: [\n            _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.HeadingNode,\n            _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListNode,\n            _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListItemNode,\n            _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.QuoteNode,\n            _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeNode,\n            _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeHighlightNode,\n            _lexical_link__WEBPACK_IMPORTED_MODULE_15__.AutoLinkNode,\n            _lexical_link__WEBPACK_IMPORTED_MODULE_15__.LinkNode,\n            // Lexical原生的ListItemNode已经支持checkbox功能\n            // 使用Lexical内置的highlight格式，不需要自定义HighlightNode\n            _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.ImageNode,\n            _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.HorizontalRuleNode, \n        ],\n        editable: !readOnly,\n        // 设置初始内容\n        editorState: value ? undefined : null\n    };\n    // 创建自定义transformers，包含图片支持\n    var IMAGE_TRANSFORMER = {\n        dependencies: [\n            _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.ImageNode\n        ],\n        export: function(node) {\n            if (!(0,_nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.$isImageNode)(node)) {\n                return null;\n            }\n            return \"![\".concat(node.getAltText(), \"](\").concat(node.getSrc(), \")\");\n        },\n        regExp: /!\\[([^\\]]*)\\]\\(([^)]+)\\)/,\n        replace: function(parentNode, children, match) {\n            var _match = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(match, 3), altText = _match[1], src = _match[2];\n            var imageNode = (0,_nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.$createImageNode)({\n                altText: altText,\n                src: src,\n                maxWidth: 800\n            });\n            children.forEach(function(child) {\n                return child.remove();\n            });\n            parentNode.append(imageNode);\n        },\n        type: \"element\"\n    };\n    // 创建自定义的下划线转换器，使用 <u>text</u> 语法\n    var UNDERLINE_TRANSFORMER = {\n        format: [\n            \"underline\"\n        ],\n        tag: \"<u>\",\n        type: \"text-format\"\n    };\n    // 创建段落缩进转换器 - 使用HTML注释来保存缩进信息\n    var PARAGRAPH_INDENT_TRANSFORMER = {\n        dependencies: [\n            lexical__WEBPACK_IMPORTED_MODULE_18__.ParagraphNode\n        ],\n        export: function(node, traverseChildren) {\n            if (!(0,lexical__WEBPACK_IMPORTED_MODULE_18__.$isParagraphNode)(node)) {\n                return null;\n            }\n            var indent = node.getIndent();\n            var content = traverseChildren(node);\n            if (indent > 0) {\n                // 使用HTML注释保存缩进信息，这样在markdown中不可见但能保存数据\n                return \"<!-- indent:\".concat(indent, \" -->\").concat(content);\n            }\n            return content;\n        },\n        regExp: /^<!-- indent:(\\d+) -->(.*)$/,\n        replace: function(parentNode, children, match, isImport) {\n            if (!isImport) return;\n            var indentLevel = parseInt(match[1], 10);\n            var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_18__.$createParagraphNode)();\n            if (indentLevel > 0) {\n                paragraph.setIndent(indentLevel);\n            }\n            // 解析内容并添加到段落中\n            if (match[2].trim()) {\n                var textNode = (0,lexical__WEBPACK_IMPORTED_MODULE_18__.$createTextNode)(match[2]);\n                paragraph.append(textNode);\n            }\n            parentNode.replace(paragraph);\n        },\n        type: \"element\"\n    };\n    // 创建水平分割线转换器\n    var HR_TRANSFORMER = {\n        dependencies: [\n            _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.HorizontalRuleNode\n        ],\n        export: function(node) {\n            return (0,_lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.$isHorizontalRuleNode)(node) ? \"---\" : null;\n        },\n        regExp: /^(---|\\*\\*\\*|___)\\s?$/,\n        replace: function(parentNode, children, match, isImport) {\n            var line = (0,_lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.$createHorizontalRuleNode)();\n            if (isImport || parentNode.getNextSibling() != null) {\n                parentNode.replace(line);\n            } else {\n                parentNode.insertBefore(line);\n            }\n            line.selectNext();\n        },\n        type: \"element\"\n    };\n    // 重新排序transformers，确保CHECK_LIST优先级高于UNORDERED_LIST\n    var customTransformers = [\n        // 首先是CHECK_LIST，确保checkbox优先匹配\n        _lexical_markdown__WEBPACK_IMPORTED_MODULE_19__.CHECK_LIST\n    ].concat(// 然后是其他TRANSFORMERS（但要排除重复的CHECK_LIST）\n    (0,_swc_helpers_src_to_consumable_array_mjs__WEBPACK_IMPORTED_MODULE_20__[\"default\"])(_lexical_markdown__WEBPACK_IMPORTED_MODULE_19__.TRANSFORMERS.filter(function(t) {\n        return t !== _lexical_markdown__WEBPACK_IMPORTED_MODULE_19__.CHECK_LIST;\n    })), [\n        // 最后是自定义的转换器\n        HR_TRANSFORMER,\n        UNDERLINE_TRANSFORMER,\n        IMAGE_TRANSFORMER\n    ]);\n    // 移除了自动完成插件 - 现在通过正确的transformer优先级来解决问题\n    var handleChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(editorState, _editor, tags) {\n        if (onChange) {\n            // 模仿TipTap的简单检查：忽略历史合并和内容同步触发的更新\n            if (tags.has(\"history-merge\") || tags.has(\"content-sync\")) {\n                return;\n            }\n            editorState.read(function() {\n                try {\n                    // 使用Lexical的官方transformers进行markdown转换\n                    var markdownContent = (0,_lexical_markdown__WEBPACK_IMPORTED_MODULE_19__.$convertToMarkdownString)(customTransformers);\n                    // 调试：检查是否包含checkbox语法\n                    if (markdownContent.includes(\"[ ]\") || markdownContent.includes(\"[x]\")) {\n                        console.log(\"\\uD83D\\uDD0D Checkbox detected in markdown\");\n                    }\n                    // 不做任何额外处理，保持Lexical原生的markdown输出\n                    // Lexical的transformers已经正确处理了列表、checkbox等格式\n                    // 简单的内容变化检查\n                    if (markdownContent !== value) {\n                        console.log(\"\\uD83D\\uDD0D Content changed, calling onChange\");\n                        onChange(function() {\n                            return markdownContent;\n                        });\n                    }\n                } catch (error) {\n                    console.error(\"\\uD83D\\uDD0D Error in markdown conversion:\", error);\n                // 如果转换出错，保持原有内容不变\n                }\n            });\n        }\n    }, [\n        onChange,\n        value\n    ]);\n    // 列表退出处理插件 - 处理Enter+Enter退出列表的逻辑\n    var ListExitPlugin = function() {\n        _s();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_17__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            return editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_18__.KEY_ENTER_COMMAND, function(event) {\n                var selection = (0,lexical__WEBPACK_IMPORTED_MODULE_18__.$getSelection)();\n                if (!(0,lexical__WEBPACK_IMPORTED_MODULE_18__.$isRangeSelection)(selection)) {\n                    return false;\n                }\n                var anchorNode = selection.anchor.getNode();\n                // 检查是否在空的列表项中\n                if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListItemNode)(anchorNode)) {\n                    var textContent = anchorNode.getTextContent().trim();\n                    if (textContent === \"\") {\n                        var listNode = anchorNode.getParent();\n                        if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListNode)(listNode)) {\n                            // 如果是空的列表项，退出列表\n                            event === null || event === void 0 ? void 0 : event.preventDefault();\n                            // 创建新段落并在列表后插入\n                            var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_18__.$createParagraphNode)();\n                            listNode.insertAfter(paragraph);\n                            // 删除空的列表项\n                            anchorNode.remove();\n                            // 选中新段落\n                            paragraph.select();\n                            return true;\n                        }\n                    }\n                }\n                return false;\n            }, lexical__WEBPACK_IMPORTED_MODULE_18__.COMMAND_PRIORITY_HIGH);\n        }, [\n            editor\n        ]);\n        return null;\n    };\n    _s(ListExitPlugin, \"mCqe7sh4aC9mLBXPHfG3d/PNTaQ=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext\n        ];\n    });\n    // 内容同步组件 - 模仿TipTap的方式\n    var ContentSyncPlugin = function() {\n        _s2();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_17__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            if (editor && value !== undefined && mounted) {\n                editor.getEditorState().read(function() {\n                    var root = (0,lexical__WEBPACK_IMPORTED_MODULE_18__.$getRoot)();\n                    var currentContent = root.getTextContent();\n                    // 只有当内容真的不同时才更新\n                    if (value !== currentContent) {\n                        editor.update(function() {\n                            // 使用Lexical官方的markdown解析器来正确渲染markdown内容\n                            if (value.trim()) {\n                                // 不要清理双换行符！保持原始markdown格式\n                                // 双换行符在markdown中有重要意义（段落分隔、列表退出等）\n                                // 使用自定义的transformers解析内容\n                                (0,_lexical_markdown__WEBPACK_IMPORTED_MODULE_19__.$convertFromMarkdownString)(value, customTransformers);\n                            } else {\n                                // 空内容时清空并创建一个空段落\n                                var root = (0,lexical__WEBPACK_IMPORTED_MODULE_18__.$getRoot)();\n                                root.clear();\n                                var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_18__.$createParagraphNode)();\n                                root.append(paragraph);\n                            }\n                        }, {\n                            tag: \"content-sync\"\n                        });\n                    }\n                });\n            }\n        }, [\n            editor,\n            value,\n            mounted\n        ]);\n        return null;\n    };\n    _s2(ContentSyncPlugin, \"mCqe7sh4aC9mLBXPHfG3d/PNTaQ=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext\n        ];\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useImperativeHandle)(ref, function() {\n        return {\n            focusAtEnd: function() {\n            // TODO: Implement focus at end\n            },\n            focusAtStart: function() {\n            // TODO: Implement focus at start\n            }\n        };\n    });\n    if (!mounted) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n            [\n                \"ea89f497a3ec74e8\",\n                [\n                    height ? height + \"px\" : \"100vh\"\n                ]\n            ]\n        ]) + \" \" + \"lexical-editor \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalComposer__WEBPACK_IMPORTED_MODULE_22__.LexicalComposer, {\n                initialConfig: initialConfig,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                        [\n                            \"ea89f497a3ec74e8\",\n                            [\n                                height ? height + \"px\" : \"100vh\"\n                            ]\n                        ]\n                    ]) + \" \" + \"editor-container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalRichTextPlugin__WEBPACK_IMPORTED_MODULE_23__.RichTextPlugin, {\n                            contentEditable: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalContentEditable__WEBPACK_IMPORTED_MODULE_24__.ContentEditable, {\n                                className: \"editor-input focus:outline-none w-full\",\n                                spellCheck: false\n                            }, void 0, false, void 0, void 0),\n                            placeholder: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Placeholder, {}, void 0, false, void 0, void 0),\n                            ErrorBoundary: _lexical_react_LexicalErrorBoundary__WEBPACK_IMPORTED_MODULE_25__.LexicalErrorBoundary\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 416,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalHistoryPlugin__WEBPACK_IMPORTED_MODULE_26__.HistoryPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 426,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalAutoFocusPlugin__WEBPACK_IMPORTED_MODULE_27__.AutoFocusPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 427,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalLinkPlugin__WEBPACK_IMPORTED_MODULE_28__.LinkPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 428,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalListPlugin__WEBPACK_IMPORTED_MODULE_29__.ListPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 429,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalMarkdownShortcutPlugin__WEBPACK_IMPORTED_MODULE_30__.MarkdownShortcutPlugin, {\n                            transformers: customTransformers\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 430,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_slash_commands_plugin__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 431,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_floating_toolbar_plugin__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 432,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalCheckListPlugin__WEBPACK_IMPORTED_MODULE_31__.CheckListPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 433,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_highlight_plugin__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 434,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalTabIndentationPlugin__WEBPACK_IMPORTED_MODULE_32__.TabIndentationPlugin, {\n                            maxIndent: 10\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 435,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_image_plugin__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 436,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalHorizontalRulePlugin__WEBPACK_IMPORTED_MODULE_33__.HorizontalRulePlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 437,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_ime_plugin__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            enabled: true,\n                            debug: \"development\" === \"development\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 438,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ListExitPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"ea89f497a3ec74e8\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentSyncPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"ea89f497a3ec74e8\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 443,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalOnChangePlugin__WEBPACK_IMPORTED_MODULE_34__.OnChangePlugin, {\n                            onChange: handleChange,\n                            ignoreHistoryMergeTagChange: true,\n                            ignoreSelectionChange: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 444,\n                            columnNumber: 21\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                    lineNumber: 415,\n                    columnNumber: 17\n                }, _this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                lineNumber: 414,\n                columnNumber: 13\n            }, _this),\n            (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"ea89f497a3ec74e8\",\n                dynamic: [\n                    height ? height + \"px\" : \"100vh\"\n                ],\n                children: \".lexical-editor{position:relative}.editor-container{position:relative}.editor-input{outline:none;padding:1rem 0;min-height:-webkit-calc(\".concat(height ? height + \"px\" : \"100vh\", \" - 14rem);min-height:-moz-calc(\").concat(height ? height + \"px\" : \"100vh\", \" - 14rem);min-height:calc(\").concat(height ? height + \"px\" : \"100vh\", ' - 14rem);padding-bottom:10rem;width:100%;max-width:none;line-height:1.7;font-size:1rem;color:inherit;-webkit-spellcheck:false;-moz-spellcheck:false;-ms-spellcheck:false;spellcheck:false}.lexical-editor{--lexical-indent-base-value:32px}.lexical-indent{--lexical-indent-base-value:32px}.editor-input p[style*=\"margin-left\"],.editor-input h1[style*=\"margin-left\"],.editor-input h2[style*=\"margin-left\"],.editor-input h3[style*=\"margin-left\"],.editor-input h4[style*=\"margin-left\"],.editor-input h5[style*=\"margin-left\"],.editor-input h6[style*=\"margin-left\"],.editor-input li[style*=\"margin-left\"]{-webkit-transition:margin-left.2s ease;-moz-transition:margin-left.2s ease;-o-transition:margin-left.2s ease;transition:margin-left.2s ease}.editor-placeholder{color:#999;overflow:hidden;position:absolute;-o-text-overflow:ellipsis;text-overflow:ellipsis;top:1rem;left:0;font-size:1rem;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;display:inline-block;pointer-events:none}.editor-paragraph{margin:1rem 0;line-height:1.7}.editor-heading-h1{font-size:2.8em;font-weight:bold;margin:1.5rem 0 1rem 0;line-height:1.2}.editor-heading-h2{font-size:2.2em;font-weight:bold;margin:1.4rem 0 .8rem 0;line-height:1.3}.editor-heading-h3{font-size:1.8em;font-weight:bold;margin:1.3rem 0 .6rem 0;line-height:1.4}.editor-heading-h4{font-size:1.5em;font-weight:bold;margin:1.2rem 0 .5rem 0;line-height:1.4}.editor-heading-h5{font-size:1.3em;font-weight:bold;margin:1.1rem 0 .4rem 0;line-height:1.5}.editor-heading-h6{font-size:1.2em;font-weight:bold;margin:1rem 0 .3rem 0;line-height:1.5}.editor-quote{margin:1rem 0;padding-left:1rem;border-left:4px solid#ccc;font-style:italic;color:#666}.editor-list-ol,.editor-list-ul{margin:1rem 0;padding-left:2rem}.editor-listitem{margin:.5rem 0}.editor-link{color:#3b82f6;text-decoration:underline;cursor:pointer}.editor-link:hover{color:#1d4ed8}.editor-text-bold{font-weight:bold}.editor-text-italic{font-style:italic}.editor-text-underline{text-decoration:underline}.editor-text-strikethrough{text-decoration:line-through}.editor-text-code{background-color:#e4e4e7;color:black;padding:.2rem .4rem;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;font-family:monospace;font-size:.9em}.editor-code{background-color:#e4e4e7;color:black;border:1px solid#e9ecef;-webkit-border-radius:.375rem;-moz-border-radius:.375rem;border-radius:.375rem;padding:1rem;margin:1rem 0;font-family:\"Courier New\",Courier,monospace;font-size:.9em;line-height:1.4;overflow-x:auto}[data-theme=\"dark\"] .editor-text-code{background-color:#3f3f46;color:white}[data-theme=\"dark\"] .editor-code{background-color:#3f3f46;color:white;border-color:#4b5563}.editor-container ul{list-style-type:disc;padding-left:1.5rem;margin:.5rem 0}.editor-container ol{list-style-type:decimal;padding-left:1.5rem;margin:.5rem 0}.editor-container li{margin:.25rem 0;line-height:1.6;padding-left:.25rem}.editor-container ul ul{list-style:none;margin:.25rem 0;padding-left:1.5rem;counter-reset:nested-list}.editor-container ul ul li{counter-increment:nested-list;position:relative;margin-left:0}.editor-container ul ul li::before{content:counter(nested-list,upper-alpha)\". \";position:absolute;left:-1.5rem;font-weight:normal;color:inherit}.editor-container ul ul ul{list-style:none;margin:.25rem 0;padding-left:1.5rem;counter-reset:nested-list-3}.editor-container ul ul ul li{counter-increment:nested-list-3}.editor-container ul ul ul li::before{content:counter(nested-list-3,lower-roman)\". \";left:-1.5rem}.editor-container ol ol{list-style:none;margin:.25rem 0;padding-left:1.5rem;counter-reset:nested-list}.editor-container ol ol li{counter-increment:nested-list;position:relative;margin-left:0}.editor-container ol ol li::before{content:counter(nested-list,upper-alpha)\". \";position:absolute;left:-1.5rem;font-weight:normal;color:inherit}.editor-container ol ol ol{list-style:none;margin:.25rem 0;padding-left:1.5rem;counter-reset:nested-list-3}.editor-container ol ol ol li{counter-increment:nested-list-3}.editor-container ol ol ol li::before{content:counter(nested-list-3,lower-roman)\". \";left:-1.5rem}.task-list{list-style:none;padding-left:0;margin:1rem 0}.task-item{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:start;-webkit-align-items:flex-start;-moz-box-align:start;-ms-flex-align:start;align-items:flex-start;margin:.5rem 0;list-style:none}.task-checkbox{margin-right:.5rem;margin-top:.125rem;cursor:pointer;width:1rem;height:1rem;border:1px solid#d1d5db;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;background:white;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;-moz-appearance:none;-ms-appearance:none;appearance:none;-webkit-appearance:none}.task-checkbox:checked{background-color:#3b82f6;border-color:#3b82f6;color:white}.task-checkbox:checked::after{content:\"✓\";font-size:.75rem;font-weight:bold;color:white}.task-content{-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1;line-height:1.7}.task-item[data-checked=\"true\"] .task-content{text-decoration:line-through;opacity:.6}.lexical-highlight,mark,.editor-text-highlight{background-color:#eab834!important;color:black!important;padding:.1rem .2rem;-webkit-border-radius:.125rem;-moz-border-radius:.125rem;border-radius:.125rem}.dark .lexical-highlight,.dark mark,.dark .editor-text-highlight,[data-theme=\"dark\"] .lexical-highlight,[data-theme=\"dark\"] mark,[data-theme=\"dark\"] .editor-text-highlight,html.dark .lexical-highlight,html.dark mark,html.dark .editor-text-highlight{background-color:#3185eb!important;color:white!important}.editor-image img{max-width:100%;height:auto;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;margin:1rem 0}@media(prefers-color-scheme:dark){.editor-placeholder{color:#6b7280}.editor-quote{border-left-color:#4b5563;color:#9ca3af}.editor-text-code{background-color:#374151;color:#f9fafb}.editor-code{background-color:#1f2937;border-color:#374151;color:#f9fafb}}.lexical-editor ul:not([data-lexical-list-type=\"check\"]) li::marker{color:#6b7280}.lexical-editor ol li::marker{color:#6b7280}.lexical-editor .PlaygroundEditorTheme__listItemChecked,.lexical-editor .PlaygroundEditorTheme__listItemUnchecked{position:relative;margin-left:.5em;margin-right:.5em;padding-left:1.5em;padding-right:1.5em;list-style-type:none;outline:none;display:block;min-height:1.5em}.lexical-editor .PlaygroundEditorTheme__listItemChecked>*,.lexical-editor .PlaygroundEditorTheme__listItemUnchecked>*{margin-left:.01em}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before,.lexical-editor .PlaygroundEditorTheme__listItemChecked:before{content:\"\";width:.9em;height:.9em;top:50%;left:0;cursor:pointer;display:block;-webkit-background-size:cover;-moz-background-size:cover;-o-background-size:cover;background-size:cover;position:absolute;-webkit-transform:translateY(-50%);-moz-transform:translateY(-50%);-ms-transform:translateY(-50%);-o-transform:translateY(-50%);transform:translateY(-50%)}.lexical-editor .PlaygroundEditorTheme__listItemChecked{text-decoration:line-through}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:focus:before,.lexical-editor .PlaygroundEditorTheme__listItemChecked:focus:before{-webkit-box-shadow:0 0 0 2px#a6cdfe;-moz-box-shadow:0 0 0 2px#a6cdfe;box-shadow:0 0 0 2px#a6cdfe;-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before{border:1px solid#999;-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px}.lexical-editor .PlaygroundEditorTheme__listItemChecked:before{border:1px solid rgb(61,135,245);-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px;background-color:#3d87f5;background-repeat:no-repeat}.lexical-editor .PlaygroundEditorTheme__listItemChecked:after{content:\"\";cursor:pointer;border-color:#fff;border-style:solid;position:absolute;display:block;top:45%;width:.2em;left:.35em;height:.4em;-webkit-transform:translateY(-50%)rotate(45deg);-moz-transform:translateY(-50%)rotate(45deg);-ms-transform:translateY(-50%)rotate(45deg);-o-transform:translateY(-50%)rotate(45deg);transform:translateY(-50%)rotate(45deg);border-width:0 .1em .1em 0}')\n            }, void 0, false, void 0, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n        lineNumber: 413,\n        columnNumber: 9\n    }, _this);\n}, \"cp2mgoBaCDop5vv5Ezf6hVUYfiE=\", false, function() {\n    return [\n        libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n})), \"cp2mgoBaCDop5vv5Ezf6hVUYfiE=\", false, function() {\n    return [\n        libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n});\n_c2 = LexicalEditor;\nLexicalEditor.displayName = \"LexicalEditor\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (LexicalEditor);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Placeholder\");\n$RefreshReg$(_c1, \"LexicalEditor$forwardRef\");\n$RefreshReg$(_c2, \"LexicalEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/editor/lexical-editor.tsx\n"));

/***/ })

});