"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./components/editor/plugins/floating-toolbar-plugin.tsx":
/*!***************************************************************!*\
  !*** ./components/editor/plugins/floating-toolbar-plugin.tsx ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FloatingToolbarPlugin; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/src/_sliced_to_array.mjs */ \"./node_modules/@swc/helpers/src/_sliced_to_array.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"./node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lexical */ \"./node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var _lexical_link__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @lexical/link */ \"./node_modules/@lexical/link/LexicalLink.dev.mjs\");\n/* harmony import */ var _lexical_list__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @lexical/list */ \"./node_modules/@lexical/list/LexicalList.dev.mjs\");\n/* harmony import */ var _highlight_plugin__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./highlight-plugin */ \"./components/editor/plugins/highlight-plugin.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-dom */ \"./node_modules/react-dom/index.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-themes */ \"./node_modules/next-themes/dist/index.modern.js\");\n/* harmony import */ var _heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @heroicons/react/outline */ \"./node_modules/@heroicons/react/outline/esm/index.js\");\n/**\n * Floating Toolbar Plugin for Lexical\n * Shows formatting options when text is selected\n */ \n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Heroicons\n\nfunction FloatingToolbarPlugin() {\n    var _this = this;\n    _s();\n    var ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_5__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_6__.useLexicalComposerContext)(), 1), editor = ref[0];\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isVisible = ref1[0], setIsVisible = ref1[1];\n    var ref2 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        top: 0,\n        left: 0\n    }), position = ref2[0], setPosition = ref2[1];\n    var ref3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isBold = ref3[0], setIsBold = ref3[1];\n    var ref4 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isUnderline = ref4[0], setIsUnderline = ref4[1];\n    var ref5 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isStrikethrough = ref5[0], setIsStrikethrough = ref5[1];\n    var ref6 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isCode = ref6[0], setIsCode = ref6[1];\n    var ref7 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isLink = ref7[0], setIsLink = ref7[1];\n    var ref8 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isHighlight = ref8[0], setIsHighlight = ref8[1];\n    var theme = (0,next_themes__WEBPACK_IMPORTED_MODULE_4__.useTheme)().theme;\n    var updateToolbar = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var selection = (0,lexical__WEBPACK_IMPORTED_MODULE_7__.$getSelection)();\n        if ((0,lexical__WEBPACK_IMPORTED_MODULE_7__.$isRangeSelection)(selection)) {\n            var anchorNode = selection.anchor.getNode();\n            var element = anchorNode.getKey() === \"root\" ? anchorNode : anchorNode.getTopLevelElementOrThrow();\n            var elementKey = element.getKey();\n            var elementDOM = editor.getElementByKey(elementKey);\n            // 显示条件：只有当有选中文本时才显示\n            var hasSelection = selection.getTextContent() !== \"\";\n            if (elementDOM !== null && hasSelection) {\n                var nativeSelection = window.getSelection();\n                var rootElement = editor.getRootElement();\n                if (nativeSelection !== null && rootElement !== null && rootElement.contains(nativeSelection.anchorNode)) {\n                    var rangeRect = nativeSelection.getRangeAt(0).getBoundingClientRect();\n                    setPosition({\n                        top: rangeRect.top - 60,\n                        left: rangeRect.left + rangeRect.width / 2 - 150\n                    });\n                    setIsVisible(true);\n                    // Update button states\n                    setIsBold(selection.hasFormat(\"bold\"));\n                    setIsUnderline(selection.hasFormat(\"underline\"));\n                    setIsStrikethrough(selection.hasFormat(\"strikethrough\"));\n                    setIsCode(selection.hasFormat(\"code\"));\n                    // Check if selection contains a link\n                    var node = selection.anchor.getNode();\n                    var parent = node.getParent();\n                    setIsLink((0,_lexical_link__WEBPACK_IMPORTED_MODULE_8__.$isLinkNode)(parent) || (0,_lexical_link__WEBPACK_IMPORTED_MODULE_8__.$isLinkNode)(node));\n                    // Check for highlight using Lexical's built-in format\n                    setIsHighlight(selection.hasFormat(\"highlight\"));\n                } else {\n                    setIsVisible(false);\n                }\n            } else {\n                setIsVisible(false);\n            }\n        } else {\n            setIsVisible(false);\n        }\n    }, [\n        editor\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n        return editor.registerUpdateListener(function(param) {\n            var editorState = param.editorState;\n            editorState.read(function() {\n                updateToolbar();\n            });\n        });\n    }, [\n        editor,\n        updateToolbar\n    ]);\n    var handleFormat = function(format) {\n        editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_7__.FORMAT_TEXT_COMMAND, format);\n    };\n    var handleLink = function() {\n        if (isLink) {\n            editor.dispatchCommand(_lexical_link__WEBPACK_IMPORTED_MODULE_8__.TOGGLE_LINK_COMMAND, null);\n        } else {\n            var url = prompt(\"Enter URL:\");\n            if (url) {\n                editor.dispatchCommand(_lexical_link__WEBPACK_IMPORTED_MODULE_8__.TOGGLE_LINK_COMMAND, url);\n            }\n        }\n    };\n    if (!isVisible) {\n        return null;\n    }\n    var toolbarBg = theme === \"dark\" ? \"border-gray-600\" : \"border-gray-200\";\n    var buttonText = theme === \"dark\" ? \"text-white\" : \"text-gray-700\";\n    var buttonHover = theme === \"dark\" ? \"hover:text-white\" : \"hover:text-gray-900\";\n    var buttonActive = theme === \"dark\" ? \"text-white\" : \"text-gray-900\";\n    var toolbarButtons = [\n        {\n            title: \"Bold\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"font-bold text-sm\",\n                children: \"B\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 139,\n                columnNumber: 19\n            }, this),\n            isActive: isBold,\n            action: function() {\n                return handleFormat(\"bold\");\n            }\n        },\n        {\n            title: \"Strikethrough\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"line-through text-sm\",\n                children: \"S\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 145,\n                columnNumber: 19\n            }, this),\n            isActive: isStrikethrough,\n            action: function() {\n                return handleFormat(\"strikethrough\");\n            }\n        },\n        {\n            title: \"Underline\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"underline text-sm\",\n                children: \"U\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 151,\n                columnNumber: 19\n            }, this),\n            isActive: isUnderline,\n            action: function() {\n                return handleFormat(\"underline\");\n            }\n        },\n        {\n            title: \"Highlight\",\n            icon: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-xs px-1 rounded text-white\",\n                style: {\n                    backgroundColor: \"#3185eb\"\n                },\n                children: \"H\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 158,\n                columnNumber: 19\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-xs px-1 rounded\",\n                style: {\n                    backgroundColor: \"#eab834\"\n                },\n                children: \"H\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 159,\n                columnNumber: 19\n            }, this),\n            isActive: isHighlight,\n            action: function() {\n                return editor.dispatchCommand(_highlight_plugin__WEBPACK_IMPORTED_MODULE_1__.TOGGLE_HIGHLIGHT_COMMAND, undefined);\n            }\n        },\n        {\n            title: \"Code\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__.CodeIcon, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 165,\n                columnNumber: 19\n            }, this),\n            isActive: isCode,\n            action: function() {\n                return handleFormat(\"code\");\n            }\n        },\n        {\n            title: isLink ? \"Remove Link\" : \"Add Link\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__.LinkIcon, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 171,\n                columnNumber: 19\n            }, this),\n            isActive: isLink,\n            action: handleLink\n        },\n        // 分隔符\n        {\n            type: \"separator\"\n        },\n        {\n            title: \"Checklist\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm\",\n                children: \"☑\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 179,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: function() {\n                return editor.dispatchCommand(_lexical_list__WEBPACK_IMPORTED_MODULE_10__.INSERT_CHECK_LIST_COMMAND, undefined);\n            }\n        },\n        {\n            title: \"Bullet List\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm\",\n                children: \"•\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 185,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: function() {\n                return editor.dispatchCommand(_lexical_list__WEBPACK_IMPORTED_MODULE_10__.INSERT_UNORDERED_LIST_COMMAND, undefined);\n            }\n        },\n        {\n            title: \"Numbered List\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm\",\n                children: \"1.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 191,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: function() {\n                return editor.dispatchCommand(_lexical_list__WEBPACK_IMPORTED_MODULE_10__.INSERT_ORDERED_LIST_COMMAND, undefined);\n            }\n        },\n        // 分隔符\n        {\n            type: \"separator\"\n        },\n        {\n            title: \"Indent\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__.ArrowRightIcon, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 199,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: function() {\n                editor.update(function() {\n                    var selection = (0,lexical__WEBPACK_IMPORTED_MODULE_7__.$getSelection)();\n                    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_7__.$isRangeSelection)(selection)) {\n                        return;\n                    }\n                    var nodes = selection.getNodes();\n                    var processedListItems = new Set();\n                    var hasListItems = false;\n                    // 找到所有需要缩进的列表项\n                    nodes.forEach(function(node) {\n                        var listItemNode = null;\n                        if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$isListItemNode)(node)) {\n                            listItemNode = node;\n                        } else if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$isListItemNode)(node.getParent())) {\n                            listItemNode = node.getParent();\n                        }\n                        if (listItemNode && (0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$isListItemNode)(listItemNode) && !processedListItems.has(listItemNode.getKey())) {\n                            processedListItems.add(listItemNode.getKey());\n                            var currentIndent = listItemNode.getIndent();\n                            listItemNode.setIndent(currentIndent + 1);\n                            hasListItems = true;\n                        }\n                    });\n                    // 如果没有找到列表项，使用通用缩进\n                    if (!hasListItems) {\n                        editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_7__.INDENT_CONTENT_COMMAND, undefined);\n                    }\n                });\n            }\n        },\n        {\n            title: \"Outdent\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__.ArrowLeftIcon, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 239,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: function() {\n                editor.update(function() {\n                    var selection = (0,lexical__WEBPACK_IMPORTED_MODULE_7__.$getSelection)();\n                    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_7__.$isRangeSelection)(selection)) {\n                        return;\n                    }\n                    var nodes = selection.getNodes();\n                    var processedListItems = new Set();\n                    var hasListItems = false;\n                    // 找到所有需要反缩进的列表项\n                    nodes.forEach(function(node) {\n                        var listItemNode = null;\n                        if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$isListItemNode)(node)) {\n                            listItemNode = node;\n                        } else if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$isListItemNode)(node.getParent())) {\n                            listItemNode = node.getParent();\n                        }\n                        if (listItemNode && (0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$isListItemNode)(listItemNode) && !processedListItems.has(listItemNode.getKey())) {\n                            processedListItems.add(listItemNode.getKey());\n                            $handleOutdent(listItemNode);\n                            hasListItems = true;\n                        }\n                    });\n                    // 如果没有找到列表项，使用通用反缩进\n                    if (!hasListItems) {\n                        editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_7__.OUTDENT_CONTENT_COMMAND, undefined);\n                    }\n                });\n            }\n        }, \n    ];\n    return /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_3__.createPortal)(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed z-50 \".concat(toolbarBg, \" border rounded-lg p-1.5 flex space-x-0.5 shadow-lg\"),\n        style: {\n            top: position.top,\n            left: position.left,\n            transform: \"translateX(-50%)\",\n            backgroundColor: theme === \"dark\" ? \"#3f3f46\" : \"#e4e4e7\"\n        },\n        children: toolbarButtons.map(function(button, index) {\n            if (button.type === \"separator\") {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-px h-6 \".concat(theme === \"dark\" ? \"bg-gray-600\" : \"bg-gray-300\", \" mx-1\")\n                }, index, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                    lineNumber: 292,\n                    columnNumber: 25\n                }, _this);\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: button.action,\n                title: button.title,\n                className: \"\\n                            px-2.5 py-1.5 rounded transition-colors duration-150 min-w-[30px] h-7 flex items-center justify-center text-sm font-medium\\n                            \".concat(button.isActive ? buttonActive : \"\".concat(buttonText, \" \").concat(buttonHover), \"\\n                        \"),\n                style: {\n                    backgroundColor: button.isActive ? theme === \"dark\" ? \"#3185eb\" : \"#eab834\" : \"transparent\"\n                },\n                onMouseEnter: function(e) {\n                    if (!button.isActive) {\n                        e.currentTarget.style.backgroundColor = theme === \"dark\" ? \"#3185eb\" : \"#eab834\";\n                        if (theme === \"dark\") {\n                            e.currentTarget.style.color = \"white\";\n                        }\n                    }\n                },\n                onMouseLeave: function(e) {\n                    if (!button.isActive) {\n                        e.currentTarget.style.backgroundColor = \"transparent\";\n                        e.currentTarget.style.color = \"\";\n                    }\n                },\n                children: button.icon\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 300,\n                columnNumber: 21\n            }, _this);\n        })\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n        lineNumber: 280,\n        columnNumber: 9\n    }, this), document.body);\n}\n_s(FloatingToolbarPlugin, \"FbU03gSrzEPMPysVlTtv8K886Dg=\", false, function() {\n    return [\n        _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_6__.useLexicalComposerContext,\n        next_themes__WEBPACK_IMPORTED_MODULE_4__.useTheme\n    ];\n});\n_c = FloatingToolbarPlugin;\nvar _c;\n$RefreshReg$(_c, \"FloatingToolbarPlugin\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/editor/plugins/floating-toolbar-plugin.tsx\n"));

/***/ })

});