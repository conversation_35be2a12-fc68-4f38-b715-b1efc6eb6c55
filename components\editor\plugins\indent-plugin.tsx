/**
 * Custom Indent Plugin for Lexical
 * Provides comprehensive indentation functionality with markdown persistence
 * Supports both paragraph and list indentation with proper save/restore
 */

import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import {
    $getSelection,
    $isRangeSelection,
    COMMAND_PRIORITY_LOW,
    createCommand,
    KEY_TAB_COMMAND,
    LexicalCommand,
    ElementNode,
    $isElementNode,
} from 'lexical';
import { $isHeadingNode } from '@lexical/rich-text';
import { $isParagraphNode } from 'lexical';
import { $isListItemNode } from '@lexical/list';
import { useEffect } from 'react';

export const INDENT_CONTENT_COMMAND: LexicalCommand<void> = createCommand(
    'INDENT_CONTENT_COMMAND',
);

export const OUTDENT_CONTENT_COMMAND: LexicalCommand<void> = createCommand(
    'OUTDENT_CONTENT_COMMAND',
);

interface IndentPluginProps {
    indentSize?: number;
    maxIndentLevel?: number;
}

// 自定义缩进属性管理 - 用于调试和标识
// const INDENT_ATTRIBUTE = 'data-indent-level';

// 获取元素的缩进级别 - 使用Lexical原生方法
function getElementIndentLevel(element: ElementNode): number {
    // 检查是否有getIndent方法（段落和标题节点有这个方法）
    if ('getIndent' in element && typeof element.getIndent === 'function') {
        return (element as any).getIndent();
    }

    // 对于列表项，检查样式
    const style = element.getStyle();
    const match = style.match(/margin-left:\s*(\d+)px/);
    if (match) {
        return Math.floor(parseInt(match[1]) / 32); // 32px per indent level
    }
    return 0;
}

// 设置元素的缩进级别 - 使用Lexical原生方法
function setElementIndentLevel(element: ElementNode, level: number): void {
    // 检查是否有setIndent方法（段落和标题节点有这个方法）
    if ('setIndent' in element && typeof element.setIndent === 'function') {
        (element as any).setIndent(level);
        return;
    }

    // 对于列表项，使用样式设置
    if (level <= 0) {
        // 移除缩进样式
        const currentStyle = element.getStyle();
        const newStyle = currentStyle.replace(/margin-left:\s*\d+px;?\s*/g, '');
        element.setStyle(newStyle);
    } else {
        // 设置缩进样式
        const indentPx = level * 32; // 32px per level
        const currentStyle = element.getStyle();
        const newStyle = currentStyle.replace(/margin-left:\s*\d+px;?\s*/g, '') +
                        `margin-left: ${indentPx}px;`;
        element.setStyle(newStyle.trim());
    }
}

export default function IndentPlugin({
    indentSize = 2,
    maxIndentLevel = 10,
}: IndentPluginProps = {}): null {
    const [editor] = useLexicalComposerContext();

    useEffect(() => {
        const removeTabCommand = editor.registerCommand(
            KEY_TAB_COMMAND,
            (event: KeyboardEvent) => {
                const selection = $getSelection();
                if ($isRangeSelection(selection)) {
                    event.preventDefault();

                    if (event.shiftKey) {
                        // Shift+Tab: Outdent
                        editor.dispatchCommand(OUTDENT_CONTENT_COMMAND, undefined);
                    } else {
                        // Tab: Indent
                        editor.dispatchCommand(INDENT_CONTENT_COMMAND, undefined);
                    }
                    return true;
                }
                return false;
            },
            COMMAND_PRIORITY_LOW,
        );

        const removeIndentCommand = editor.registerCommand(
            INDENT_CONTENT_COMMAND,
            () => {
                editor.update(() => {
                    const selection = $getSelection();
                    if ($isRangeSelection(selection)) {
                        const nodes = selection.getNodes();
                        const processedElements = new Set<ElementNode>();

                        for (const node of nodes) {
                            const element = node.getTopLevelElement();
                            if (element && $isElementNode(element) && !processedElements.has(element)) {
                                processedElements.add(element);

                                // 处理段落和标题的缩进
                                if ($isParagraphNode(element) || $isHeadingNode(element)) {
                                    const currentLevel = getElementIndentLevel(element);
                                    if (currentLevel < maxIndentLevel) {
                                        setElementIndentLevel(element, currentLevel + 1);
                                    }
                                }
                                // 处理列表项的缩进 - 使用 Lexical 的原生列表缩进
                                else if ($isListItemNode(element)) {
                                    // 对于列表项，我们需要使用 Lexical 的 INDENT_CONTENT_COMMAND
                                    // 但这里我们在命令处理器内部，所以直接设置缩进
                                    const currentLevel = getElementIndentLevel(element);
                                    if (currentLevel < maxIndentLevel) {
                                        // 列表项使用样式缩进
                                        const indentPx = (currentLevel + 1) * 32;
                                        const currentStyle = element.getStyle();
                                        const newStyle = currentStyle.replace(/margin-left:\s*\d+px;?\s*/g, '') +
                                                        `margin-left: ${indentPx}px;`;
                                        element.setStyle(newStyle.trim());
                                    }
                                }
                            }
                        }
                    }
                });
                return true;
            },
            COMMAND_PRIORITY_LOW,
        );

        const removeOutdentCommand = editor.registerCommand(
            OUTDENT_CONTENT_COMMAND,
            () => {
                editor.update(() => {
                    const selection = $getSelection();
                    if ($isRangeSelection(selection)) {
                        const nodes = selection.getNodes();
                        const processedElements = new Set<ElementNode>();

                        for (const node of nodes) {
                            const element = node.getTopLevelElement();
                            if (element && $isElementNode(element) && !processedElements.has(element)) {
                                processedElements.add(element);

                                // 处理段落和标题的反缩进
                                if ($isParagraphNode(element) || $isHeadingNode(element)) {
                                    const currentLevel = getElementIndentLevel(element);
                                    if (currentLevel > 0) {
                                        setElementIndentLevel(element, currentLevel - 1);
                                    }
                                }
                                // 处理列表项的反缩进
                                else if ($isListItemNode(element)) {
                                    const currentLevel = getElementIndentLevel(element);
                                    if (currentLevel > 0) {
                                        // 列表项使用样式反缩进
                                        const newLevel = currentLevel - 1;
                                        if (newLevel <= 0) {
                                            // 移除缩进样式
                                            const currentStyle = element.getStyle();
                                            const newStyle = currentStyle.replace(/margin-left:\s*\d+px;?\s*/g, '');
                                            element.setStyle(newStyle);
                                        } else {
                                            // 设置新的缩进级别
                                            const indentPx = newLevel * 32;
                                            const currentStyle = element.getStyle();
                                            const newStyle = currentStyle.replace(/margin-left:\s*\d+px;?\s*/g, '') +
                                                            `margin-left: ${indentPx}px;`;
                                            element.setStyle(newStyle.trim());
                                        }
                                    }
                                }
                            }
                        }
                    }
                });
                return true;
            },
            COMMAND_PRIORITY_LOW,
        );

        return () => {
            removeTabCommand();
            removeIndentCommand();
            removeOutdentCommand();
        };
    }, [editor, indentSize, maxIndentLevel]);

    return null;
}
