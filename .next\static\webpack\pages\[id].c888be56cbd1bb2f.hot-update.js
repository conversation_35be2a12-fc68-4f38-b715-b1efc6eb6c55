"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./libs/shared/markdown/parse-markdown-title.ts":
/*!******************************************************!*\
  !*** ./libs/shared/markdown/parse-markdown-title.ts ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"parseMarkdownTitle\": function() { return /* binding */ parseMarkdownTitle; }\n/* harmony export */ });\n/* harmony import */ var _html_to_markdown__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../html-to-markdown */ \"./libs/shared/html-to-markdown.ts\");\n\n/**\n * 检查内容是否为Lexical JSON格式\n */ function isLexicalJSON(content) {\n    try {\n        var parsed = JSON.parse(content);\n        return parsed && typeof parsed === \"object\" && parsed.root && parsed.root.type === \"root\";\n    } catch (e) {\n        return false;\n    }\n}\n/**\n * 从Lexical JSON中提取标题\n */ function extractTitleFromLexicalJSON(jsonContent) {\n    try {\n        var editorState = JSON.parse(jsonContent);\n        // 递归查找第一个有文本内容的节点\n        function findFirstText(node) {\n            if (!node) return \"\";\n            if (node.type === \"text\" && node.text) {\n                return node.text.trim();\n            }\n            if (node.children && Array.isArray(node.children)) {\n                var _iteratorNormalCompletion = true, _didIteratorError = false, _iteratorError = undefined;\n                try {\n                    for(var _iterator = node.children[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true){\n                        var child = _step.value;\n                        var text = findFirstText(child);\n                        if (text) return text;\n                    }\n                } catch (err) {\n                    _didIteratorError = true;\n                    _iteratorError = err;\n                } finally{\n                    try {\n                        if (!_iteratorNormalCompletion && _iterator.return != null) {\n                            _iterator.return();\n                        }\n                    } finally{\n                        if (_didIteratorError) {\n                            throw _iteratorError;\n                        }\n                    }\n                }\n            }\n            return \"\";\n        }\n        var firstText = findFirstText(editorState.root);\n        // 清理标题文本，移除markdown格式符号\n        if (firstText) {\n            var title = firstText.replace(/^#+\\s*/, \"\") // 移除标题标记\n            .replace(/\\*\\*(.*?)\\*\\*/g, \"$1\") // 粗体\n            .replace(/\\*(.*?)\\*/g, \"$1\") // 斜体\n            .replace(/`(.*?)`/g, \"$1\") // 行内代码\n            .replace(/!?\\[(.*?)\\]\\(.*?\\)/g, \"$1\") // 链接和图片\n            .trim();\n            return title.length > 0 ? title : undefined;\n        }\n        return undefined;\n    } catch (error) {\n        console.error(\"Error extracting title from Lexical JSON:\", error);\n        return undefined;\n    }\n}\n/**\n * Parse the first line as title from HTML, Markdown, or Lexical JSON content\n */ var parseMarkdownTitle = function(content) {\n    // 🔧 新逻辑：首先检查是否为Lexical JSON格式\n    if (isLexicalJSON(content)) {\n        console.log(\"Detected Lexical JSON format, extracting title...\");\n        var title = extractTitleFromLexicalJSON(content);\n        return {\n            content: content,\n            title: title\n        };\n    }\n    // 原有逻辑：处理HTML/Markdown格式\n    var markdown = (0,_html_to_markdown__WEBPACK_IMPORTED_MODULE_0__.convertHtmlToMarkdown)(content);\n    // Split by newline and get first non-empty line\n    var lines = markdown.split(\"\\n\").filter(function(line) {\n        return line.trim().length > 0;\n    });\n    var firstLine = lines[0];\n    if (!firstLine) {\n        return {\n            content: content,\n            title: undefined\n        };\n    }\n    // Remove heading markers if present\n    var title1 = firstLine.replace(/^#+\\s*/, \"\").trim();\n    // Remove other common markdown formatting\n    // Order matters here: remove links first, then other inline formatting\n    title1 = title1.replace(/!?\\[(.*?)\\]\\(.*?\\)/g, \"$1\") // Remove links and images, keeping the text: [text](url) or ![alt](url) -> text/alt\n    .replace(/\\*\\*(.*?)\\*\\*/g, \"$1\") // Bold: **text** -> text\n    .replace(/\\*(.*?)\\*/g, \"$1\") // Italic: *text* -> text\n    .replace(/__(.*?)__/g, \"$1\") // Bold (underscore): __text__ -> text\n    .replace(/_(.*?)_/g, \"$1\") // Italic (underscore): _text_ -> text\n    .replace(/~~(.*?)~~/g, \"$1\") // Strikethrough: ~~text~~ -> text\n    .replace(/`(.*?)`/g, \"$1\") // Inline code: `code` -> code\n    .trim();\n    return {\n        content: content,\n        title: title1.length > 0 ? title1 : undefined\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./libs/shared/markdown/parse-markdown-title.ts\n"));

/***/ })

});