"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./libs/web/state/lexical-editor.ts":
/*!******************************************!*\
  !*** ./libs/web/state/lexical-editor.ts ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/src/_object_spread.mjs */ \"./node_modules/@swc/helpers/src/_object_spread.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/src/_object_spread_props.mjs */ \"./node_modules/@swc/helpers/src/_object_spread_props.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var libs_web_state_note__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! libs/web/state/note */ \"./libs/web/state/note.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var libs_shared_note__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! libs/shared/note */ \"./libs/shared/note.ts\");\n/* harmony import */ var libs_web_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! libs/web/hooks/use-toast */ \"./libs/web/hooks/use-toast.ts\");\n/* harmony import */ var libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! libs/web/cache/note */ \"./libs/web/cache/note.ts\");\n/* harmony import */ var unstated_next__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! unstated-next */ \"./node_modules/unstated-next/dist/unstated-next.mjs\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash */ \"./node_modules/lodash/lodash.js\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var libs_web_utils_ime_state_manager__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! libs/web/utils/ime-state-manager */ \"./libs/web/utils/ime-state-manager.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar ROOT_ID = \"root\";\nvar useLexicalEditor = function(initNote) {\n    // Use initNote if provided, otherwise try to get from NoteState\n    var note = initNote;\n    var createNoteWithTitle, updateNote, createNote;\n    try {\n        var noteState = libs_web_state_note__WEBPACK_IMPORTED_MODULE_0__[\"default\"].useContainer();\n        createNoteWithTitle = noteState.createNoteWithTitle;\n        updateNote = noteState.updateNote;\n        createNote = noteState.createNote;\n        // Only use noteState.note if no initNote is provided\n        if (!note) {\n            note = noteState.note;\n        }\n    } catch (error) {\n        // If NoteState is not available, we'll work with just the initNote\n        console.warn(\"NoteState not available in LexicalEditorState, using initNote only\");\n        createNoteWithTitle = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n        updateNote = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n        createNote = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n    }\n    var router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    var toast = (0,libs_web_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    var editorEl = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // 🔧 新增：快照状态管理\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null), noteSnapshot = ref[0], setNoteSnapshot = ref[1];\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\"), currentEditorContent = ref1[0], setCurrentEditorContent = ref1[1];\n    // Manual save function for IndexedDB\n    var saveToIndexedDB = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(function(data) {\n            var existingNote, baseNote, updatedNote;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                            2\n                        ];\n                        return [\n                            4,\n                            libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                        ];\n                    case 1:\n                        existingNote = _state.sent();\n                        baseNote = existingNote || note;\n                        updatedNote = (0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])({}, baseNote, data);\n                        return [\n                            4,\n                            libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].setItem(note.id, updatedNote)\n                        ];\n                    case 2:\n                        _state.sent();\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function(data) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        note\n    ]);\n    var syncToServer = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(function() {\n        var isNew, localNote, noteToSave, noteData, item, noteUrl, updatedNote, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        false\n                    ];\n                    isNew = (0,lodash__WEBPACK_IMPORTED_MODULE_6__.has)(router.query, \"new\");\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        11,\n                        ,\n                        12\n                    ]);\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                    ];\n                case 2:\n                    localNote = _state.sent();\n                    noteToSave = localNote || note;\n                    if (!isNew) return [\n                        3,\n                        7\n                    ];\n                    noteData = (0,_swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_11__[\"default\"])((0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])({}, noteToSave), {\n                        pid: router.query.pid || ROOT_ID\n                    });\n                    return [\n                        4,\n                        createNote(noteData)\n                    ];\n                case 3:\n                    item = _state.sent();\n                    if (!item) return [\n                        3,\n                        6\n                    ];\n                    noteUrl = \"/\".concat(item.id);\n                    if (!(router.asPath !== noteUrl)) return [\n                        3,\n                        5\n                    ];\n                    return [\n                        4,\n                        router.replace(noteUrl, undefined, {\n                            shallow: true\n                        })\n                    ];\n                case 4:\n                    _state.sent();\n                    _state.label = 5;\n                case 5:\n                    toast(\"Note saved to server\", \"success\");\n                    return [\n                        2,\n                        true\n                    ];\n                case 6:\n                    return [\n                        3,\n                        10\n                    ];\n                case 7:\n                    return [\n                        4,\n                        updateNote(noteToSave)\n                    ];\n                case 8:\n                    updatedNote = _state.sent();\n                    if (!updatedNote) return [\n                        3,\n                        10\n                    ];\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].setItem(updatedNote.id, updatedNote)\n                    ];\n                case 9:\n                    _state.sent();\n                    toast(\"Note updated on server\", \"success\");\n                    return [\n                        2,\n                        true\n                    ];\n                case 10:\n                    return [\n                        3,\n                        12\n                    ];\n                case 11:\n                    error = _state.sent();\n                    toast(\"Failed to save note to server\", \"error\");\n                    return [\n                        2,\n                        false\n                    ];\n                case 12:\n                    return [\n                        2,\n                        false\n                    ];\n            }\n        });\n    }), [\n        note,\n        router,\n        createNote,\n        updateNote,\n        toast\n    ]);\n    var onCreateLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(function(title) {\n            var result;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!createNoteWithTitle) return [\n                            2,\n                            \"\"\n                        ];\n                        return [\n                            4,\n                            createNoteWithTitle(title)\n                        ];\n                    case 1:\n                        result = _state.sent();\n                        if (result === null || result === void 0 ? void 0 : result.id) {\n                            return [\n                                2,\n                                \"/\".concat(result.id)\n                            ];\n                        }\n                        return [\n                            2,\n                            \"\"\n                        ];\n                }\n            });\n        });\n        return function(title) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        createNoteWithTitle\n    ]);\n    var onSearchLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(function(term) {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    []\n                ];\n            });\n        });\n        return function(term) {\n            return _ref.apply(this, arguments);\n        };\n    }(), []);\n    var onClickLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(href, event) {\n        if ((0,libs_shared_note__WEBPACK_IMPORTED_MODULE_3__.isNoteLink)(href)) {\n            event.preventDefault();\n            router.push(href);\n        } else {\n            window.open(href, \"_blank\", \"noopener,noreferrer\");\n        }\n    }, [\n        router\n    ]);\n    var onUploadImage = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(function(_file, _id) {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(this, function(_state) {\n                // Image upload is disabled in PostgreSQL version\n                toast(\"Image upload is not supported in this version\", \"error\");\n                throw new Error(\"Image upload is not supported\");\n            });\n        });\n        return function(_file, _id) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        toast\n    ]);\n    var onHoverLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(event) {\n        return true;\n    }, []);\n    var ref2 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(), backlinks = ref2[0], setBackLinks = ref2[1];\n    var getBackLinks = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(function() {\n        var linkNotes;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    console.log(note === null || note === void 0 ? void 0 : note.id);\n                    linkNotes = [];\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        linkNotes\n                    ];\n                    setBackLinks([]);\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].iterate(function(value) {\n                            var ref;\n                            if ((ref = value.linkIds) === null || ref === void 0 ? void 0 : ref.includes((note === null || note === void 0 ? void 0 : note.id) || \"\")) {\n                                linkNotes.push(value);\n                            }\n                        })\n                    ];\n                case 1:\n                    _state.sent();\n                    setBackLinks(linkNotes);\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        note === null || note === void 0 ? void 0 : note.id\n    ]);\n    // 🔧 快照初始化逻辑 - 打开笔记时设置JSON快照\n    var initializeSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(function() {\n        var cachedNote, snapshotJsonContent, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) {\n                        // 新建笔记：快照为空值\n                        setNoteSnapshot(null);\n                        setCurrentEditorContent(\"\");\n                        console.log(\"\\uD83D\\uDD27 新建笔记：JSON快照设置为空值\");\n                        return [\n                            2\n                        ];\n                    }\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        3,\n                        ,\n                        4\n                    ]);\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                    ];\n                case 2:\n                    cachedNote = _state.sent();\n                    snapshotJsonContent = (cachedNote === null || cachedNote === void 0 ? void 0 : cachedNote.content) || \"\";\n                    setNoteSnapshot(snapshotJsonContent);\n                    setCurrentEditorContent(snapshotJsonContent);\n                    console.log(\"\\uD83D\\uDD27 已存在笔记：JSON快照设置为缓存内容\", {\n                        noteId: note.id,\n                        hasSnapshot: !!snapshotJsonContent,\n                        isJsonFormat: snapshotJsonContent.startsWith(\"{\")\n                    });\n                    return [\n                        3,\n                        4\n                    ];\n                case 3:\n                    error = _state.sent();\n                    console.error(\"\\uD83D\\uDD27 JSON快照初始化失败:\", error);\n                    // 失败时设置为空快照\n                    setNoteSnapshot(null);\n                    setCurrentEditorContent(\"\");\n                    return [\n                        3,\n                        4\n                    ];\n                case 4:\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        note === null || note === void 0 ? void 0 : note.id\n    ]);\n    // 当笔记ID变化时初始化快照\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n        initializeSnapshot();\n    }, [\n        initializeSnapshot\n    ]);\n    // 🔧 修复：恢复编辑器基本功能 - 更新JSON内容状态\n    var originalOnEditorChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(value) {\n        var jsonContent = value(); // 这里是JSON格式的内容\n        // 更新当前编辑器JSON内容状态\n        setCurrentEditorContent(jsonContent);\n        console.log(\"\\uD83D\\uDD27 编辑器JSON内容更新:\", {\n            contentLength: jsonContent.length\n        });\n    // 保存逻辑现在完全由SaveButton的快照对比机制处理\n    }, []);\n    // 使用智能onChange包装器 - 基于输入状态智能处理\n    var onEditorChange = (0,libs_web_utils_ime_state_manager__WEBPACK_IMPORTED_MODULE_7__.createSmartOnChange)(originalOnEditorChange, {\n        delay: 200 // 快速输入结束后200ms执行\n    });\n    // Function to handle title changes specifically\n    var onTitleChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(title) {\n        var ref;\n        (ref = saveToIndexedDB({\n            title: title,\n            updated_at: new Date().toISOString()\n        })) === null || ref === void 0 ? void 0 : ref.catch(function(v) {\n            return console.error(\"Error whilst saving title to IndexedDB: %O\", v);\n        });\n    }, [\n        saveToIndexedDB\n    ]);\n    // 🔧 修复：JSON快照对比功能 - 供SaveButton使用\n    var compareWithSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        // 如果是新建笔记（快照为null），任何JSON内容都算作变化\n        if (noteSnapshot === null) {\n            return currentEditorContent.trim() !== \"\";\n        }\n        // 已存在笔记：比较当前JSON内容与JSON快照\n        var hasChanges = currentEditorContent !== noteSnapshot;\n        console.log(\"\\uD83D\\uDD27 JSON快照对比:\", {\n            hasChanges: hasChanges,\n            currentLength: currentEditorContent.length,\n            snapshotLength: noteSnapshot.length,\n            bothAreJson: currentEditorContent.startsWith(\"{\") && noteSnapshot.startsWith(\"{\")\n        });\n        return hasChanges;\n    }, [\n        noteSnapshot,\n        currentEditorContent\n    ]);\n    // 🔧 新增：获取当前编辑器状态 - 供SaveButton使用\n    var getEditorState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        return {\n            hasChanges: compareWithSnapshot(),\n            currentContent: currentEditorContent,\n            snapshot: noteSnapshot,\n            isNewNote: noteSnapshot === null\n        };\n    }, [\n        compareWithSnapshot,\n        currentEditorContent,\n        noteSnapshot\n    ]);\n    // 🔧 修复：保存当前JSON内容到IndexedDB - 供SaveButton调用\n    var saveCurrentContent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(function() {\n        var title, titleInput, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        false\n                    ];\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        3,\n                        ,\n                        4\n                    ]);\n                    if (note === null || note === void 0 ? void 0 : note.isDailyNote) {\n                        title = note.title;\n                    } else {\n                        titleInput = document.querySelector(\"h1 textarea\");\n                        if (titleInput && titleInput.value) {\n                            title = titleInput.value.trim();\n                        } else {\n                            // 对于JSON格式，使用现有标题或默认标题\n                            title = (note === null || note === void 0 ? void 0 : note.title) || \"Untitled\";\n                        }\n                    }\n                    // 保存JSON内容到IndexedDB\n                    return [\n                        4,\n                        saveToIndexedDB({\n                            content: currentEditorContent,\n                            title: title,\n                            updated_at: new Date().toISOString()\n                        })\n                    ];\n                case 2:\n                    _state.sent();\n                    console.log(\"\\uD83D\\uDD27 JSON内容已保存到IndexedDB\", {\n                        noteId: note.id,\n                        title: title,\n                        contentLength: currentEditorContent.length,\n                        isJsonFormat: currentEditorContent.startsWith(\"{\")\n                    });\n                    return [\n                        2,\n                        true\n                    ];\n                case 3:\n                    error = _state.sent();\n                    console.error(\"\\uD83D\\uDD27 保存JSON到IndexedDB失败:\", error);\n                    return [\n                        2,\n                        false\n                    ];\n                case 4:\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        note,\n        currentEditorContent,\n        saveToIndexedDB\n    ]);\n    return {\n        onCreateLink: onCreateLink,\n        onSearchLink: onSearchLink,\n        onClickLink: onClickLink,\n        onUploadImage: onUploadImage,\n        onHoverLink: onHoverLink,\n        getBackLinks: getBackLinks,\n        onEditorChange: onEditorChange,\n        onTitleChange: onTitleChange,\n        saveToIndexedDB: saveToIndexedDB,\n        syncToServer: syncToServer,\n        backlinks: backlinks,\n        editorEl: editorEl,\n        note: note,\n        // 🔧 新增：快照对比相关功能\n        getEditorState: getEditorState,\n        saveCurrentContent: saveCurrentContent,\n        compareWithSnapshot: compareWithSnapshot\n    };\n};\nvar LexicalEditorState = (0,unstated_next__WEBPACK_IMPORTED_MODULE_12__.createContainer)(useLexicalEditor);\n/* harmony default export */ __webpack_exports__[\"default\"] = (LexicalEditorState);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9saWJzL3dlYi9zdGF0ZS9sZXhpY2FsLWVkaXRvci50cy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUFBOzs7O0FBQTRDO0FBQ0o7QUFPekI7QUFFMEM7QUFDTDtBQUdSO0FBQ0k7QUFHbkI7QUFFMEM7QUFDdkUsSUFBTVksT0FBTyxHQUFHLE1BQU07QUFFdEIsSUFBTUMsZ0JBQWdCLEdBQUcsU0FBQ0MsUUFBb0IsRUFBSztJQUMvQyxnRUFBZ0U7SUFDaEUsSUFBSUMsSUFBSSxHQUFHRCxRQUFRO0lBQ25CLElBQUlFLG1CQUFtQixFQUFPQyxVQUFVLEVBQU9DLFVBQVU7SUFFekQsSUFBSTtRQUNBLElBQU1DLFNBQVMsR0FBR25CLHdFQUFzQixFQUFFO1FBQzFDZ0IsbUJBQW1CLEdBQUdHLFNBQVMsQ0FBQ0gsbUJBQW1CLENBQUM7UUFDcERDLFVBQVUsR0FBR0UsU0FBUyxDQUFDRixVQUFVLENBQUM7UUFDbENDLFVBQVUsR0FBR0MsU0FBUyxDQUFDRCxVQUFVLENBQUM7UUFFbEMscURBQXFEO1FBQ3JELElBQUksQ0FBQ0gsSUFBSSxFQUFFO1lBQ1BBLElBQUksR0FBR0ksU0FBUyxDQUFDSixJQUFJLENBQUM7UUFDMUIsQ0FBQztJQUNMLEVBQUUsT0FBT00sS0FBSyxFQUFFO1FBQ1osbUVBQW1FO1FBQ25FQyxPQUFPLENBQUNDLElBQUksQ0FBQyxvRUFBb0UsQ0FBQyxDQUFDO1FBQ25GUCxtQkFBbUIsaUJBQUc7O2dCQUFZUTs7b0JBQUFBLFNBQVM7a0JBQUE7O1NBQUEsRUFBQztRQUM1Q1AsVUFBVSxpQkFBRzs7Z0JBQVlPOztvQkFBQUEsU0FBUztrQkFBQTs7U0FBQSxFQUFDO1FBQ25DTixVQUFVLGlCQUFHOztnQkFBWU07O29CQUFBQSxTQUFTO2tCQUFBOztTQUFBLEVBQUM7SUFDdkMsQ0FBQztJQUVELElBQU1DLE1BQU0sR0FBR3hCLHNEQUFTLEVBQUU7SUFDMUIsSUFBTXlCLEtBQUssR0FBR25CLGtFQUFRLEVBQUU7SUFDeEIsSUFBTW9CLFFBQVEsR0FBR3ZCLDZDQUFNLENBQW1CLElBQUksQ0FBQztJQUUvQyxjQUFjO0lBQ2QsSUFBd0NELEdBQTZCLEdBQTdCQSwrQ0FBUSxDQUFnQixJQUFJLENBQUMsRUFBOUR5QixZQUFZLEdBQXFCekIsR0FBNkIsR0FBbEQsRUFBRTBCLGVBQWUsR0FBSTFCLEdBQTZCLEdBQWpDO0lBQ3BDLElBQXdEQSxJQUFvQixHQUFwQkEsK0NBQVEsQ0FBUyxFQUFFLENBQUMsRUFBckUyQixvQkFBb0IsR0FBNkIzQixJQUFvQixHQUFqRCxFQUFFNEIsdUJBQXVCLEdBQUk1QixJQUFvQixHQUF4QjtJQUVwRCxxQ0FBcUM7SUFDckMsSUFBTTZCLGVBQWUsR0FBRzlCLGtEQUFXO21CQUMvQiw2RkFBTytCLElBQXdCLEVBQUs7Z0JBSTFCQyxZQUFZLEVBQ1pDLFFBQVEsRUFFUkMsV0FBVzs7Ozt3QkFOakIsSUFBSSxDQUFDckIsQ0FBQUEsSUFBSSxhQUFKQSxJQUFJLFdBQUksR0FBUkEsS0FBQUEsQ0FBUSxHQUFSQSxJQUFJLENBQUVzQixFQUFFLEdBQUU7OzBCQUFPO3dCQUdEOzs0QkFBTTdCLG1FQUFpQixDQUFDTyxJQUFJLENBQUNzQixFQUFFLENBQUM7MEJBQUE7O3dCQUEvQ0gsWUFBWSxHQUFHLGFBQWdDO3dCQUMvQ0MsUUFBUSxHQUFHRCxZQUFZLElBQUluQixJQUFJLENBQUM7d0JBRWhDcUIsV0FBVyxHQUFHLG9GQUFLRCxRQUFRLEVBQUtGLElBQUksQ0FBRSxDQUFDO3dCQUU3Qzs7NEJBQU16QixtRUFBaUIsQ0FBQ08sSUFBSSxDQUFDc0IsRUFBRSxFQUFFRCxXQUFXLENBQUM7MEJBQUE7O3dCQUE3QyxhQUE2QyxDQUFDOzs7Ozs7UUFDbEQsQ0FBQzt3QkFWTUgsSUFBd0I7OztTQVcvQjtRQUFDbEIsSUFBSTtLQUFDLENBQ1Q7SUFFRCxJQUFNeUIsWUFBWSxHQUFHdEMsa0RBQVcsZUFDNUIsK0ZBQVk7WUFHRnVDLEtBQUssRUFHREMsU0FBUyxFQUNUQyxVQUFVLEVBR05DLFFBQVEsRUFLUkMsSUFBSSxFQUdBQyxPQUFPLEVBUVhWLFdBQVcsRUFRaEJmLEtBQUs7Ozs7b0JBakNkLElBQUksQ0FBQ04sQ0FBQUEsSUFBSSxhQUFKQSxJQUFJLFdBQUksR0FBUkEsS0FBQUEsQ0FBUSxHQUFSQSxJQUFJLENBQUVzQixFQUFFLEdBQUU7O3dCQUFPLEtBQUs7c0JBQUM7b0JBRXRCSSxLQUFLLEdBQUcvQiwyQ0FBRyxDQUFDZSxNQUFNLENBQUNzQixLQUFLLEVBQUUsS0FBSyxDQUFDLENBQUM7Ozs7Ozs7OztvQkFHakI7O3dCQUFNdkMsbUVBQWlCLENBQUNPLElBQUksQ0FBQ3NCLEVBQUUsQ0FBQztzQkFBQTs7b0JBQTVDSyxTQUFTLEdBQUcsYUFBZ0M7b0JBQzVDQyxVQUFVLEdBQUdELFNBQVMsSUFBSTNCLElBQUksQ0FBQzt5QkFFakMwQixLQUFLLEVBQUxBOzs7c0JBQUs7b0JBQ0NHLFFBQVEsR0FBRywwS0FDVkQsVUFBVTt3QkFDYkssR0FBRyxFQUFFLE1BQU8sQ0FBQ0QsS0FBSyxDQUFDQyxHQUFHLElBQWVwQyxPQUFPO3NCQUMvQyxDQUFDO29CQUVXOzt3QkFBTU0sVUFBVSxDQUFDMEIsUUFBUSxDQUFDO3NCQUFBOztvQkFBakNDLElBQUksR0FBRyxhQUEwQjt5QkFFbkNBLElBQUksRUFBSkE7OztzQkFBSTtvQkFDRUMsT0FBTyxHQUFHLEdBQUUsQ0FBVSxPQUFSRCxJQUFJLENBQUNSLEVBQUUsQ0FBRSxDQUFDO3lCQUMxQlosQ0FBQUEsTUFBTSxDQUFDd0IsTUFBTSxLQUFLSCxPQUFPLEdBQXpCckI7OztzQkFBeUI7b0JBQ3pCOzt3QkFBTUEsTUFBTSxDQUFDeUIsT0FBTyxDQUFDSixPQUFPLEVBQUV0QixTQUFTLEVBQUU7NEJBQUUyQixPQUFPLEVBQUUsSUFBSTt5QkFBRSxDQUFDO3NCQUFBOztvQkFBM0QsYUFBMkQsQ0FBQzs7O29CQUVoRXpCLEtBQUssQ0FBQyxzQkFBc0IsRUFBRSxTQUFTLENBQUMsQ0FBQztvQkFDekM7O3dCQUFPLElBQUk7c0JBQUM7Ozs7Ozs7b0JBR0k7O3dCQUFNVCxVQUFVLENBQUMwQixVQUFVLENBQUM7c0JBQUE7O29CQUExQ1AsV0FBVyxHQUFHLGFBQTRCO3lCQUU1Q0EsV0FBVyxFQUFYQTs7O3NCQUFXO29CQUNYOzt3QkFBTTVCLG1FQUFpQixDQUFDNEIsV0FBVyxDQUFDQyxFQUFFLEVBQUVELFdBQVcsQ0FBQztzQkFBQTs7b0JBQXBELGFBQW9ELENBQUM7b0JBQ3JEVixLQUFLLENBQUMsd0JBQXdCLEVBQUUsU0FBUyxDQUFDLENBQUM7b0JBQzNDOzt3QkFBTyxJQUFJO3NCQUFDOzs7Ozs7O29CQUdmTCxLQUFLO29CQUNWSyxLQUFLLENBQUMsK0JBQStCLEVBQUUsT0FBTyxDQUFDLENBQUM7b0JBQ2hEOzt3QkFBTyxLQUFLO3NCQUFDOztvQkFHakI7O3dCQUFPLEtBQUs7c0JBQUM7OztJQUNqQixDQUFDLEdBQ0Q7UUFBQ1gsSUFBSTtRQUFFVSxNQUFNO1FBQUVQLFVBQVU7UUFBRUQsVUFBVTtRQUFFUyxLQUFLO0tBQUMsQ0FDaEQ7SUFFRCxJQUFNMEIsWUFBWSxHQUFHbEQsa0RBQVc7bUJBQzVCLDZGQUFPbUQsS0FBYSxFQUFLO2dCQUdmQyxNQUFNOzs7O3dCQUZaLElBQUksQ0FBQ3RDLG1CQUFtQixFQUFFOzs0QkFBTyxFQUFFOzBCQUFDO3dCQUVyQjs7NEJBQU1BLG1CQUFtQixDQUFDcUMsS0FBSyxDQUFDOzBCQUFBOzt3QkFBekNDLE1BQU0sR0FBRyxhQUFnQzt3QkFDL0MsSUFBSUEsTUFBTSxhQUFOQSxNQUFNLFdBQUksR0FBVkEsS0FBQUEsQ0FBVSxHQUFWQSxNQUFNLENBQUVqQixFQUFFLEVBQUU7NEJBQ1o7O2dDQUFRLEdBQUMsQ0FBWSxPQUFWaUIsTUFBTSxDQUFDakIsRUFBRSxDQUFFOzhCQUFDO3dCQUMzQixDQUFDO3dCQUNEOzs0QkFBTyxFQUFFOzBCQUFDOzs7UUFDZCxDQUFDO3dCQVJNZ0IsS0FBYTs7O1NBU3BCO1FBQUNyQyxtQkFBbUI7S0FBQyxDQUN4QjtJQUVELElBQU11QyxZQUFZLEdBQUdyRCxrREFBVzttQkFDNUIsNkZBQU9zRCxJQUFZLEVBQUs7O2dCQUNwQjs7O2tCQUFVOztRQUNkLENBQUM7d0JBRk1BLElBQVk7OztTQUduQixFQUFFLENBQ0w7SUFFRCxJQUFNQyxXQUFXLEdBQUd2RCxrREFBVyxDQUMzQixTQUFDd0QsSUFBWSxFQUFFQyxLQUFzQixFQUFLO1FBQ3RDLElBQUlyRCw0REFBVSxDQUFDb0QsSUFBSSxDQUFDLEVBQUU7WUFDbEJDLEtBQUssQ0FBQ0MsY0FBYyxFQUFFLENBQUM7WUFDdkJuQyxNQUFNLENBQUNvQyxJQUFJLENBQUNILElBQUksQ0FBQyxDQUFDO1FBQ3RCLE9BQU87WUFDSEksTUFBTSxDQUFDQyxJQUFJLENBQUNMLElBQUksRUFBRSxRQUFRLEVBQUUscUJBQXFCLENBQUMsQ0FBQztRQUN2RCxDQUFDO0lBQ0wsQ0FBQyxFQUNEO1FBQUNqQyxNQUFNO0tBQUMsQ0FDWDtJQUVELElBQU11QyxhQUFhLEdBQUc5RCxrREFBVzttQkFDN0IsNkZBQU8rRCxLQUFXLEVBQUVDLEdBQVksRUFBSzs7Z0JBQ2pDLGlEQUFpRDtnQkFDakR4QyxLQUFLLENBQUMsK0NBQStDLEVBQUUsT0FBTyxDQUFDLENBQUM7Z0JBQ2hFLE1BQU0sSUFBSXlDLEtBQUssQ0FBQywrQkFBK0IsQ0FBQyxDQUFDOztRQUNyRCxDQUFDO3dCQUpNRixLQUFXLEVBQUVDLEdBQVk7OztTQUtoQztRQUFDeEMsS0FBSztLQUFDLENBQ1Y7SUFFRCxJQUFNMEMsV0FBVyxHQUFHbEUsa0RBQVcsQ0FBQyxTQUFDeUQsS0FBc0IsRUFBSztRQUN4RCxPQUFPLElBQUksQ0FBQztJQUNoQixDQUFDLEVBQUUsRUFBRSxDQUFDO0lBRU4sSUFBa0N4RCxJQUEyQixHQUEzQkEsK0NBQVEsRUFBbUIsRUFBdERrRSxTQUFTLEdBQWtCbEUsSUFBMkIsR0FBN0MsRUFBRW1FLFlBQVksR0FBSW5FLElBQTJCLEdBQS9CO0lBRTlCLElBQU1vRSxZQUFZLEdBQUdyRSxrREFBVyxlQUFDLCtGQUFZO1lBRW5Dc0UsU0FBUzs7OztvQkFEZmxELE9BQU8sQ0FBQ21ELEdBQUcsQ0FBQzFELElBQUksYUFBSkEsSUFBSSxXQUFJLEdBQVJBLEtBQUFBLENBQVEsR0FBUkEsSUFBSSxDQUFFc0IsRUFBRSxDQUFDLENBQUM7b0JBQ2hCbUMsU0FBUyxLQUFzQixDQUFDO29CQUN0QyxJQUFJLENBQUN6RCxDQUFBQSxJQUFJLGFBQUpBLElBQUksV0FBSSxHQUFSQSxLQUFBQSxDQUFRLEdBQVJBLElBQUksQ0FBRXNCLEVBQUUsR0FBRTs7d0JBQU9tQyxTQUFTO3NCQUFDO29CQUNoQ0YsWUFBWSxJQUFJLENBQUM7b0JBQ2pCOzt3QkFBTTlELG1FQUFpQixDQUFzQixTQUFDbUUsS0FBSyxFQUFLO2dDQUNoREEsR0FBYTs0QkFBakIsSUFBSUEsQ0FBQUEsR0FBYSxHQUFiQSxLQUFLLENBQUNDLE9BQU8sY0FBYkQsR0FBYSxXQUFVLEdBQXZCQSxLQUFBQSxDQUF1QixHQUF2QkEsR0FBYSxDQUFFRSxRQUFRLENBQUM5RCxDQUFBQSxJQUFJLGFBQUpBLElBQUksV0FBSSxHQUFSQSxLQUFBQSxDQUFRLEdBQVJBLElBQUksQ0FBRXNCLEVBQUUsS0FBSSxFQUFFLENBQUMsRUFBRTtnQ0FDekNtQyxTQUFTLENBQUNYLElBQUksQ0FBQ2MsS0FBSyxDQUFDLENBQUM7NEJBQzFCLENBQUM7d0JBQ0wsQ0FBQyxDQUFDO3NCQUFBOztvQkFKRixhQUlFLENBQUM7b0JBQ0hMLFlBQVksQ0FBQ0UsU0FBUyxDQUFDLENBQUM7Ozs7OztJQUM1QixDQUFDLEdBQUU7UUFBQ3pELElBQUksYUFBSkEsSUFBSSxXQUFJLEdBQVJBLEtBQUFBLENBQVEsR0FBUkEsSUFBSSxDQUFFc0IsRUFBRTtLQUFDLENBQUM7SUFFZCw0QkFBNEI7SUFDNUIsSUFBTXlDLGtCQUFrQixHQUFHNUUsa0RBQVcsZUFBQywrRkFBWTtZQVdyQzZFLFVBQVUsRUFDVkMsbUJBQW1CLEVBUXBCM0QsS0FBSzs7OztvQkFuQmQsSUFBSSxDQUFDTixDQUFBQSxJQUFJLGFBQUpBLElBQUksV0FBSSxHQUFSQSxLQUFBQSxDQUFRLEdBQVJBLElBQUksQ0FBRXNCLEVBQUUsR0FBRTt3QkFDWCxhQUFhO3dCQUNiUixlQUFlLENBQUMsSUFBSSxDQUFDLENBQUM7d0JBQ3RCRSx1QkFBdUIsQ0FBQyxFQUFFLENBQUMsQ0FBQzt3QkFDNUJULE9BQU8sQ0FBQ21ELEdBQUcsQ0FBQywrQkFBb0IsQ0FBQyxDQUFDO3dCQUNsQzs7MEJBQU87b0JBQ1gsQ0FBQzs7Ozs7Ozs7O29CQUlzQjs7d0JBQU1qRSxtRUFBaUIsQ0FBQ08sSUFBSSxDQUFDc0IsRUFBRSxDQUFDO3NCQUFBOztvQkFBN0MwQyxVQUFVLEdBQUcsYUFBZ0M7b0JBQzdDQyxtQkFBbUIsR0FBR0QsQ0FBQUEsVUFBVSxhQUFWQSxVQUFVLFdBQVMsR0FBbkJBLEtBQUFBLENBQW1CLEdBQW5CQSxVQUFVLENBQUVFLE9BQU8sS0FBSSxFQUFFLENBQUM7b0JBQ3REcEQsZUFBZSxDQUFDbUQsbUJBQW1CLENBQUMsQ0FBQztvQkFDckNqRCx1QkFBdUIsQ0FBQ2lELG1CQUFtQixDQUFDLENBQUM7b0JBQzdDMUQsT0FBTyxDQUFDbUQsR0FBRyxDQUFDLGtDQUF1QixFQUFFO3dCQUNqQ1MsTUFBTSxFQUFFbkUsSUFBSSxDQUFDc0IsRUFBRTt3QkFDZjhDLFdBQVcsRUFBRSxDQUFDLENBQUNILG1CQUFtQjt3QkFDbENJLFlBQVksRUFBRUosbUJBQW1CLENBQUNLLFVBQVUsQ0FBQyxHQUFHLENBQUM7cUJBQ3BELENBQUMsQ0FBQzs7Ozs7O29CQUNFaEUsS0FBSztvQkFDVkMsT0FBTyxDQUFDRCxLQUFLLENBQUMsMkJBQWdCLEVBQUVBLEtBQUssQ0FBQyxDQUFDO29CQUN2QyxZQUFZO29CQUNaUSxlQUFlLENBQUMsSUFBSSxDQUFDLENBQUM7b0JBQ3RCRSx1QkFBdUIsQ0FBQyxFQUFFLENBQUMsQ0FBQzs7Ozs7Ozs7Ozs7SUFFcEMsQ0FBQyxHQUFFO1FBQUNoQixJQUFJLGFBQUpBLElBQUksV0FBSSxHQUFSQSxLQUFBQSxDQUFRLEdBQVJBLElBQUksQ0FBRXNCLEVBQUU7S0FBQyxDQUFDO0lBRWQsZ0JBQWdCO0lBQ2hCaEMsZ0RBQVMsQ0FBQyxXQUFNO1FBQ1p5RSxrQkFBa0IsRUFBRSxDQUFDO0lBQ3pCLENBQUMsRUFBRTtRQUFDQSxrQkFBa0I7S0FBQyxDQUFDLENBQUM7SUFFekIsOEJBQThCO0lBQzlCLElBQU1RLHNCQUFzQixHQUFHcEYsa0RBQVcsQ0FDdEMsU0FBQ3lFLEtBQW1CLEVBQVc7UUFDM0IsSUFBTVksV0FBVyxHQUFHWixLQUFLLEVBQUUsRUFBRSxlQUFlO1FBRTVDLGtCQUFrQjtRQUNsQjVDLHVCQUF1QixDQUFDd0QsV0FBVyxDQUFDLENBQUM7UUFDckNqRSxPQUFPLENBQUNtRCxHQUFHLENBQUMsMkJBQWdCLEVBQUU7WUFBRWUsYUFBYSxFQUFFRCxXQUFXLENBQUNFLE1BQU07U0FBRSxDQUFDLENBQUM7SUFFckUsK0JBQStCO0lBQ25DLENBQUMsRUFDRCxFQUFFLENBQ0w7SUFFRCwrQkFBK0I7SUFDL0IsSUFBTUMsY0FBYyxHQUFHL0UscUZBQW1CLENBQUMyRSxzQkFBc0IsRUFBRTtRQUMvREssS0FBSyxFQUFFLEdBQUcsQ0FBQyxpQkFBaUI7S0FDL0IsQ0FBQztJQUVGLGdEQUFnRDtJQUNoRCxJQUFNQyxhQUFhLEdBQUcxRixrREFBVyxDQUM3QixTQUFDbUQsS0FBYSxFQUFXO1lBQ3JCckIsR0FHRTtRQUhGQSxDQUFBQSxHQUdFLEdBSEZBLGVBQWUsQ0FBQztZQUNacUIsS0FBSyxFQUFMQSxLQUFLO1lBQ0x3QyxVQUFVLEVBQUUsSUFBSUMsSUFBSSxFQUFFLENBQUNDLFdBQVcsRUFBRTtTQUN2QyxDQUFDLGNBSEYvRCxHQUdFLFdBQU8sR0FIVEEsS0FBQUEsQ0FHUyxHQUhUQSxHQUdFLENBQUVnRSxLQUFLLENBQUMsU0FBQ0MsQ0FBQzttQkFBSzNFLE9BQU8sQ0FBQ0QsS0FBSyxDQUFDLDRDQUE0QyxFQUFFNEUsQ0FBQyxDQUFDO1NBQUEsQ0FBQyxDQUFDO0lBQ3JGLENBQUMsRUFDRDtRQUFDakUsZUFBZTtLQUFDLENBQ3BCO0lBRUQsa0NBQWtDO0lBQ2xDLElBQU1rRSxtQkFBbUIsR0FBR2hHLGtEQUFXLENBQUMsV0FBZTtRQUNuRCxpQ0FBaUM7UUFDakMsSUFBSTBCLFlBQVksS0FBSyxJQUFJLEVBQUU7WUFDdkIsT0FBT0Usb0JBQW9CLENBQUNxRSxJQUFJLEVBQUUsS0FBSyxFQUFFLENBQUM7UUFDOUMsQ0FBQztRQUVELDBCQUEwQjtRQUMxQixJQUFNQyxVQUFVLEdBQUd0RSxvQkFBb0IsS0FBS0YsWUFBWTtRQUN4RE4sT0FBTyxDQUFDbUQsR0FBRyxDQUFDLHdCQUFhLEVBQUU7WUFDdkIyQixVQUFVLEVBQVZBLFVBQVU7WUFDVkMsYUFBYSxFQUFFdkUsb0JBQW9CLENBQUMyRCxNQUFNO1lBQzFDYSxjQUFjLEVBQUUxRSxZQUFZLENBQUM2RCxNQUFNO1lBQ25DYyxXQUFXLEVBQUV6RSxvQkFBb0IsQ0FBQ3VELFVBQVUsQ0FBQyxHQUFHLENBQUMsSUFBSXpELFlBQVksQ0FBQ3lELFVBQVUsQ0FBQyxHQUFHLENBQUM7U0FDcEYsQ0FBQyxDQUFDO1FBQ0gsT0FBT2UsVUFBVSxDQUFDO0lBQ3RCLENBQUMsRUFBRTtRQUFDeEUsWUFBWTtRQUFFRSxvQkFBb0I7S0FBQyxDQUFDO0lBRXhDLGlDQUFpQztJQUNqQyxJQUFNMEUsY0FBYyxHQUFHdEcsa0RBQVcsQ0FBQyxXQUFNO1FBQ3JDLE9BQU87WUFDSGtHLFVBQVUsRUFBRUYsbUJBQW1CLEVBQUU7WUFDakNPLGNBQWMsRUFBRTNFLG9CQUFvQjtZQUNwQzRFLFFBQVEsRUFBRTlFLFlBQVk7WUFDdEIrRSxTQUFTLEVBQUUvRSxZQUFZLEtBQUssSUFBSTtTQUNuQyxDQUFDO0lBQ04sQ0FBQyxFQUFFO1FBQUNzRSxtQkFBbUI7UUFBRXBFLG9CQUFvQjtRQUFFRixZQUFZO0tBQUMsQ0FBQztJQUU3RCw0Q0FBNEM7SUFDNUMsSUFBTWdGLGtCQUFrQixHQUFHMUcsa0RBQVcsZUFBQywrRkFBOEI7WUFLekRtRCxLQUFLLEVBSUN3RCxVQUFVLEVBdUJmeEYsS0FBSzs7OztvQkEvQmQsSUFBSSxDQUFDTixDQUFBQSxJQUFJLGFBQUpBLElBQUksV0FBSSxHQUFSQSxLQUFBQSxDQUFRLEdBQVJBLElBQUksQ0FBRXNCLEVBQUUsR0FBRTs7d0JBQU8sS0FBSztzQkFBQzs7Ozs7Ozs7O29CQUt4QixJQUFJdEIsSUFBSSxhQUFKQSxJQUFJLFdBQWEsR0FBakJBLEtBQUFBLENBQWlCLEdBQWpCQSxJQUFJLENBQUUrRixXQUFXLEVBQUU7d0JBQ25CekQsS0FBSyxHQUFHdEMsSUFBSSxDQUFDc0MsS0FBSyxDQUFDO29CQUN2QixPQUFPO3dCQUNHd0QsVUFBVSxHQUFHRSxRQUFRLENBQUNDLGFBQWEsQ0FBQyxhQUFhLENBQUMsQ0FBd0I7d0JBQ2hGLElBQUlILFVBQVUsSUFBSUEsVUFBVSxDQUFDbEMsS0FBSyxFQUFFOzRCQUNoQ3RCLEtBQUssR0FBR3dELFVBQVUsQ0FBQ2xDLEtBQUssQ0FBQ3dCLElBQUksRUFBRSxDQUFDO3dCQUNwQyxPQUFPOzRCQUNILHVCQUF1Qjs0QkFDdkI5QyxLQUFLLEdBQUd0QyxDQUFBQSxJQUFJLGFBQUpBLElBQUksV0FBTyxHQUFYQSxLQUFBQSxDQUFXLEdBQVhBLElBQUksQ0FBRXNDLEtBQUssS0FBSSxVQUFVLENBQUM7d0JBQ3RDLENBQUM7b0JBQ0wsQ0FBQztvQkFFRCxxQkFBcUI7b0JBQ3JCOzt3QkFBTXJCLGVBQWUsQ0FBQzs0QkFDbEJpRCxPQUFPLEVBQUVuRCxvQkFBb0I7NEJBQzdCdUIsS0FBSyxFQUFMQSxLQUFLOzRCQUNMd0MsVUFBVSxFQUFFLElBQUlDLElBQUksRUFBRSxDQUFDQyxXQUFXLEVBQUU7eUJBQ3ZDLENBQUM7c0JBQUE7O29CQUpGLGFBSUUsQ0FBQztvQkFFSHpFLE9BQU8sQ0FBQ21ELEdBQUcsQ0FBQyxrQ0FBdUIsRUFBRTt3QkFDakNTLE1BQU0sRUFBRW5FLElBQUksQ0FBQ3NCLEVBQUU7d0JBQ2ZnQixLQUFLLEVBQUxBLEtBQUs7d0JBQ0xtQyxhQUFhLEVBQUUxRCxvQkFBb0IsQ0FBQzJELE1BQU07d0JBQzFDTCxZQUFZLEVBQUV0RCxvQkFBb0IsQ0FBQ3VELFVBQVUsQ0FBQyxHQUFHLENBQUM7cUJBQ3JELENBQUMsQ0FBQztvQkFDSDs7d0JBQU8sSUFBSTtzQkFBQzs7b0JBQ1BoRSxLQUFLO29CQUNWQyxPQUFPLENBQUNELEtBQUssQ0FBQyxrQ0FBdUIsRUFBRUEsS0FBSyxDQUFDLENBQUM7b0JBQzlDOzt3QkFBTyxLQUFLO3NCQUFDOzs7Ozs7O0lBRXJCLENBQUMsR0FBRTtRQUFDTixJQUFJO1FBQUVlLG9CQUFvQjtRQUFFRSxlQUFlO0tBQUMsQ0FBQztJQUVqRCxPQUFPO1FBQ0hvQixZQUFZLEVBQVpBLFlBQVk7UUFDWkcsWUFBWSxFQUFaQSxZQUFZO1FBQ1pFLFdBQVcsRUFBWEEsV0FBVztRQUNYTyxhQUFhLEVBQWJBLGFBQWE7UUFDYkksV0FBVyxFQUFYQSxXQUFXO1FBQ1hHLFlBQVksRUFBWkEsWUFBWTtRQUNabUIsY0FBYyxFQUFkQSxjQUFjO1FBQ2RFLGFBQWEsRUFBYkEsYUFBYTtRQUNiNUQsZUFBZSxFQUFmQSxlQUFlO1FBQ2ZRLFlBQVksRUFBWkEsWUFBWTtRQUNaNkIsU0FBUyxFQUFUQSxTQUFTO1FBQ1QxQyxRQUFRLEVBQVJBLFFBQVE7UUFDUlosSUFBSSxFQUFKQSxJQUFJO1FBQ0osZ0JBQWdCO1FBQ2hCeUYsY0FBYyxFQUFkQSxjQUFjO1FBQ2RJLGtCQUFrQixFQUFsQkEsa0JBQWtCO1FBQ2xCVixtQkFBbUIsRUFBbkJBLG1CQUFtQjtLQUN0QixDQUFDO0FBQ04sQ0FBQztBQUVELElBQU1lLGtCQUFrQixHQUFHeEcsK0RBQWUsQ0FBQ0ksZ0JBQWdCLENBQUM7QUFFNUQsK0RBQWVvRyxrQkFBa0IsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9saWJzL3dlYi9zdGF0ZS9sZXhpY2FsLWVkaXRvci50cz80M2Y4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBOb3RlU3RhdGUgZnJvbSAnbGlicy93ZWIvc3RhdGUvbm90ZSc7XG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L3JvdXRlcic7XG5pbXBvcnQge1xuICAgIHVzZUNhbGxiYWNrLFxuICAgIE1vdXNlRXZlbnQgYXMgUmVhY3RNb3VzZUV2ZW50LFxuICAgIHVzZVN0YXRlLFxuICAgIHVzZVJlZixcbiAgICB1c2VFZmZlY3QsXG59IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHNlYXJjaE5vdGUsIHNlYXJjaFJhbmdlVGV4dCB9IGZyb20gJ2xpYnMvd2ViL3V0aWxzL3NlYXJjaCc7XG5pbXBvcnQgeyBpc05vdGVMaW5rLCBOb3RlTW9kZWwgfSBmcm9tICdsaWJzL3NoYXJlZC9ub3RlJztcbmltcG9ydCB7IHVzZVRvYXN0IH0gZnJvbSAnbGlicy93ZWIvaG9va3MvdXNlLXRvYXN0JztcbmltcG9ydCBQb3J0YWxTdGF0ZSBmcm9tICdsaWJzL3dlYi9zdGF0ZS9wb3J0YWwnO1xuaW1wb3J0IHsgTm90ZUNhY2hlSXRlbSB9IGZyb20gJ2xpYnMvd2ViL2NhY2hlJztcbmltcG9ydCBub3RlQ2FjaGUgZnJvbSAnbGlicy93ZWIvY2FjaGUvbm90ZSc7XG5pbXBvcnQgeyBjcmVhdGVDb250YWluZXIgfSBmcm9tICd1bnN0YXRlZC1uZXh0JztcbmltcG9ydCB7IExleGljYWxFZGl0b3JSZWYgfSBmcm9tICdjb21wb25lbnRzL2VkaXRvci9sZXhpY2FsLWVkaXRvcic7XG5pbXBvcnQgVUlTdGF0ZSBmcm9tICdsaWJzL3dlYi9zdGF0ZS91aSc7XG5pbXBvcnQgeyBoYXMgfSBmcm9tICdsb2Rhc2gnO1xuaW1wb3J0IHsgcGFyc2VNYXJrZG93blRpdGxlIH0gZnJvbSAnbGlicy9zaGFyZWQvbWFya2Rvd24vcGFyc2UtbWFya2Rvd24tdGl0bGUnO1xuaW1wb3J0IHsgY3JlYXRlU21hcnRPbkNoYW5nZSB9IGZyb20gJ2xpYnMvd2ViL3V0aWxzL2ltZS1zdGF0ZS1tYW5hZ2VyJztcbmNvbnN0IFJPT1RfSUQgPSAncm9vdCc7XG5cbmNvbnN0IHVzZUxleGljYWxFZGl0b3IgPSAoaW5pdE5vdGU/OiBOb3RlTW9kZWwpID0+IHtcbiAgICAvLyBVc2UgaW5pdE5vdGUgaWYgcHJvdmlkZWQsIG90aGVyd2lzZSB0cnkgdG8gZ2V0IGZyb20gTm90ZVN0YXRlXG4gICAgbGV0IG5vdGUgPSBpbml0Tm90ZTtcbiAgICBsZXQgY3JlYXRlTm90ZVdpdGhUaXRsZTogYW55LCB1cGRhdGVOb3RlOiBhbnksIGNyZWF0ZU5vdGU6IGFueTtcblxuICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IG5vdGVTdGF0ZSA9IE5vdGVTdGF0ZS51c2VDb250YWluZXIoKTtcbiAgICAgICAgY3JlYXRlTm90ZVdpdGhUaXRsZSA9IG5vdGVTdGF0ZS5jcmVhdGVOb3RlV2l0aFRpdGxlO1xuICAgICAgICB1cGRhdGVOb3RlID0gbm90ZVN0YXRlLnVwZGF0ZU5vdGU7XG4gICAgICAgIGNyZWF0ZU5vdGUgPSBub3RlU3RhdGUuY3JlYXRlTm90ZTtcblxuICAgICAgICAvLyBPbmx5IHVzZSBub3RlU3RhdGUubm90ZSBpZiBubyBpbml0Tm90ZSBpcyBwcm92aWRlZFxuICAgICAgICBpZiAoIW5vdGUpIHtcbiAgICAgICAgICAgIG5vdGUgPSBub3RlU3RhdGUubm90ZTtcbiAgICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIC8vIElmIE5vdGVTdGF0ZSBpcyBub3QgYXZhaWxhYmxlLCB3ZSdsbCB3b3JrIHdpdGgganVzdCB0aGUgaW5pdE5vdGVcbiAgICAgICAgY29uc29sZS53YXJuKCdOb3RlU3RhdGUgbm90IGF2YWlsYWJsZSBpbiBMZXhpY2FsRWRpdG9yU3RhdGUsIHVzaW5nIGluaXROb3RlIG9ubHknKTtcbiAgICAgICAgY3JlYXRlTm90ZVdpdGhUaXRsZSA9IGFzeW5jICgpID0+IHVuZGVmaW5lZDtcbiAgICAgICAgdXBkYXRlTm90ZSA9IGFzeW5jICgpID0+IHVuZGVmaW5lZDtcbiAgICAgICAgY3JlYXRlTm90ZSA9IGFzeW5jICgpID0+IHVuZGVmaW5lZDtcbiAgICB9XG5cbiAgICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcbiAgICBjb25zdCB0b2FzdCA9IHVzZVRvYXN0KCk7XG4gICAgY29uc3QgZWRpdG9yRWwgPSB1c2VSZWY8TGV4aWNhbEVkaXRvclJlZj4obnVsbCk7XG5cbiAgICAvLyDwn5SnIOaWsOWinu+8muW/q+eFp+eKtuaAgeeuoeeQhlxuICAgIGNvbnN0IFtub3RlU25hcHNob3QsIHNldE5vdGVTbmFwc2hvdF0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgICBjb25zdCBbY3VycmVudEVkaXRvckNvbnRlbnQsIHNldEN1cnJlbnRFZGl0b3JDb250ZW50XSA9IHVzZVN0YXRlPHN0cmluZz4oJycpO1xuXG4gICAgLy8gTWFudWFsIHNhdmUgZnVuY3Rpb24gZm9yIEluZGV4ZWREQlxuICAgIGNvbnN0IHNhdmVUb0luZGV4ZWREQiA9IHVzZUNhbGxiYWNrKFxuICAgICAgICBhc3luYyAoZGF0YTogUGFydGlhbDxOb3RlTW9kZWw+KSA9PiB7XG4gICAgICAgICAgICBpZiAoIW5vdGU/LmlkKSByZXR1cm47XG5cbiAgICAgICAgICAgIC8vIOS7jiBJbmRleGVkREIg6I635Y+W5pyA5paw5pWw5o2u5L2c5Li65Z+656GA77yM6YG/5YWN6KaG55uW5bey5L+d5a2Y55qE5pWw5o2uXG4gICAgICAgICAgICBjb25zdCBleGlzdGluZ05vdGUgPSBhd2FpdCBub3RlQ2FjaGUuZ2V0SXRlbShub3RlLmlkKTtcbiAgICAgICAgICAgIGNvbnN0IGJhc2VOb3RlID0gZXhpc3RpbmdOb3RlIHx8IG5vdGU7XG5cbiAgICAgICAgICAgIGNvbnN0IHVwZGF0ZWROb3RlID0geyAuLi5iYXNlTm90ZSwgLi4uZGF0YSB9O1xuXG4gICAgICAgICAgICBhd2FpdCBub3RlQ2FjaGUuc2V0SXRlbShub3RlLmlkLCB1cGRhdGVkTm90ZSk7XG4gICAgICAgIH0sXG4gICAgICAgIFtub3RlXVxuICAgICk7XG5cbiAgICBjb25zdCBzeW5jVG9TZXJ2ZXIgPSB1c2VDYWxsYmFjayhcbiAgICAgICAgYXN5bmMgKCkgPT4ge1xuICAgICAgICAgICAgaWYgKCFub3RlPy5pZCkgcmV0dXJuIGZhbHNlO1xuXG4gICAgICAgICAgICBjb25zdCBpc05ldyA9IGhhcyhyb3V0ZXIucXVlcnksICduZXcnKTtcblxuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBjb25zdCBsb2NhbE5vdGUgPSBhd2FpdCBub3RlQ2FjaGUuZ2V0SXRlbShub3RlLmlkKTtcbiAgICAgICAgICAgICAgICBjb25zdCBub3RlVG9TYXZlID0gbG9jYWxOb3RlIHx8IG5vdGU7XG5cbiAgICAgICAgICAgICAgICBpZiAoaXNOZXcpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3Qgbm90ZURhdGEgPSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAuLi5ub3RlVG9TYXZlLFxuICAgICAgICAgICAgICAgICAgICAgICAgcGlkOiAocm91dGVyLnF1ZXJ5LnBpZCBhcyBzdHJpbmcpIHx8IFJPT1RfSURcbiAgICAgICAgICAgICAgICAgICAgfTtcblxuICAgICAgICAgICAgICAgICAgICBjb25zdCBpdGVtID0gYXdhaXQgY3JlYXRlTm90ZShub3RlRGF0YSk7XG5cbiAgICAgICAgICAgICAgICAgICAgaWYgKGl0ZW0pIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IG5vdGVVcmwgPSBgLyR7aXRlbS5pZH1gO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHJvdXRlci5hc1BhdGggIT09IG5vdGVVcmwpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhd2FpdCByb3V0ZXIucmVwbGFjZShub3RlVXJsLCB1bmRlZmluZWQsIHsgc2hhbGxvdzogdHJ1ZSB9KTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIHRvYXN0KCdOb3RlIHNhdmVkIHRvIHNlcnZlcicsICdzdWNjZXNzJyk7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHVwZGF0ZWROb3RlID0gYXdhaXQgdXBkYXRlTm90ZShub3RlVG9TYXZlKTtcblxuICAgICAgICAgICAgICAgICAgICBpZiAodXBkYXRlZE5vdGUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGF3YWl0IG5vdGVDYWNoZS5zZXRJdGVtKHVwZGF0ZWROb3RlLmlkLCB1cGRhdGVkTm90ZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICB0b2FzdCgnTm90ZSB1cGRhdGVkIG9uIHNlcnZlcicsICdzdWNjZXNzJyk7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAgICAgdG9hc3QoJ0ZhaWxlZCB0byBzYXZlIG5vdGUgdG8gc2VydmVyJywgJ2Vycm9yJyk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH0sXG4gICAgICAgIFtub3RlLCByb3V0ZXIsIGNyZWF0ZU5vdGUsIHVwZGF0ZU5vdGUsIHRvYXN0XVxuICAgICk7XG5cbiAgICBjb25zdCBvbkNyZWF0ZUxpbmsgPSB1c2VDYWxsYmFjayhcbiAgICAgICAgYXN5bmMgKHRpdGxlOiBzdHJpbmcpID0+IHtcbiAgICAgICAgICAgIGlmICghY3JlYXRlTm90ZVdpdGhUaXRsZSkgcmV0dXJuICcnO1xuXG4gICAgICAgICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBjcmVhdGVOb3RlV2l0aFRpdGxlKHRpdGxlKTtcbiAgICAgICAgICAgIGlmIChyZXN1bHQ/LmlkKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGAvJHtyZXN1bHQuaWR9YDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiAnJztcbiAgICAgICAgfSxcbiAgICAgICAgW2NyZWF0ZU5vdGVXaXRoVGl0bGVdXG4gICAgKTtcblxuICAgIGNvbnN0IG9uU2VhcmNoTGluayA9IHVzZUNhbGxiYWNrKFxuICAgICAgICBhc3luYyAodGVybTogc3RyaW5nKSA9PiB7XG4gICAgICAgICAgICByZXR1cm4gW107XG4gICAgICAgIH0sXG4gICAgICAgIFtdXG4gICAgKTtcblxuICAgIGNvbnN0IG9uQ2xpY2tMaW5rID0gdXNlQ2FsbGJhY2soXG4gICAgICAgIChocmVmOiBzdHJpbmcsIGV2ZW50OiBSZWFjdE1vdXNlRXZlbnQpID0+IHtcbiAgICAgICAgICAgIGlmIChpc05vdGVMaW5rKGhyZWYpKSB7XG4gICAgICAgICAgICAgICAgZXZlbnQucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgICAgICAgICByb3V0ZXIucHVzaChocmVmKTtcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgd2luZG93Lm9wZW4oaHJlZiwgJ19ibGFuaycsICdub29wZW5lcixub3JlZmVycmVyJyk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIFtyb3V0ZXJdXG4gICAgKTtcblxuICAgIGNvbnN0IG9uVXBsb2FkSW1hZ2UgPSB1c2VDYWxsYmFjayhcbiAgICAgICAgYXN5bmMgKF9maWxlOiBGaWxlLCBfaWQ/OiBzdHJpbmcpID0+IHtcbiAgICAgICAgICAgIC8vIEltYWdlIHVwbG9hZCBpcyBkaXNhYmxlZCBpbiBQb3N0Z3JlU1FMIHZlcnNpb25cbiAgICAgICAgICAgIHRvYXN0KCdJbWFnZSB1cGxvYWQgaXMgbm90IHN1cHBvcnRlZCBpbiB0aGlzIHZlcnNpb24nLCAnZXJyb3InKTtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignSW1hZ2UgdXBsb2FkIGlzIG5vdCBzdXBwb3J0ZWQnKTtcbiAgICAgICAgfSxcbiAgICAgICAgW3RvYXN0XVxuICAgICk7XG5cbiAgICBjb25zdCBvbkhvdmVyTGluayA9IHVzZUNhbGxiYWNrKChldmVudDogUmVhY3RNb3VzZUV2ZW50KSA9PiB7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgIH0sIFtdKTtcblxuICAgIGNvbnN0IFtiYWNrbGlua3MsIHNldEJhY2tMaW5rc10gPSB1c2VTdGF0ZTxOb3RlQ2FjaGVJdGVtW10+KCk7XG5cbiAgICBjb25zdCBnZXRCYWNrTGlua3MgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgICAgIGNvbnNvbGUubG9nKG5vdGU/LmlkKTtcbiAgICAgICAgY29uc3QgbGlua05vdGVzOiBOb3RlQ2FjaGVJdGVtW10gPSBbXTtcbiAgICAgICAgaWYgKCFub3RlPy5pZCkgcmV0dXJuIGxpbmtOb3RlcztcbiAgICAgICAgc2V0QmFja0xpbmtzKFtdKTtcbiAgICAgICAgYXdhaXQgbm90ZUNhY2hlLml0ZXJhdGU8Tm90ZUNhY2hlSXRlbSwgdm9pZD4oKHZhbHVlKSA9PiB7XG4gICAgICAgICAgICBpZiAodmFsdWUubGlua0lkcz8uaW5jbHVkZXMobm90ZT8uaWQgfHwgJycpKSB7XG4gICAgICAgICAgICAgICAgbGlua05vdGVzLnB1c2godmFsdWUpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICAgICAgc2V0QmFja0xpbmtzKGxpbmtOb3Rlcyk7XG4gICAgfSwgW25vdGU/LmlkXSk7XG5cbiAgICAvLyDwn5SnIOW/q+eFp+WIneWni+WMlumAu+i+kSAtIOaJk+W8gOeslOiusOaXtuiuvue9rkpTT07lv6vnhadcbiAgICBjb25zdCBpbml0aWFsaXplU25hcHNob3QgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgICAgIGlmICghbm90ZT8uaWQpIHtcbiAgICAgICAgICAgIC8vIOaWsOW7uueslOiusO+8muW/q+eFp+S4uuepuuWAvFxuICAgICAgICAgICAgc2V0Tm90ZVNuYXBzaG90KG51bGwpO1xuICAgICAgICAgICAgc2V0Q3VycmVudEVkaXRvckNvbnRlbnQoJycpO1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ/CflKcg5paw5bu656yU6K6w77yaSlNPTuW/q+eFp+iuvue9ruS4uuepuuWAvCcpO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG5cbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIC8vIOW3suWtmOWcqOeslOiusO+8muS7jue8k+WtmOiOt+WPlkpTT07lv6vnhadcbiAgICAgICAgICAgIGNvbnN0IGNhY2hlZE5vdGUgPSBhd2FpdCBub3RlQ2FjaGUuZ2V0SXRlbShub3RlLmlkKTtcbiAgICAgICAgICAgIGNvbnN0IHNuYXBzaG90SnNvbkNvbnRlbnQgPSBjYWNoZWROb3RlPy5jb250ZW50IHx8ICcnO1xuICAgICAgICAgICAgc2V0Tm90ZVNuYXBzaG90KHNuYXBzaG90SnNvbkNvbnRlbnQpO1xuICAgICAgICAgICAgc2V0Q3VycmVudEVkaXRvckNvbnRlbnQoc25hcHNob3RKc29uQ29udGVudCk7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn8J+UpyDlt7LlrZjlnKjnrJTorrDvvJpKU09O5b+r54Wn6K6+572u5Li657yT5a2Y5YaF5a65Jywge1xuICAgICAgICAgICAgICAgIG5vdGVJZDogbm90ZS5pZCxcbiAgICAgICAgICAgICAgICBoYXNTbmFwc2hvdDogISFzbmFwc2hvdEpzb25Db250ZW50LFxuICAgICAgICAgICAgICAgIGlzSnNvbkZvcm1hdDogc25hcHNob3RKc29uQ29udGVudC5zdGFydHNXaXRoKCd7JylcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcign8J+UpyBKU09O5b+r54Wn5Yid5aeL5YyW5aSx6LSlOicsIGVycm9yKTtcbiAgICAgICAgICAgIC8vIOWksei0peaXtuiuvue9ruS4uuepuuW/q+eFp1xuICAgICAgICAgICAgc2V0Tm90ZVNuYXBzaG90KG51bGwpO1xuICAgICAgICAgICAgc2V0Q3VycmVudEVkaXRvckNvbnRlbnQoJycpO1xuICAgICAgICB9XG4gICAgfSwgW25vdGU/LmlkXSk7XG5cbiAgICAvLyDlvZPnrJTorrBJROWPmOWMluaXtuWIneWni+WMluW/q+eFp1xuICAgIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIGluaXRpYWxpemVTbmFwc2hvdCgpO1xuICAgIH0sIFtpbml0aWFsaXplU25hcHNob3RdKTtcblxuICAgIC8vIPCflKcg5L+u5aSN77ya5oGi5aSN57yW6L6R5Zmo5Z+65pys5Yqf6IO9IC0g5pu05pawSlNPTuWGheWuueeKtuaAgVxuICAgIGNvbnN0IG9yaWdpbmFsT25FZGl0b3JDaGFuZ2UgPSB1c2VDYWxsYmFjayhcbiAgICAgICAgKHZhbHVlOiAoKSA9PiBzdHJpbmcpOiB2b2lkID0+IHtcbiAgICAgICAgICAgIGNvbnN0IGpzb25Db250ZW50ID0gdmFsdWUoKTsgLy8g6L+Z6YeM5pivSlNPTuagvOW8j+eahOWGheWuuVxuXG4gICAgICAgICAgICAvLyDmm7TmlrDlvZPliY3nvJbovpHlmahKU09O5YaF5a6554q25oCBXG4gICAgICAgICAgICBzZXRDdXJyZW50RWRpdG9yQ29udGVudChqc29uQ29udGVudCk7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn8J+UpyDnvJbovpHlmahKU09O5YaF5a655pu05pawOicsIHsgY29udGVudExlbmd0aDoganNvbkNvbnRlbnQubGVuZ3RoIH0pO1xuXG4gICAgICAgICAgICAvLyDkv53lrZjpgLvovpHnjrDlnKjlrozlhajnlLFTYXZlQnV0dG9u55qE5b+r54Wn5a+55q+U5py65Yi25aSE55CGXG4gICAgICAgIH0sXG4gICAgICAgIFtdXG4gICAgKTtcblxuICAgIC8vIOS9v+eUqOaZuuiDvW9uQ2hhbmdl5YyF6KOF5ZmoIC0g5Z+65LqO6L6T5YWl54q25oCB5pm66IO95aSE55CGXG4gICAgY29uc3Qgb25FZGl0b3JDaGFuZ2UgPSBjcmVhdGVTbWFydE9uQ2hhbmdlKG9yaWdpbmFsT25FZGl0b3JDaGFuZ2UsIHtcbiAgICAgICAgZGVsYXk6IDIwMCAvLyDlv6vpgJ/ovpPlhaXnu5PmnZ/lkI4yMDBtc+aJp+ihjFxuICAgIH0pO1xuXG4gICAgLy8gRnVuY3Rpb24gdG8gaGFuZGxlIHRpdGxlIGNoYW5nZXMgc3BlY2lmaWNhbGx5XG4gICAgY29uc3Qgb25UaXRsZUNoYW5nZSA9IHVzZUNhbGxiYWNrKFxuICAgICAgICAodGl0bGU6IHN0cmluZyk6IHZvaWQgPT4ge1xuICAgICAgICAgICAgc2F2ZVRvSW5kZXhlZERCKHtcbiAgICAgICAgICAgICAgICB0aXRsZSxcbiAgICAgICAgICAgICAgICB1cGRhdGVkX2F0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKClcbiAgICAgICAgICAgIH0pPy5jYXRjaCgodikgPT4gY29uc29sZS5lcnJvcignRXJyb3Igd2hpbHN0IHNhdmluZyB0aXRsZSB0byBJbmRleGVkREI6ICVPJywgdikpO1xuICAgICAgICB9LFxuICAgICAgICBbc2F2ZVRvSW5kZXhlZERCXVxuICAgICk7XG5cbiAgICAvLyDwn5SnIOS/ruWkje+8mkpTT07lv6vnhaflr7nmr5Tlip/og70gLSDkvptTYXZlQnV0dG9u5L2/55SoXG4gICAgY29uc3QgY29tcGFyZVdpdGhTbmFwc2hvdCA9IHVzZUNhbGxiYWNrKCgpOiBib29sZWFuID0+IHtcbiAgICAgICAgLy8g5aaC5p6c5piv5paw5bu656yU6K6w77yI5b+r54Wn5Li6bnVsbO+8ie+8jOS7u+S9lUpTT07lhoXlrrnpg73nrpfkvZzlj5jljJZcbiAgICAgICAgaWYgKG5vdGVTbmFwc2hvdCA9PT0gbnVsbCkge1xuICAgICAgICAgICAgcmV0dXJuIGN1cnJlbnRFZGl0b3JDb250ZW50LnRyaW0oKSAhPT0gJyc7XG4gICAgICAgIH1cblxuICAgICAgICAvLyDlt7LlrZjlnKjnrJTorrDvvJrmr5TovoPlvZPliY1KU09O5YaF5a655LiOSlNPTuW/q+eFp1xuICAgICAgICBjb25zdCBoYXNDaGFuZ2VzID0gY3VycmVudEVkaXRvckNvbnRlbnQgIT09IG5vdGVTbmFwc2hvdDtcbiAgICAgICAgY29uc29sZS5sb2coJ/CflKcgSlNPTuW/q+eFp+WvueavlDonLCB7XG4gICAgICAgICAgICBoYXNDaGFuZ2VzLFxuICAgICAgICAgICAgY3VycmVudExlbmd0aDogY3VycmVudEVkaXRvckNvbnRlbnQubGVuZ3RoLFxuICAgICAgICAgICAgc25hcHNob3RMZW5ndGg6IG5vdGVTbmFwc2hvdC5sZW5ndGgsXG4gICAgICAgICAgICBib3RoQXJlSnNvbjogY3VycmVudEVkaXRvckNvbnRlbnQuc3RhcnRzV2l0aCgneycpICYmIG5vdGVTbmFwc2hvdC5zdGFydHNXaXRoKCd7JylcbiAgICAgICAgfSk7XG4gICAgICAgIHJldHVybiBoYXNDaGFuZ2VzO1xuICAgIH0sIFtub3RlU25hcHNob3QsIGN1cnJlbnRFZGl0b3JDb250ZW50XSk7XG5cbiAgICAvLyDwn5SnIOaWsOWinu+8muiOt+WPluW9k+WJjee8lui+keWZqOeKtuaAgSAtIOS+m1NhdmVCdXR0b27kvb/nlKhcbiAgICBjb25zdCBnZXRFZGl0b3JTdGF0ZSA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGhhc0NoYW5nZXM6IGNvbXBhcmVXaXRoU25hcHNob3QoKSxcbiAgICAgICAgICAgIGN1cnJlbnRDb250ZW50OiBjdXJyZW50RWRpdG9yQ29udGVudCxcbiAgICAgICAgICAgIHNuYXBzaG90OiBub3RlU25hcHNob3QsXG4gICAgICAgICAgICBpc05ld05vdGU6IG5vdGVTbmFwc2hvdCA9PT0gbnVsbFxuICAgICAgICB9O1xuICAgIH0sIFtjb21wYXJlV2l0aFNuYXBzaG90LCBjdXJyZW50RWRpdG9yQ29udGVudCwgbm90ZVNuYXBzaG90XSk7XG5cbiAgICAvLyDwn5SnIOS/ruWkje+8muS/neWtmOW9k+WJjUpTT07lhoXlrrnliLBJbmRleGVkREIgLSDkvptTYXZlQnV0dG9u6LCD55SoXG4gICAgY29uc3Qgc2F2ZUN1cnJlbnRDb250ZW50ID0gdXNlQ2FsbGJhY2soYXN5bmMgKCk6IFByb21pc2U8Ym9vbGVhbj4gPT4ge1xuICAgICAgICBpZiAoIW5vdGU/LmlkKSByZXR1cm4gZmFsc2U7XG5cbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIC8vIOiOt+WPluagh+mimFxuICAgICAgICAgICAgbGV0IHRpdGxlOiBzdHJpbmc7XG4gICAgICAgICAgICBpZiAobm90ZT8uaXNEYWlseU5vdGUpIHtcbiAgICAgICAgICAgICAgICB0aXRsZSA9IG5vdGUudGl0bGU7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIGNvbnN0IHRpdGxlSW5wdXQgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCdoMSB0ZXh0YXJlYScpIGFzIEhUTUxUZXh0QXJlYUVsZW1lbnQ7XG4gICAgICAgICAgICAgICAgaWYgKHRpdGxlSW5wdXQgJiYgdGl0bGVJbnB1dC52YWx1ZSkge1xuICAgICAgICAgICAgICAgICAgICB0aXRsZSA9IHRpdGxlSW5wdXQudmFsdWUudHJpbSgpO1xuICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIOWvueS6jkpTT07moLzlvI/vvIzkvb/nlKjnjrDmnInmoIfpopjmiJbpu5jorqTmoIfpophcbiAgICAgICAgICAgICAgICAgICAgdGl0bGUgPSBub3RlPy50aXRsZSB8fCAnVW50aXRsZWQnO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgLy8g5L+d5a2YSlNPTuWGheWuueWIsEluZGV4ZWREQlxuICAgICAgICAgICAgYXdhaXQgc2F2ZVRvSW5kZXhlZERCKHtcbiAgICAgICAgICAgICAgICBjb250ZW50OiBjdXJyZW50RWRpdG9yQ29udGVudCwgLy8g6L+Z6YeM5pivSlNPTuagvOW8j1xuICAgICAgICAgICAgICAgIHRpdGxlLFxuICAgICAgICAgICAgICAgIHVwZGF0ZWRfYXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgICAgfSk7XG5cbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SnIEpTT07lhoXlrrnlt7Lkv53lrZjliLBJbmRleGVkREInLCB7XG4gICAgICAgICAgICAgICAgbm90ZUlkOiBub3RlLmlkLFxuICAgICAgICAgICAgICAgIHRpdGxlLFxuICAgICAgICAgICAgICAgIGNvbnRlbnRMZW5ndGg6IGN1cnJlbnRFZGl0b3JDb250ZW50Lmxlbmd0aCxcbiAgICAgICAgICAgICAgICBpc0pzb25Gb3JtYXQ6IGN1cnJlbnRFZGl0b3JDb250ZW50LnN0YXJ0c1dpdGgoJ3snKVxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ/CflKcg5L+d5a2YSlNPTuWIsEluZGV4ZWREQuWksei0pTonLCBlcnJvcik7XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cbiAgICB9LCBbbm90ZSwgY3VycmVudEVkaXRvckNvbnRlbnQsIHNhdmVUb0luZGV4ZWREQl0pO1xuXG4gICAgcmV0dXJuIHtcbiAgICAgICAgb25DcmVhdGVMaW5rLFxuICAgICAgICBvblNlYXJjaExpbmssXG4gICAgICAgIG9uQ2xpY2tMaW5rLFxuICAgICAgICBvblVwbG9hZEltYWdlLFxuICAgICAgICBvbkhvdmVyTGluayxcbiAgICAgICAgZ2V0QmFja0xpbmtzLFxuICAgICAgICBvbkVkaXRvckNoYW5nZSxcbiAgICAgICAgb25UaXRsZUNoYW5nZSxcbiAgICAgICAgc2F2ZVRvSW5kZXhlZERCLFxuICAgICAgICBzeW5jVG9TZXJ2ZXIsXG4gICAgICAgIGJhY2tsaW5rcyxcbiAgICAgICAgZWRpdG9yRWwsXG4gICAgICAgIG5vdGUsXG4gICAgICAgIC8vIPCflKcg5paw5aKe77ya5b+r54Wn5a+55q+U55u45YWz5Yqf6IO9XG4gICAgICAgIGdldEVkaXRvclN0YXRlLFxuICAgICAgICBzYXZlQ3VycmVudENvbnRlbnQsXG4gICAgICAgIGNvbXBhcmVXaXRoU25hcHNob3QsXG4gICAgfTtcbn07XG5cbmNvbnN0IExleGljYWxFZGl0b3JTdGF0ZSA9IGNyZWF0ZUNvbnRhaW5lcih1c2VMZXhpY2FsRWRpdG9yKTtcblxuZXhwb3J0IGRlZmF1bHQgTGV4aWNhbEVkaXRvclN0YXRlO1xuIl0sIm5hbWVzIjpbIk5vdGVTdGF0ZSIsInVzZVJvdXRlciIsInVzZUNhbGxiYWNrIiwidXNlU3RhdGUiLCJ1c2VSZWYiLCJ1c2VFZmZlY3QiLCJpc05vdGVMaW5rIiwidXNlVG9hc3QiLCJub3RlQ2FjaGUiLCJjcmVhdGVDb250YWluZXIiLCJoYXMiLCJjcmVhdGVTbWFydE9uQ2hhbmdlIiwiUk9PVF9JRCIsInVzZUxleGljYWxFZGl0b3IiLCJpbml0Tm90ZSIsIm5vdGUiLCJjcmVhdGVOb3RlV2l0aFRpdGxlIiwidXBkYXRlTm90ZSIsImNyZWF0ZU5vdGUiLCJub3RlU3RhdGUiLCJ1c2VDb250YWluZXIiLCJlcnJvciIsImNvbnNvbGUiLCJ3YXJuIiwidW5kZWZpbmVkIiwicm91dGVyIiwidG9hc3QiLCJlZGl0b3JFbCIsIm5vdGVTbmFwc2hvdCIsInNldE5vdGVTbmFwc2hvdCIsImN1cnJlbnRFZGl0b3JDb250ZW50Iiwic2V0Q3VycmVudEVkaXRvckNvbnRlbnQiLCJzYXZlVG9JbmRleGVkREIiLCJkYXRhIiwiZXhpc3RpbmdOb3RlIiwiYmFzZU5vdGUiLCJ1cGRhdGVkTm90ZSIsImlkIiwiZ2V0SXRlbSIsInNldEl0ZW0iLCJzeW5jVG9TZXJ2ZXIiLCJpc05ldyIsImxvY2FsTm90ZSIsIm5vdGVUb1NhdmUiLCJub3RlRGF0YSIsIml0ZW0iLCJub3RlVXJsIiwicXVlcnkiLCJwaWQiLCJhc1BhdGgiLCJyZXBsYWNlIiwic2hhbGxvdyIsIm9uQ3JlYXRlTGluayIsInRpdGxlIiwicmVzdWx0Iiwib25TZWFyY2hMaW5rIiwidGVybSIsIm9uQ2xpY2tMaW5rIiwiaHJlZiIsImV2ZW50IiwicHJldmVudERlZmF1bHQiLCJwdXNoIiwid2luZG93Iiwib3BlbiIsIm9uVXBsb2FkSW1hZ2UiLCJfZmlsZSIsIl9pZCIsIkVycm9yIiwib25Ib3ZlckxpbmsiLCJiYWNrbGlua3MiLCJzZXRCYWNrTGlua3MiLCJnZXRCYWNrTGlua3MiLCJsaW5rTm90ZXMiLCJsb2ciLCJpdGVyYXRlIiwidmFsdWUiLCJsaW5rSWRzIiwiaW5jbHVkZXMiLCJpbml0aWFsaXplU25hcHNob3QiLCJjYWNoZWROb3RlIiwic25hcHNob3RKc29uQ29udGVudCIsImNvbnRlbnQiLCJub3RlSWQiLCJoYXNTbmFwc2hvdCIsImlzSnNvbkZvcm1hdCIsInN0YXJ0c1dpdGgiLCJvcmlnaW5hbE9uRWRpdG9yQ2hhbmdlIiwianNvbkNvbnRlbnQiLCJjb250ZW50TGVuZ3RoIiwibGVuZ3RoIiwib25FZGl0b3JDaGFuZ2UiLCJkZWxheSIsIm9uVGl0bGVDaGFuZ2UiLCJ1cGRhdGVkX2F0IiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwiY2F0Y2giLCJ2IiwiY29tcGFyZVdpdGhTbmFwc2hvdCIsInRyaW0iLCJoYXNDaGFuZ2VzIiwiY3VycmVudExlbmd0aCIsInNuYXBzaG90TGVuZ3RoIiwiYm90aEFyZUpzb24iLCJnZXRFZGl0b3JTdGF0ZSIsImN1cnJlbnRDb250ZW50Iiwic25hcHNob3QiLCJpc05ld05vdGUiLCJzYXZlQ3VycmVudENvbnRlbnQiLCJ0aXRsZUlucHV0IiwiaXNEYWlseU5vdGUiLCJkb2N1bWVudCIsInF1ZXJ5U2VsZWN0b3IiLCJMZXhpY2FsRWRpdG9yU3RhdGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./libs/web/state/lexical-editor.ts\n"));

/***/ })

});