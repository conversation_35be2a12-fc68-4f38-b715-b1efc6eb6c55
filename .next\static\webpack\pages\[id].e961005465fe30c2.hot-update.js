"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./components/editor/plugins/floating-toolbar-plugin.tsx":
/*!***************************************************************!*\
  !*** ./components/editor/plugins/floating-toolbar-plugin.tsx ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FloatingToolbarPlugin; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/src/_sliced_to_array.mjs */ \"./node_modules/@swc/helpers/src/_sliced_to_array.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"./node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lexical */ \"./node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var _lexical_link__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @lexical/link */ \"./node_modules/@lexical/link/LexicalLink.dev.mjs\");\n/* harmony import */ var _lexical_list__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @lexical/list */ \"./node_modules/@lexical/list/LexicalList.dev.mjs\");\n/* harmony import */ var _highlight_plugin__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./highlight-plugin */ \"./components/editor/plugins/highlight-plugin.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-dom */ \"./node_modules/react-dom/index.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-themes */ \"./node_modules/next-themes/dist/index.modern.js\");\n/* harmony import */ var _heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @heroicons/react/outline */ \"./node_modules/@heroicons/react/outline/esm/index.js\");\n/**\n * Floating Toolbar Plugin for Lexical\n * Shows formatting options when text is selected\n */ \n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Heroicons\n\nfunction FloatingToolbarPlugin() {\n    var _this = this;\n    _s();\n    var ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_5__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_6__.useLexicalComposerContext)(), 1), editor = ref[0];\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isVisible = ref1[0], setIsVisible = ref1[1];\n    var ref2 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        top: 0,\n        left: 0\n    }), position = ref2[0], setPosition = ref2[1];\n    var ref3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isBold = ref3[0], setIsBold = ref3[1];\n    var ref4 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isUnderline = ref4[0], setIsUnderline = ref4[1];\n    var ref5 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isStrikethrough = ref5[0], setIsStrikethrough = ref5[1];\n    var ref6 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isCode = ref6[0], setIsCode = ref6[1];\n    var ref7 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isLink = ref7[0], setIsLink = ref7[1];\n    var ref8 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isHighlight = ref8[0], setIsHighlight = ref8[1];\n    var theme = (0,next_themes__WEBPACK_IMPORTED_MODULE_4__.useTheme)().theme;\n    var updateToolbar = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var selection = (0,lexical__WEBPACK_IMPORTED_MODULE_7__.$getSelection)();\n        if ((0,lexical__WEBPACK_IMPORTED_MODULE_7__.$isRangeSelection)(selection)) {\n            var anchorNode = selection.anchor.getNode();\n            var element = anchorNode.getKey() === \"root\" ? anchorNode : anchorNode.getTopLevelElementOrThrow();\n            var elementKey = element.getKey();\n            var elementDOM = editor.getElementByKey(elementKey);\n            // 显示条件：只有当有选中文本时才显示\n            var hasSelection = selection.getTextContent() !== \"\";\n            if (elementDOM !== null && hasSelection) {\n                var nativeSelection = window.getSelection();\n                var rootElement = editor.getRootElement();\n                if (nativeSelection !== null && rootElement !== null && rootElement.contains(nativeSelection.anchorNode)) {\n                    var rangeRect = nativeSelection.getRangeAt(0).getBoundingClientRect();\n                    setPosition({\n                        top: rangeRect.top - 60,\n                        left: rangeRect.left + rangeRect.width / 2 - 150\n                    });\n                    setIsVisible(true);\n                    // Update button states\n                    setIsBold(selection.hasFormat(\"bold\"));\n                    setIsUnderline(selection.hasFormat(\"underline\"));\n                    setIsStrikethrough(selection.hasFormat(\"strikethrough\"));\n                    setIsCode(selection.hasFormat(\"code\"));\n                    // Check if selection contains a link\n                    var node = selection.anchor.getNode();\n                    var parent = node.getParent();\n                    setIsLink((0,_lexical_link__WEBPACK_IMPORTED_MODULE_8__.$isLinkNode)(parent) || (0,_lexical_link__WEBPACK_IMPORTED_MODULE_8__.$isLinkNode)(node));\n                    // Check for highlight using Lexical's built-in format\n                    setIsHighlight(selection.hasFormat(\"highlight\"));\n                } else {\n                    setIsVisible(false);\n                }\n            } else {\n                setIsVisible(false);\n            }\n        } else {\n            setIsVisible(false);\n        }\n    }, [\n        editor\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n        return editor.registerUpdateListener(function(param) {\n            var editorState = param.editorState;\n            editorState.read(function() {\n                updateToolbar();\n            });\n        });\n    }, [\n        editor,\n        updateToolbar\n    ]);\n    var handleFormat = function(format) {\n        editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_7__.FORMAT_TEXT_COMMAND, format);\n    };\n    var handleLink = function() {\n        if (isLink) {\n            editor.dispatchCommand(_lexical_link__WEBPACK_IMPORTED_MODULE_8__.TOGGLE_LINK_COMMAND, null);\n        } else {\n            var url = prompt(\"Enter URL:\");\n            if (url) {\n                editor.dispatchCommand(_lexical_link__WEBPACK_IMPORTED_MODULE_8__.TOGGLE_LINK_COMMAND, url);\n            }\n        }\n    };\n    if (!isVisible) {\n        return null;\n    }\n    var toolbarBg = theme === \"dark\" ? \"border-gray-600\" : \"border-gray-200\";\n    var buttonText = theme === \"dark\" ? \"text-white\" : \"text-gray-700\";\n    var buttonHover = theme === \"dark\" ? \"hover:text-white\" : \"hover:text-gray-900\";\n    var buttonActive = theme === \"dark\" ? \"text-white\" : \"text-gray-900\";\n    var toolbarButtons = [\n        {\n            title: \"Bold\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"font-bold text-sm\",\n                children: \"B\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 139,\n                columnNumber: 19\n            }, this),\n            isActive: isBold,\n            action: function() {\n                return handleFormat(\"bold\");\n            }\n        },\n        {\n            title: \"Strikethrough\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"line-through text-sm\",\n                children: \"S\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 145,\n                columnNumber: 19\n            }, this),\n            isActive: isStrikethrough,\n            action: function() {\n                return handleFormat(\"strikethrough\");\n            }\n        },\n        {\n            title: \"Underline\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"underline text-sm\",\n                children: \"U\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 151,\n                columnNumber: 19\n            }, this),\n            isActive: isUnderline,\n            action: function() {\n                return handleFormat(\"underline\");\n            }\n        },\n        {\n            title: \"Highlight\",\n            icon: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-xs px-1 rounded text-white\",\n                style: {\n                    backgroundColor: \"#3185eb\"\n                },\n                children: \"H\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 158,\n                columnNumber: 19\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-xs px-1 rounded\",\n                style: {\n                    backgroundColor: \"#eab834\"\n                },\n                children: \"H\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 159,\n                columnNumber: 19\n            }, this),\n            isActive: isHighlight,\n            action: function() {\n                return editor.dispatchCommand(_highlight_plugin__WEBPACK_IMPORTED_MODULE_1__.TOGGLE_HIGHLIGHT_COMMAND, undefined);\n            }\n        },\n        {\n            title: \"Code\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__.CodeIcon, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 165,\n                columnNumber: 19\n            }, this),\n            isActive: isCode,\n            action: function() {\n                return handleFormat(\"code\");\n            }\n        },\n        {\n            title: isLink ? \"Remove Link\" : \"Add Link\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__.LinkIcon, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 171,\n                columnNumber: 19\n            }, this),\n            isActive: isLink,\n            action: handleLink\n        },\n        // 分隔符\n        {\n            type: \"separator\"\n        },\n        {\n            title: \"Checklist\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm\",\n                children: \"☑\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 179,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: function() {\n                return editor.dispatchCommand(_lexical_list__WEBPACK_IMPORTED_MODULE_10__.INSERT_CHECK_LIST_COMMAND, undefined);\n            }\n        },\n        {\n            title: \"Bullet List\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm\",\n                children: \"•\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 185,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: function() {\n                return editor.dispatchCommand(_lexical_list__WEBPACK_IMPORTED_MODULE_10__.INSERT_UNORDERED_LIST_COMMAND, undefined);\n            }\n        },\n        {\n            title: \"Numbered List\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm\",\n                children: \"1.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 191,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: function() {\n                return editor.dispatchCommand(_lexical_list__WEBPACK_IMPORTED_MODULE_10__.INSERT_ORDERED_LIST_COMMAND, undefined);\n            }\n        },\n        // 分隔符\n        {\n            type: \"separator\"\n        },\n        {\n            title: \"Indent\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__.ArrowRightIcon, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 199,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: function() {\n                editor.update(function() {\n                    var selection = (0,lexical__WEBPACK_IMPORTED_MODULE_7__.$getSelection)();\n                    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_7__.$isRangeSelection)(selection)) {\n                        return;\n                    }\n                    var nodes = selection.getNodes();\n                    var listItemsToIndent = [];\n                    // 收集所有需要缩进的列表项\n                    nodes.forEach(function(node) {\n                        var listItemNode = null;\n                        if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$isListItemNode)(node)) {\n                            listItemNode = node;\n                        } else if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$isListItemNode)(node.getParent())) {\n                            listItemNode = node.getParent();\n                        }\n                        if (listItemNode && !listItemsToIndent.includes(listItemNode)) {\n                            listItemsToIndent.push(listItemNode);\n                        }\n                    });\n                    if (listItemsToIndent.length > 0) {\n                        // 按文档顺序排序列表项\n                        listItemsToIndent.sort(function(a, b) {\n                            var aIndex = a.getIndexWithinParent();\n                            var bIndex = b.getIndexWithinParent();\n                            return aIndex - bIndex;\n                        });\n                        // 处理每个列表项的缩进\n                        listItemsToIndent.forEach(function(listItemNode) {\n                            // 找到前一个兄弟节点作为父系\n                            var previousSibling = listItemNode.getPreviousSibling();\n                            if (previousSibling && (0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$isListItemNode)(previousSibling)) {\n                                // 检查前一个兄弟是否已经有子列表\n                                var nestedList = null;\n                                var children = previousSibling.getChildren();\n                                var _iteratorNormalCompletion = true, _didIteratorError = false, _iteratorError = undefined;\n                                try {\n                                    // 查找是否已经有子列表\n                                    for(var _iterator = children[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true){\n                                        var child = _step.value;\n                                        if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$isListNode)(child)) {\n                                            nestedList = child;\n                                            break;\n                                        }\n                                    }\n                                } catch (err) {\n                                    _didIteratorError = true;\n                                    _iteratorError = err;\n                                } finally{\n                                    try {\n                                        if (!_iteratorNormalCompletion && _iterator.return != null) {\n                                            _iterator.return();\n                                        }\n                                    } finally{\n                                        if (_didIteratorError) {\n                                            throw _iteratorError;\n                                        }\n                                    }\n                                }\n                                // 如果没有子列表，创建一个新的无序子列表\n                                if (!nestedList) {\n                                    nestedList = (0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$createListNode)(\"bullet\");\n                                    previousSibling.append(nestedList);\n                                }\n                                // 将当前列表项移动到子列表中\n                                listItemNode.remove();\n                                nestedList.append(listItemNode);\n                            }\n                        });\n                    } else {\n                        // 如果没有列表项，使用通用缩进\n                        editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_7__.INDENT_CONTENT_COMMAND, undefined);\n                    }\n                });\n            }\n        },\n        {\n            title: \"Outdent\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__.ArrowLeftIcon, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 272,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: function() {\n                // 暂时使用简单的反缩进逻辑，确保列表符号正常显示\n                editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_7__.OUTDENT_CONTENT_COMMAND, undefined);\n            }\n        }, \n    ];\n    return /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_3__.createPortal)(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed z-50 \".concat(toolbarBg, \" border rounded-lg p-1.5 flex space-x-0.5 shadow-lg\"),\n        style: {\n            top: position.top,\n            left: position.left,\n            transform: \"translateX(-50%)\",\n            backgroundColor: theme === \"dark\" ? \"#3f3f46\" : \"#e4e4e7\"\n        },\n        children: toolbarButtons.map(function(button, index) {\n            if (button.type === \"separator\") {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-px h-6 \".concat(theme === \"dark\" ? \"bg-gray-600\" : \"bg-gray-300\", \" mx-1\")\n                }, index, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 25\n                }, _this);\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: button.action,\n                title: button.title,\n                className: \"\\n                            px-2.5 py-1.5 rounded transition-colors duration-150 min-w-[30px] h-7 flex items-center justify-center text-sm font-medium\\n                            \".concat(button.isActive ? buttonActive : \"\".concat(buttonText, \" \").concat(buttonHover), \"\\n                        \"),\n                style: {\n                    backgroundColor: button.isActive ? theme === \"dark\" ? \"#3185eb\" : \"#eab834\" : \"transparent\"\n                },\n                onMouseEnter: function(e) {\n                    if (!button.isActive) {\n                        e.currentTarget.style.backgroundColor = theme === \"dark\" ? \"#3185eb\" : \"#eab834\";\n                        if (theme === \"dark\") {\n                            e.currentTarget.style.color = \"white\";\n                        }\n                    }\n                },\n                onMouseLeave: function(e) {\n                    if (!button.isActive) {\n                        e.currentTarget.style.backgroundColor = \"transparent\";\n                        e.currentTarget.style.color = \"\";\n                    }\n                },\n                children: button.icon\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 303,\n                columnNumber: 21\n            }, _this);\n        })\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n        lineNumber: 283,\n        columnNumber: 9\n    }, this), document.body);\n}\n_s(FloatingToolbarPlugin, \"FbU03gSrzEPMPysVlTtv8K886Dg=\", false, function() {\n    return [\n        _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_6__.useLexicalComposerContext,\n        next_themes__WEBPACK_IMPORTED_MODULE_4__.useTheme\n    ];\n});\n_c = FloatingToolbarPlugin;\nvar _c;\n$RefreshReg$(_c, \"FloatingToolbarPlugin\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/editor/plugins/floating-toolbar-plugin.tsx\n"));

/***/ })

});