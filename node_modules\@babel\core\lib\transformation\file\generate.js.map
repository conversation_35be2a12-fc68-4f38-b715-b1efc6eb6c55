{"version": 3, "names": ["generateCode", "pluginPasses", "file", "opts", "ast", "code", "inputMap", "generatorOpts", "results", "plugins", "plugin", "generatorOverride", "result", "generate", "undefined", "push", "length", "then", "Error", "outputCode", "decodedMap", "outputMap", "map", "mergeSourceMap", "toObject", "sourceFileName", "sourceMaps", "convertSourceMap", "fromObject", "toComment"], "sources": ["../../../src/transformation/file/generate.ts"], "sourcesContent": ["import type { PluginPasses } from \"../../config\";\nimport convertSourceMap from \"convert-source-map\";\ntype SourceMap = any;\nimport generate from \"@babel/generator\";\n\nimport type File from \"./file\";\nimport mergeSourceMap from \"./merge-map\";\n\nexport default function generateCode(\n  pluginPasses: PluginPasses,\n  file: File,\n): {\n  outputCode: string;\n  outputMap: SourceMap | null;\n} {\n  const { opts, ast, code, inputMap } = file;\n  const { generatorOpts } = opts;\n\n  const results = [];\n  for (const plugins of pluginPasses) {\n    for (const plugin of plugins) {\n      const { generatorOverride } = plugin;\n      if (generatorOverride) {\n        const result = generatorOverride(ast, generatorOpts, code, generate);\n\n        if (result !== undefined) results.push(result);\n      }\n    }\n  }\n\n  let result;\n  if (results.length === 0) {\n    result = generate(ast, generatorOpts, code);\n  } else if (results.length === 1) {\n    result = results[0];\n\n    if (typeof result.then === \"function\") {\n      throw new Error(\n        `You appear to be using an async codegen plugin, ` +\n          `which your current version of Babel does not support. ` +\n          `If you're using a published plugin, ` +\n          `you may need to upgrade your @babel/core version.`,\n      );\n    }\n  } else {\n    throw new Error(\"More than one plugin attempted to override codegen.\");\n  }\n\n  // Decoded maps are faster to merge, so we attempt to get use the decodedMap\n  // first. But to preserve backwards compat with older Generator, we'll fall\n  // back to the encoded map.\n  let { code: outputCode, decodedMap: outputMap = result.map } = result;\n\n  if (outputMap) {\n    if (inputMap) {\n      // mergeSourceMap returns an encoded map\n      outputMap = mergeSourceMap(\n        inputMap.toObject(),\n        outputMap,\n        generatorOpts.sourceFileName,\n      );\n    } else {\n      // We cannot output a decoded map, so retrieve the encoded form. Because\n      // the decoded form is free, it's fine to prioritize decoded first.\n      outputMap = result.map;\n    }\n  }\n\n  if (opts.sourceMaps === \"inline\" || opts.sourceMaps === \"both\") {\n    outputCode += \"\\n\" + convertSourceMap.fromObject(outputMap).toComment();\n  }\n\n  if (opts.sourceMaps === \"inline\") {\n    outputMap = null;\n  }\n\n  return { outputCode, outputMap };\n}\n"], "mappings": ";;;;;;;AACA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AAEA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AAGA;;AAEe,SAASA,YAAT,CACbC,YADa,EAEbC,IAFa,EAMb;EACA,MAAM;IAAEC,IAAF;IAAQC,GAAR;IAAaC,IAAb;IAAmBC;EAAnB,IAAgCJ,IAAtC;EACA,MAAM;IAAEK;EAAF,IAAoBJ,IAA1B;EAEA,MAAMK,OAAO,GAAG,EAAhB;;EACA,KAAK,MAAMC,OAAX,IAAsBR,YAAtB,EAAoC;IAClC,KAAK,MAAMS,MAAX,IAAqBD,OAArB,EAA8B;MAC5B,MAAM;QAAEE;MAAF,IAAwBD,MAA9B;;MACA,IAAIC,iBAAJ,EAAuB;QACrB,MAAMC,MAAM,GAAGD,iBAAiB,CAACP,GAAD,EAAMG,aAAN,EAAqBF,IAArB,EAA2BQ,oBAA3B,CAAhC;QAEA,IAAID,MAAM,KAAKE,SAAf,EAA0BN,OAAO,CAACO,IAAR,CAAaH,MAAb;MAC3B;IACF;EACF;;EAED,IAAIA,MAAJ;;EACA,IAAIJ,OAAO,CAACQ,MAAR,KAAmB,CAAvB,EAA0B;IACxBJ,MAAM,GAAG,IAAAC,oBAAA,EAAST,GAAT,EAAcG,aAAd,EAA6BF,IAA7B,CAAT;EACD,CAFD,MAEO,IAAIG,OAAO,CAACQ,MAAR,KAAmB,CAAvB,EAA0B;IAC/BJ,MAAM,GAAGJ,OAAO,CAAC,CAAD,CAAhB;;IAEA,IAAI,OAAOI,MAAM,CAACK,IAAd,KAAuB,UAA3B,EAAuC;MACrC,MAAM,IAAIC,KAAJ,CACH,kDAAD,GACG,wDADH,GAEG,sCAFH,GAGG,mDAJC,CAAN;IAMD;EACF,CAXM,MAWA;IACL,MAAM,IAAIA,KAAJ,CAAU,qDAAV,CAAN;EACD;;EAKD,IAAI;IAAEb,IAAI,EAAEc,UAAR;IAAoBC,UAAU,EAAEC,SAAS,GAAGT,MAAM,CAACU;EAAnD,IAA2DV,MAA/D;;EAEA,IAAIS,SAAJ,EAAe;IACb,IAAIf,QAAJ,EAAc;MAEZe,SAAS,GAAG,IAAAE,iBAAA,EACVjB,QAAQ,CAACkB,QAAT,EADU,EAEVH,SAFU,EAGVd,aAAa,CAACkB,cAHJ,CAAZ;IAKD,CAPD,MAOO;MAGLJ,SAAS,GAAGT,MAAM,CAACU,GAAnB;IACD;EACF;;EAED,IAAInB,IAAI,CAACuB,UAAL,KAAoB,QAApB,IAAgCvB,IAAI,CAACuB,UAAL,KAAoB,MAAxD,EAAgE;IAC9DP,UAAU,IAAI,OAAOQ,mBAAA,CAAiBC,UAAjB,CAA4BP,SAA5B,EAAuCQ,SAAvC,EAArB;EACD;;EAED,IAAI1B,IAAI,CAACuB,UAAL,KAAoB,QAAxB,EAAkC;IAChCL,SAAS,GAAG,IAAZ;EACD;;EAED,OAAO;IAAEF,UAAF;IAAcE;EAAd,CAAP;AACD"}