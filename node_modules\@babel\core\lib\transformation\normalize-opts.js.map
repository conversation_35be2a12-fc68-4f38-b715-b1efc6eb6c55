{"version": 3, "names": ["normalizeOptions", "config", "filename", "cwd", "filenameRelative", "path", "relative", "sourceType", "inputSourceMap", "sourceMaps", "sourceRoot", "options", "moduleRoot", "sourceFileName", "basename", "comments", "compact", "opts", "parserOpts", "extname", "plugins", "generatorOpts", "auxiliaryCommentBefore", "auxiliaryCommentAfter", "retainLines", "shouldPrintComment", "minified", "passes", "plugin", "manipulateOptions"], "sources": ["../../src/transformation/normalize-opts.ts"], "sourcesContent": ["import path from \"path\";\nimport type { ResolvedConfig } from \"../config\";\n\nexport default function normalizeOptions(config: ResolvedConfig): {} {\n  const {\n    filename,\n    cwd,\n    filenameRelative = typeof filename === \"string\"\n      ? path.relative(cwd, filename)\n      : \"unknown\",\n    sourceType = \"module\",\n    inputSourceMap,\n    sourceMaps = !!inputSourceMap,\n    sourceRoot = process.env.BABEL_8_BREAKING\n      ? undefined\n      : config.options.moduleRoot,\n\n    sourceFileName = path.basename(filenameRelative),\n\n    comments = true,\n    compact = \"auto\",\n  } = config.options;\n\n  const opts = config.options;\n\n  const options = {\n    ...opts,\n\n    parserOpts: {\n      sourceType:\n        path.extname(filenameRelative) === \".mjs\" ? \"module\" : sourceType,\n\n      sourceFileName: filename,\n      plugins: [],\n      ...opts.parserOpts,\n    },\n\n    generatorOpts: {\n      // General generator flags.\n      filename,\n\n      auxiliaryCommentBefore: opts.auxiliaryCommentBefore,\n      auxiliaryCommentAfter: opts.auxiliaryCommentAfter,\n      retainLines: opts.retainLines,\n      comments,\n      shouldPrintComment: opts.shouldPrintComment,\n      compact,\n      minified: opts.minified,\n\n      // Source-map generation flags.\n      sourceMaps,\n\n      sourceRoot,\n      sourceFileName,\n      ...opts.generatorOpts,\n    },\n  };\n\n  for (const plugins of config.passes) {\n    for (const plugin of plugins) {\n      if (plugin.manipulateOptions) {\n        plugin.manipulateOptions(options, options.parserOpts);\n      }\n    }\n  }\n\n  return options;\n}\n"], "mappings": ";;;;;;;AAAA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AAGe,SAASA,gBAAT,CAA0BC,MAA1B,EAAsD;EACnE,MAAM;IACJC,QADI;IAEJC,GAFI;IAGJC,gBAAgB,GAAG,OAAOF,QAAP,KAAoB,QAApB,GACfG,OAAA,CAAKC,QAAL,CAAcH,GAAd,EAAmBD,QAAnB,CADe,GAEf,SALA;IAMJK,UAAU,GAAG,QANT;IAOJC,cAPI;IAQJC,UAAU,GAAG,CAAC,CAACD,cARX;IASJE,UAAU,GAENT,MAAM,CAACU,OAAP,CAAeC,UAXf;IAaJC,cAAc,GAAGR,OAAA,CAAKS,QAAL,CAAcV,gBAAd,CAbb;IAeJW,QAAQ,GAAG,IAfP;IAgBJC,OAAO,GAAG;EAhBN,IAiBFf,MAAM,CAACU,OAjBX;EAmBA,MAAMM,IAAI,GAAGhB,MAAM,CAACU,OAApB;EAEA,MAAMA,OAAO,qBACRM,IADQ;IAGXC,UAAU;MACRX,UAAU,EACRF,OAAA,CAAKc,OAAL,CAAaf,gBAAb,MAAmC,MAAnC,GAA4C,QAA5C,GAAuDG,UAFjD;MAIRM,cAAc,EAAEX,QAJR;MAKRkB,OAAO,EAAE;IALD,GAMLH,IAAI,CAACC,UANA,CAHC;IAYXG,aAAa;MAEXnB,QAFW;MAIXoB,sBAAsB,EAAEL,IAAI,CAACK,sBAJlB;MAKXC,qBAAqB,EAAEN,IAAI,CAACM,qBALjB;MAMXC,WAAW,EAAEP,IAAI,CAACO,WANP;MAOXT,QAPW;MAQXU,kBAAkB,EAAER,IAAI,CAACQ,kBARd;MASXT,OATW;MAUXU,QAAQ,EAAET,IAAI,CAACS,QAVJ;MAaXjB,UAbW;MAeXC,UAfW;MAgBXG;IAhBW,GAiBRI,IAAI,CAACI,aAjBG;EAZF,EAAb;;EAiCA,KAAK,MAAMD,OAAX,IAAsBnB,MAAM,CAAC0B,MAA7B,EAAqC;IACnC,KAAK,MAAMC,MAAX,IAAqBR,OAArB,EAA8B;MAC5B,IAAIQ,MAAM,CAACC,iBAAX,EAA8B;QAC5BD,MAAM,CAACC,iBAAP,CAAyBlB,OAAzB,EAAkCA,OAAO,CAACO,UAA1C;MACD;IACF;EACF;;EAED,OAAOP,OAAP;AACD"}