{"version": 3, "names": ["transformFileRunner", "gens<PERSON>", "filename", "opts", "options", "config", "loadConfig", "code", "fs", "readFile", "run", "transformFile", "args", "errback", "transformFileSync", "sync", "transformFileAsync", "async"], "sources": ["../src/transform-file.ts"], "sourcesContent": ["import gensync, { type <PERSON><PERSON> } from \"gensync\";\n\nimport loadConfig from \"./config\";\nimport type { InputOptions, ResolvedConfig } from \"./config\";\nimport { run } from \"./transformation\";\nimport type { FileResult, FileResultCallback } from \"./transformation\";\nimport * as fs from \"./gensync-utils/fs\";\n\ntype transformFileBrowserType = typeof import(\"./transform-file-browser\");\ntype transformFileType = typeof import(\"./transform-file\");\n\n// Kind of gross, but essentially asserting that the exports of this module are the same as the\n// exports of transform-file-browser, since this file may be replaced at bundle time with\n// transform-file-browser.\n({} as any as transformFileBrowserType as transformFileType);\n\nconst transformFileRunner = gensync(function* (\n  filename: string,\n  opts?: InputOptions,\n): Handler<FileResult | null> {\n  const options = { ...opts, filename };\n\n  const config: ResolvedConfig | null = yield* loadConfig(options);\n  if (config === null) return null;\n\n  const code = yield* fs.readFile(filename, \"utf8\");\n  return yield* run(config, code);\n});\n\n// @ts-expect-error TS doesn't detect that this signature is compatible\nexport function transformFile(\n  filename: string,\n  callback: FileResultCallback,\n): void;\nexport function transformFile(\n  filename: string,\n  opts: InputOptions | undefined | null,\n  callback: FileResultCallback,\n): void;\nexport function transformFile(\n  ...args: Parameters<typeof transformFileRunner.errback>\n) {\n  return transformFileRunner.errback(...args);\n}\n\nexport function transformFileSync(\n  ...args: Parameters<typeof transformFileRunner.sync>\n) {\n  return transformFileRunner.sync(...args);\n}\nexport function transformFileAsync(\n  ...args: Parameters<typeof transformFileRunner.async>\n) {\n  return transformFileRunner.async(...args);\n}\n"], "mappings": ";;;;;;;;;AAAA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AAEA;;AAEA;;AAEA;;AAQA,CAAC,EAAD;;AAEA,MAAMA,mBAAmB,GAAGC,UAAA,CAAQ,WAClCC,QADkC,EAElCC,IAFkC,EAGN;EAC5B,MAAMC,OAAO,qBAAQD,IAAR;IAAcD;EAAd,EAAb;EAEA,MAAMG,MAA6B,GAAG,OAAO,IAAAC,eAAA,EAAWF,OAAX,CAA7C;EACA,IAAIC,MAAM,KAAK,IAAf,EAAqB,OAAO,IAAP;EAErB,MAAME,IAAI,GAAG,OAAOC,EAAE,CAACC,QAAH,CAAYP,QAAZ,EAAsB,MAAtB,CAApB;EACA,OAAO,OAAO,IAAAQ,mBAAA,EAAIL,MAAJ,EAAYE,IAAZ,CAAd;AACD,CAX2B,CAA5B;;AAuBO,SAASI,aAAT,CACL,GAAGC,IADE,EAEL;EACA,OAAOZ,mBAAmB,CAACa,OAApB,CAA4B,GAAGD,IAA/B,CAAP;AACD;;AAEM,SAASE,iBAAT,CACL,GAAGF,IADE,EAEL;EACA,OAAOZ,mBAAmB,CAACe,IAApB,CAAyB,GAAGH,IAA5B,CAAP;AACD;;AACM,SAASI,kBAAT,CACL,GAAGJ,IADE,EAEL;EACA,OAAOZ,mBAAmB,CAACiB,KAApB,CAA0B,GAAGL,IAA7B,CAAP;AACD"}