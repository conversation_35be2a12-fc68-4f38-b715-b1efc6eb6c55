{"version": 3, "names": ["transformRunner", "gens<PERSON>", "transform", "code", "opts", "config", "loadConfig", "run", "optsOrCallback", "maybe<PERSON><PERSON><PERSON>", "callback", "undefined", "beginHiddenCallStack", "sync", "errback", "transformSync", "args", "transformAsync", "async"], "sources": ["../src/transform.ts"], "sourcesContent": ["import gensync, { type <PERSON><PERSON> } from \"gensync\";\n\nimport loadConfig from \"./config\";\nimport type { InputOptions, ResolvedConfig } from \"./config\";\nimport { run } from \"./transformation\";\n\nimport type { FileResult, FileResultCallback } from \"./transformation\";\nimport { beginHiddenCallStack } from \"./errors/rewrite-stack-trace\";\n\nexport type { FileResult } from \"./transformation\";\n\ntype Transform = {\n  (code: string, callback: FileResultCallback): void;\n  (\n    code: string,\n    opts: InputOptions | undefined | null,\n    callback: FileResultCallback,\n  ): void;\n  (code: string, opts?: InputOptions | null): FileResult | null;\n};\n\nconst transformRunner = gensync(function* transform(\n  code: string,\n  opts?: InputOptions,\n): Handler<FileResult | null> {\n  const config: ResolvedConfig | null = yield* loadConfig(opts);\n  if (config === null) return null;\n\n  return yield* run(config, code);\n});\n\nexport const transform: Transform = function transform(\n  code,\n  optsOrCallback?: InputOptions | null | undefined | FileResultCallback,\n  maybeCallback?: FileResultCallback,\n) {\n  let opts: InputOptions | undefined | null;\n  let callback: FileResultCallback | undefined;\n  if (typeof optsOrCallback === \"function\") {\n    callback = optsOrCallback;\n    opts = undefined;\n  } else {\n    opts = optsOrCallback;\n    callback = maybeCallback;\n  }\n\n  if (callback === undefined) {\n    if (process.env.BABEL_8_BREAKING) {\n      throw new Error(\n        \"Starting from Babel 8.0.0, the 'transform' function expects a callback. If you need to call it synchronously, please use 'transformSync'.\",\n      );\n    } else {\n      // console.warn(\n      //   \"Starting from Babel 8.0.0, the 'transform' function will expect a callback. If you need to call it synchronously, please use 'transformSync'.\",\n      // );\n      return beginHiddenCallStack(transformRunner.sync)(code, opts);\n    }\n  }\n\n  beginHiddenCallStack(transformRunner.errback)(code, opts, callback);\n};\n\nexport function transformSync(\n  ...args: Parameters<typeof transformRunner.sync>\n) {\n  return beginHiddenCallStack(transformRunner.sync)(...args);\n}\nexport function transformAsync(\n  ...args: Parameters<typeof transformRunner.async>\n) {\n  return beginHiddenCallStack(transformRunner.async)(...args);\n}\n"], "mappings": ";;;;;;;;;AAAA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AAEA;;AAEA;;AAGA;;AAcA,MAAMA,eAAe,GAAGC,UAAA,CAAQ,UAAUC,SAAV,CAC9BC,IAD8B,EAE9BC,IAF8B,EAGF;EAC5B,MAAMC,MAA6B,GAAG,OAAO,IAAAC,eAAA,EAAWF,IAAX,CAA7C;EACA,IAAIC,MAAM,KAAK,IAAf,EAAqB,OAAO,IAAP;EAErB,OAAO,OAAO,IAAAE,mBAAA,EAAIF,MAAJ,EAAYF,IAAZ,CAAd;AACD,CARuB,CAAxB;;AAUO,MAAMD,SAAoB,GAAG,SAASA,SAAT,CAClCC,IADkC,EAElCK,cAFkC,EAGlCC,aAHkC,EAIlC;EACA,IAAIL,IAAJ;EACA,IAAIM,QAAJ;;EACA,IAAI,OAAOF,cAAP,KAA0B,UAA9B,EAA0C;IACxCE,QAAQ,GAAGF,cAAX;IACAJ,IAAI,GAAGO,SAAP;EACD,CAHD,MAGO;IACLP,IAAI,GAAGI,cAAP;IACAE,QAAQ,GAAGD,aAAX;EACD;;EAED,IAAIC,QAAQ,KAAKC,SAAjB,EAA4B;IAKnB;MAIL,OAAO,IAAAC,uCAAA,EAAqBZ,eAAe,CAACa,IAArC,EAA2CV,IAA3C,EAAiDC,IAAjD,CAAP;IACD;EACF;;EAED,IAAAQ,uCAAA,EAAqBZ,eAAe,CAACc,OAArC,EAA8CX,IAA9C,EAAoDC,IAApD,EAA0DM,QAA1D;AACD,CA7BM;;;;AA+BA,SAASK,aAAT,CACL,GAAGC,IADE,EAEL;EACA,OAAO,IAAAJ,uCAAA,EAAqBZ,eAAe,CAACa,IAArC,EAA2C,GAAGG,IAA9C,CAAP;AACD;;AACM,SAASC,cAAT,CACL,GAAGD,IADE,EAEL;EACA,OAAO,IAAAJ,uCAAA,EAAqBZ,eAAe,CAACkB,KAArC,EAA4C,GAAGF,IAA/C,CAAP;AACD"}