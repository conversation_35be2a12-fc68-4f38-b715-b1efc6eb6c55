{"version": 3, "names": ["defineType", "defineAliasedType", "visitor", "aliases", "fields", "name", "validate", "assertNodeType", "value", "optional", "builder", "openingElement", "closingElement", "children", "chain", "assertValueType", "assertEach", "selfClosing", "expression", "object", "property", "namespace", "default", "attributes", "typeParameters", "argument", "openingFragment", "closingFragment"], "sources": ["../../src/definitions/jsx.ts"], "sourcesContent": ["import {\n  defineAliasedType,\n  assertNodeType,\n  assertValueType,\n  chain,\n  assertEach,\n} from \"./utils\";\n\nconst defineType = defineAliasedType(\"JSX\");\n\ndefineType(\"JSXAttribute\", {\n  visitor: [\"name\", \"value\"],\n  aliases: [\"Immutable\"],\n  fields: {\n    name: {\n      validate: assertNodeType(\"JSXIdentifier\", \"JSXNamespacedName\"),\n    },\n    value: {\n      optional: true,\n      validate: assertNodeType(\n        \"JSXElement\",\n        \"JSXFragment\",\n        \"StringLiteral\",\n        \"JSXExpressionContainer\",\n      ),\n    },\n  },\n});\n\ndefineType(\"JSXClosingElement\", {\n  visitor: [\"name\"],\n  aliases: [\"Immutable\"],\n  fields: {\n    name: {\n      validate: assertNodeType(\n        \"JSXIdentifier\",\n        \"JSXMemberExpression\",\n        \"JSXNamespacedName\",\n      ),\n    },\n  },\n});\n\ndefineType(\"JSXElement\", {\n  builder: process.env.BABEL_8_BREAKING\n    ? [\"openingElement\", \"closingElement\", \"children\"]\n    : [\"openingElement\", \"closingElement\", \"children\", \"selfClosing\"],\n  visitor: [\"openingElement\", \"children\", \"closingElement\"],\n  aliases: [\"Immutable\", \"Expression\"],\n  fields: {\n    openingElement: {\n      validate: assertNodeType(\"JSXOpeningElement\"),\n    },\n    closingElement: {\n      optional: true,\n      validate: assertNodeType(\"JSXClosingElement\"),\n    },\n    children: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(\n          assertNodeType(\n            \"JSXText\",\n            \"JSXExpressionContainer\",\n            \"JSXSpreadChild\",\n            \"JSXElement\",\n            \"JSXFragment\",\n          ),\n        ),\n      ),\n    },\n    ...(process.env.BABEL_8_BREAKING\n      ? {}\n      : {\n          selfClosing: {\n            validate: assertValueType(\"boolean\"),\n            optional: true,\n          },\n        }),\n  },\n});\n\ndefineType(\"JSXEmptyExpression\", {});\n\ndefineType(\"JSXExpressionContainer\", {\n  visitor: [\"expression\"],\n  aliases: [\"Immutable\"],\n  fields: {\n    expression: {\n      validate: assertNodeType(\"Expression\", \"JSXEmptyExpression\"),\n    },\n  },\n});\n\ndefineType(\"JSXSpreadChild\", {\n  visitor: [\"expression\"],\n  aliases: [\"Immutable\"],\n  fields: {\n    expression: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n});\n\ndefineType(\"JSXIdentifier\", {\n  builder: [\"name\"],\n  fields: {\n    name: {\n      validate: assertValueType(\"string\"),\n    },\n  },\n});\n\ndefineType(\"JSXMemberExpression\", {\n  visitor: [\"object\", \"property\"],\n  fields: {\n    object: {\n      validate: assertNodeType(\"JSXMemberExpression\", \"JSXIdentifier\"),\n    },\n    property: {\n      validate: assertNodeType(\"JSXIdentifier\"),\n    },\n  },\n});\n\ndefineType(\"JSXNamespacedName\", {\n  visitor: [\"namespace\", \"name\"],\n  fields: {\n    namespace: {\n      validate: assertNodeType(\"JSXIdentifier\"),\n    },\n    name: {\n      validate: assertNodeType(\"JSXIdentifier\"),\n    },\n  },\n});\n\ndefineType(\"JSXOpeningElement\", {\n  builder: [\"name\", \"attributes\", \"selfClosing\"],\n  visitor: [\"name\", \"attributes\"],\n  aliases: [\"Immutable\"],\n  fields: {\n    name: {\n      validate: assertNodeType(\n        \"JSXIdentifier\",\n        \"JSXMemberExpression\",\n        \"JSXNamespacedName\",\n      ),\n    },\n    selfClosing: {\n      default: false,\n    },\n    attributes: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(assertNodeType(\"JSXAttribute\", \"JSXSpreadAttribute\")),\n      ),\n    },\n    typeParameters: {\n      validate: assertNodeType(\n        \"TypeParameterInstantiation\",\n        \"TSTypeParameterInstantiation\",\n      ),\n      optional: true,\n    },\n  },\n});\n\ndefineType(\"JSXSpreadAttribute\", {\n  visitor: [\"argument\"],\n  fields: {\n    argument: {\n      validate: assertNodeType(\"Expression\"),\n    },\n  },\n});\n\ndefineType(\"JSXText\", {\n  aliases: [\"Immutable\"],\n  builder: [\"value\"],\n  fields: {\n    value: {\n      validate: assertValueType(\"string\"),\n    },\n  },\n});\n\ndefineType(\"JSXFragment\", {\n  builder: [\"openingFragment\", \"closingFragment\", \"children\"],\n  visitor: [\"openingFragment\", \"children\", \"closingFragment\"],\n  aliases: [\"Immutable\", \"Expression\"],\n  fields: {\n    openingFragment: {\n      validate: assertNodeType(\"JSXOpeningFragment\"),\n    },\n    closingFragment: {\n      validate: assertNodeType(\"JSXClosingFragment\"),\n    },\n    children: {\n      validate: chain(\n        assertValueType(\"array\"),\n        assertEach(\n          assertNodeType(\n            \"JSXText\",\n            \"JSXExpressionContainer\",\n            \"JSXSpreadChild\",\n            \"JSXElement\",\n            \"JSXFragment\",\n          ),\n        ),\n      ),\n    },\n  },\n});\n\ndefineType(\"JSXOpeningFragment\", {\n  aliases: [\"Immutable\"],\n});\n\ndefineType(\"JSXClosingFragment\", {\n  aliases: [\"Immutable\"],\n});\n"], "mappings": ";;AAAA;;AAQA,MAAMA,UAAU,GAAG,IAAAC,wBAAA,EAAkB,KAAlB,CAAnB;AAEAD,UAAU,CAAC,cAAD,EAAiB;EACzBE,OAAO,EAAE,CAAC,MAAD,EAAS,OAAT,CADgB;EAEzBC,OAAO,EAAE,CAAC,WAAD,CAFgB;EAGzBC,MAAM,EAAE;IACNC,IAAI,EAAE;MACJC,QAAQ,EAAE,IAAAC,qBAAA,EAAe,eAAf,EAAgC,mBAAhC;IADN,CADA;IAINC,KAAK,EAAE;MACLC,QAAQ,EAAE,IADL;MAELH,QAAQ,EAAE,IAAAC,qBAAA,EACR,YADQ,EAER,aAFQ,EAGR,eAHQ,EAIR,wBAJQ;IAFL;EAJD;AAHiB,CAAjB,CAAV;AAmBAP,UAAU,CAAC,mBAAD,EAAsB;EAC9BE,OAAO,EAAE,CAAC,MAAD,CADqB;EAE9BC,OAAO,EAAE,CAAC,WAAD,CAFqB;EAG9BC,MAAM,EAAE;IACNC,IAAI,EAAE;MACJC,QAAQ,EAAE,IAAAC,qBAAA,EACR,eADQ,EAER,qBAFQ,EAGR,mBAHQ;IADN;EADA;AAHsB,CAAtB,CAAV;AAcAP,UAAU,CAAC,YAAD,EAAe;EACvBU,OAAO,EAEH,CAAC,gBAAD,EAAmB,gBAAnB,EAAqC,UAArC,EAAiD,aAAjD,CAHmB;EAIvBR,OAAO,EAAE,CAAC,gBAAD,EAAmB,UAAnB,EAA+B,gBAA/B,CAJc;EAKvBC,OAAO,EAAE,CAAC,WAAD,EAAc,YAAd,CALc;EAMvBC,MAAM;IACJO,cAAc,EAAE;MACdL,QAAQ,EAAE,IAAAC,qBAAA,EAAe,mBAAf;IADI,CADZ;IAIJK,cAAc,EAAE;MACdH,QAAQ,EAAE,IADI;MAEdH,QAAQ,EAAE,IAAAC,qBAAA,EAAe,mBAAf;IAFI,CAJZ;IAQJM,QAAQ,EAAE;MACRP,QAAQ,EAAE,IAAAQ,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EACE,IAAAT,qBAAA,EACE,SADF,EAEE,wBAFF,EAGE,gBAHF,EAIE,YAJF,EAKE,aALF,CADF,CAFQ;IADF;EARN,GAwBA;IACEU,WAAW,EAAE;MACXX,QAAQ,EAAE,IAAAS,sBAAA,EAAgB,SAAhB,CADC;MAEXN,QAAQ,EAAE;IAFC;EADf,CAxBA;AANiB,CAAf,CAAV;AAuCAT,UAAU,CAAC,oBAAD,EAAuB,EAAvB,CAAV;AAEAA,UAAU,CAAC,wBAAD,EAA2B;EACnCE,OAAO,EAAE,CAAC,YAAD,CAD0B;EAEnCC,OAAO,EAAE,CAAC,WAAD,CAF0B;EAGnCC,MAAM,EAAE;IACNc,UAAU,EAAE;MACVZ,QAAQ,EAAE,IAAAC,qBAAA,EAAe,YAAf,EAA6B,oBAA7B;IADA;EADN;AAH2B,CAA3B,CAAV;AAUAP,UAAU,CAAC,gBAAD,EAAmB;EAC3BE,OAAO,EAAE,CAAC,YAAD,CADkB;EAE3BC,OAAO,EAAE,CAAC,WAAD,CAFkB;EAG3BC,MAAM,EAAE;IACNc,UAAU,EAAE;MACVZ,QAAQ,EAAE,IAAAC,qBAAA,EAAe,YAAf;IADA;EADN;AAHmB,CAAnB,CAAV;AAUAP,UAAU,CAAC,eAAD,EAAkB;EAC1BU,OAAO,EAAE,CAAC,MAAD,CADiB;EAE1BN,MAAM,EAAE;IACNC,IAAI,EAAE;MACJC,QAAQ,EAAE,IAAAS,sBAAA,EAAgB,QAAhB;IADN;EADA;AAFkB,CAAlB,CAAV;AASAf,UAAU,CAAC,qBAAD,EAAwB;EAChCE,OAAO,EAAE,CAAC,QAAD,EAAW,UAAX,CADuB;EAEhCE,MAAM,EAAE;IACNe,MAAM,EAAE;MACNb,QAAQ,EAAE,IAAAC,qBAAA,EAAe,qBAAf,EAAsC,eAAtC;IADJ,CADF;IAINa,QAAQ,EAAE;MACRd,QAAQ,EAAE,IAAAC,qBAAA,EAAe,eAAf;IADF;EAJJ;AAFwB,CAAxB,CAAV;AAYAP,UAAU,CAAC,mBAAD,EAAsB;EAC9BE,OAAO,EAAE,CAAC,WAAD,EAAc,MAAd,CADqB;EAE9BE,MAAM,EAAE;IACNiB,SAAS,EAAE;MACTf,QAAQ,EAAE,IAAAC,qBAAA,EAAe,eAAf;IADD,CADL;IAINF,IAAI,EAAE;MACJC,QAAQ,EAAE,IAAAC,qBAAA,EAAe,eAAf;IADN;EAJA;AAFsB,CAAtB,CAAV;AAYAP,UAAU,CAAC,mBAAD,EAAsB;EAC9BU,OAAO,EAAE,CAAC,MAAD,EAAS,YAAT,EAAuB,aAAvB,CADqB;EAE9BR,OAAO,EAAE,CAAC,MAAD,EAAS,YAAT,CAFqB;EAG9BC,OAAO,EAAE,CAAC,WAAD,CAHqB;EAI9BC,MAAM,EAAE;IACNC,IAAI,EAAE;MACJC,QAAQ,EAAE,IAAAC,qBAAA,EACR,eADQ,EAER,qBAFQ,EAGR,mBAHQ;IADN,CADA;IAQNU,WAAW,EAAE;MACXK,OAAO,EAAE;IADE,CARP;IAWNC,UAAU,EAAE;MACVjB,QAAQ,EAAE,IAAAQ,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EAAW,IAAAT,qBAAA,EAAe,cAAf,EAA+B,oBAA/B,CAAX,CAFQ;IADA,CAXN;IAiBNiB,cAAc,EAAE;MACdlB,QAAQ,EAAE,IAAAC,qBAAA,EACR,4BADQ,EAER,8BAFQ,CADI;MAKdE,QAAQ,EAAE;IALI;EAjBV;AAJsB,CAAtB,CAAV;AA+BAT,UAAU,CAAC,oBAAD,EAAuB;EAC/BE,OAAO,EAAE,CAAC,UAAD,CADsB;EAE/BE,MAAM,EAAE;IACNqB,QAAQ,EAAE;MACRnB,QAAQ,EAAE,IAAAC,qBAAA,EAAe,YAAf;IADF;EADJ;AAFuB,CAAvB,CAAV;AASAP,UAAU,CAAC,SAAD,EAAY;EACpBG,OAAO,EAAE,CAAC,WAAD,CADW;EAEpBO,OAAO,EAAE,CAAC,OAAD,CAFW;EAGpBN,MAAM,EAAE;IACNI,KAAK,EAAE;MACLF,QAAQ,EAAE,IAAAS,sBAAA,EAAgB,QAAhB;IADL;EADD;AAHY,CAAZ,CAAV;AAUAf,UAAU,CAAC,aAAD,EAAgB;EACxBU,OAAO,EAAE,CAAC,iBAAD,EAAoB,iBAApB,EAAuC,UAAvC,CADe;EAExBR,OAAO,EAAE,CAAC,iBAAD,EAAoB,UAApB,EAAgC,iBAAhC,CAFe;EAGxBC,OAAO,EAAE,CAAC,WAAD,EAAc,YAAd,CAHe;EAIxBC,MAAM,EAAE;IACNsB,eAAe,EAAE;MACfpB,QAAQ,EAAE,IAAAC,qBAAA,EAAe,oBAAf;IADK,CADX;IAINoB,eAAe,EAAE;MACfrB,QAAQ,EAAE,IAAAC,qBAAA,EAAe,oBAAf;IADK,CAJX;IAONM,QAAQ,EAAE;MACRP,QAAQ,EAAE,IAAAQ,YAAA,EACR,IAAAC,sBAAA,EAAgB,OAAhB,CADQ,EAER,IAAAC,iBAAA,EACE,IAAAT,qBAAA,EACE,SADF,EAEE,wBAFF,EAGE,gBAHF,EAIE,YAJF,EAKE,aALF,CADF,CAFQ;IADF;EAPJ;AAJgB,CAAhB,CAAV;AA4BAP,UAAU,CAAC,oBAAD,EAAuB;EAC/BG,OAAO,EAAE,CAAC,WAAD;AADsB,CAAvB,CAAV;AAIAH,UAAU,CAAC,oBAAD,EAAuB;EAC/BG,OAAO,EAAE,CAAC,WAAD;AADsB,CAAvB,CAAV"}