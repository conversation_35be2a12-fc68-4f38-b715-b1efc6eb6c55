{"c": ["webpack"], "r": ["pages/test-indent", "pages/settings"], "m": ["./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5CAdministrator%5CDocuments%5Cmotea-docker-main%5Cpages%5Ctest-indent.tsx&page=%2Ftest-indent!", "./pages/test-indent.tsx", "./components/button-progress.tsx", "./components/debug/debug-info-copy-button.tsx", "./components/debug/issue-list.tsx", "./components/debug/issue.tsx", "./components/settings/daily-notes.tsx", "./components/settings/debugging.tsx", "./components/settings/editor-width.tsx", "./components/settings/export-button.tsx", "./components/settings/import-button.tsx", "./components/settings/import-or-export.tsx", "./components/settings/language.tsx", "./components/settings/setting-footer.tsx", "./components/settings/settings-container.tsx", "./components/settings/settings-header.tsx", "./components/settings/snippet-injection.tsx", "./components/settings/theme.tsx", "./libs/shared/debugging.ts", "./libs/shared/util.ts", "./libs/web/hooks/use-tree-options.tsx", "./libs/web/utils/markdown-processor.ts", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=C%3A%5CUsers%5CAdministrator%5CDocuments%5Cmotea-docker-main%5Cpages%5Csettings.tsx&page=%2Fsettings!", "./node_modules/pino/browser.js", "./node_modules/quick-format-unescaped/index.js", "./package.json", "./pages/settings.tsx"]}