"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./libs/web/state/lexical-editor.ts":
/*!******************************************!*\
  !*** ./libs/web/state/lexical-editor.ts ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/src/_object_spread.mjs */ \"./node_modules/@swc/helpers/src/_object_spread.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/src/_object_spread_props.mjs */ \"./node_modules/@swc/helpers/src/_object_spread_props.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var libs_web_state_note__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! libs/web/state/note */ \"./libs/web/state/note.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var libs_shared_note__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! libs/shared/note */ \"./libs/shared/note.ts\");\n/* harmony import */ var libs_web_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! libs/web/hooks/use-toast */ \"./libs/web/hooks/use-toast.ts\");\n/* harmony import */ var libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! libs/web/cache/note */ \"./libs/web/cache/note.ts\");\n/* harmony import */ var unstated_next__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! unstated-next */ \"./node_modules/unstated-next/dist/unstated-next.mjs\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash */ \"./node_modules/lodash/lodash.js\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_6__);\n\n\n\n\n\n\n\n\n\n\n\n\nvar ROOT_ID = \"root\";\nvar useLexicalEditor = function(initNote) {\n    // Use initNote if provided, otherwise try to get from NoteState\n    var note = initNote;\n    var createNoteWithTitle, updateNote, createNote;\n    try {\n        var noteState = libs_web_state_note__WEBPACK_IMPORTED_MODULE_0__[\"default\"].useContainer();\n        createNoteWithTitle = noteState.createNoteWithTitle;\n        updateNote = noteState.updateNote;\n        createNote = noteState.createNote;\n        // Only use noteState.note if no initNote is provided\n        if (!note) {\n            note = noteState.note;\n        }\n    } catch (error) {\n        // If NoteState is not available, we'll work with just the initNote\n        console.warn(\"NoteState not available in LexicalEditorState, using initNote only\");\n        createNoteWithTitle = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n        updateNote = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n        createNote = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n    }\n    var router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    var toast = (0,libs_web_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    var editorEl = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // 🔧 新增：快照状态管理\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null), noteSnapshot = ref[0], setNoteSnapshot = ref[1];\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\"), currentEditorContent = ref1[0], setCurrentEditorContent = ref1[1];\n    // Manual save function for IndexedDB\n    var saveToIndexedDB = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(data) {\n            var existingNote, baseNote, updatedNote;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                            2\n                        ];\n                        return [\n                            4,\n                            libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                        ];\n                    case 1:\n                        existingNote = _state.sent();\n                        baseNote = existingNote || note;\n                        updatedNote = (0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, baseNote, data);\n                        return [\n                            4,\n                            libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].setItem(note.id, updatedNote)\n                        ];\n                    case 2:\n                        _state.sent();\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function(data) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        note\n    ]);\n    var syncToServer = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var isNew, localNote, noteToSave, noteData, item, noteUrl, updatedNote, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        false\n                    ];\n                    isNew = (0,lodash__WEBPACK_IMPORTED_MODULE_6__.has)(router.query, \"new\");\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        11,\n                        ,\n                        12\n                    ]);\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                    ];\n                case 2:\n                    localNote = _state.sent();\n                    noteToSave = localNote || note;\n                    if (!isNew) return [\n                        3,\n                        7\n                    ];\n                    noteData = (0,_swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])((0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, noteToSave), {\n                        pid: router.query.pid || ROOT_ID\n                    });\n                    return [\n                        4,\n                        createNote(noteData)\n                    ];\n                case 3:\n                    item = _state.sent();\n                    if (!item) return [\n                        3,\n                        6\n                    ];\n                    noteUrl = \"/\".concat(item.id);\n                    if (!(router.asPath !== noteUrl)) return [\n                        3,\n                        5\n                    ];\n                    return [\n                        4,\n                        router.replace(noteUrl, undefined, {\n                            shallow: true\n                        })\n                    ];\n                case 4:\n                    _state.sent();\n                    _state.label = 5;\n                case 5:\n                    toast(\"Note saved to server\", \"success\");\n                    return [\n                        2,\n                        true\n                    ];\n                case 6:\n                    return [\n                        3,\n                        10\n                    ];\n                case 7:\n                    return [\n                        4,\n                        updateNote(noteToSave)\n                    ];\n                case 8:\n                    updatedNote = _state.sent();\n                    if (!updatedNote) return [\n                        3,\n                        10\n                    ];\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].setItem(updatedNote.id, updatedNote)\n                    ];\n                case 9:\n                    _state.sent();\n                    toast(\"Note updated on server\", \"success\");\n                    return [\n                        2,\n                        true\n                    ];\n                case 10:\n                    return [\n                        3,\n                        12\n                    ];\n                case 11:\n                    error = _state.sent();\n                    toast(\"Failed to save note to server\", \"error\");\n                    return [\n                        2,\n                        false\n                    ];\n                case 12:\n                    return [\n                        2,\n                        false\n                    ];\n            }\n        });\n    }), [\n        note,\n        router,\n        createNote,\n        updateNote,\n        toast\n    ]);\n    var onCreateLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(title) {\n            var result;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!createNoteWithTitle) return [\n                            2,\n                            \"\"\n                        ];\n                        return [\n                            4,\n                            createNoteWithTitle(title)\n                        ];\n                    case 1:\n                        result = _state.sent();\n                        if (result === null || result === void 0 ? void 0 : result.id) {\n                            return [\n                                2,\n                                \"/\".concat(result.id)\n                            ];\n                        }\n                        return [\n                            2,\n                            \"\"\n                        ];\n                }\n            });\n        });\n        return function(title) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        createNoteWithTitle\n    ]);\n    var onSearchLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(term) {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    []\n                ];\n            });\n        });\n        return function(term) {\n            return _ref.apply(this, arguments);\n        };\n    }(), []);\n    var onClickLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(href, event) {\n        if ((0,libs_shared_note__WEBPACK_IMPORTED_MODULE_3__.isNoteLink)(href)) {\n            event.preventDefault();\n            router.push(href);\n        } else {\n            window.open(href, \"_blank\", \"noopener,noreferrer\");\n        }\n    }, [\n        router\n    ]);\n    var onUploadImage = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(_file, _id) {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                // Image upload is disabled in PostgreSQL version\n                toast(\"Image upload is not supported in this version\", \"error\");\n                throw new Error(\"Image upload is not supported\");\n            });\n        });\n        return function(_file, _id) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        toast\n    ]);\n    var onHoverLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(event) {\n        return true;\n    }, []);\n    var ref2 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(), backlinks = ref2[0], setBackLinks = ref2[1];\n    var getBackLinks = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var linkNotes;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    console.log(note === null || note === void 0 ? void 0 : note.id);\n                    linkNotes = [];\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        linkNotes\n                    ];\n                    setBackLinks([]);\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].iterate(function(value) {\n                            var ref;\n                            if ((ref = value.linkIds) === null || ref === void 0 ? void 0 : ref.includes((note === null || note === void 0 ? void 0 : note.id) || \"\")) {\n                                linkNotes.push(value);\n                            }\n                        })\n                    ];\n                case 1:\n                    _state.sent();\n                    setBackLinks(linkNotes);\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        note === null || note === void 0 ? void 0 : note.id\n    ]);\n    // 🔧 快照初始化逻辑 - 打开笔记时设置JSON快照\n    var initializeSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var cachedNote, snapshotJsonContent, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) {\n                        // 新建笔记：快照为空值\n                        setNoteSnapshot(null);\n                        setCurrentEditorContent(\"\");\n                        console.log(\"\\uD83D\\uDD27 新建笔记：JSON快照设置为空值\");\n                        return [\n                            2\n                        ];\n                    }\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        3,\n                        ,\n                        4\n                    ]);\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                    ];\n                case 2:\n                    cachedNote = _state.sent();\n                    snapshotJsonContent = (cachedNote === null || cachedNote === void 0 ? void 0 : cachedNote.content) || \"\";\n                    setNoteSnapshot(snapshotJsonContent);\n                    setCurrentEditorContent(snapshotJsonContent);\n                    console.log(\"\\uD83D\\uDD27 已存在笔记：JSON快照设置为缓存内容\", {\n                        noteId: note.id,\n                        hasSnapshot: !!snapshotJsonContent,\n                        isJsonFormat: snapshotJsonContent.startsWith(\"{\")\n                    });\n                    return [\n                        3,\n                        4\n                    ];\n                case 3:\n                    error = _state.sent();\n                    console.error(\"\\uD83D\\uDD27 JSON快照初始化失败:\", error);\n                    // 失败时设置为空快照\n                    setNoteSnapshot(null);\n                    setCurrentEditorContent(\"\");\n                    return [\n                        3,\n                        4\n                    ];\n                case 4:\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        note === null || note === void 0 ? void 0 : note.id\n    ]);\n    // 当笔记ID变化时初始化快照\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n        initializeSnapshot();\n    }, [\n        initializeSnapshot\n    ]);\n    // 🔧 修复：恢复编辑器基本功能 - 更新JSON内容状态\n    var originalOnEditorChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(value) {\n        var jsonContent = value(); // 这里是JSON格式的内容\n        // 更新当前编辑器JSON内容状态\n        setCurrentEditorContent(jsonContent);\n        console.log(\"\\uD83D\\uDD27 编辑器JSON内容更新:\", {\n            contentLength: jsonContent.length\n        });\n    // 保存逻辑现在完全由SaveButton的快照对比机制处理\n    }, []);\n    // 🔧 临时移除智能包装器，直接使用原始onChange测试输入问题\n    var onEditorChange = originalOnEditorChange;\n    // Function to handle title changes specifically\n    var onTitleChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(title) {\n        var ref;\n        (ref = saveToIndexedDB({\n            title: title,\n            updated_at: new Date().toISOString()\n        })) === null || ref === void 0 ? void 0 : ref.catch(function(v) {\n            return console.error(\"Error whilst saving title to IndexedDB: %O\", v);\n        });\n    }, [\n        saveToIndexedDB\n    ]);\n    // 🔧 修复：JSON快照对比功能 - 供SaveButton使用\n    var compareWithSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        // 如果是新建笔记（快照为null），任何JSON内容都算作变化\n        if (noteSnapshot === null) {\n            return currentEditorContent.trim() !== \"\";\n        }\n        // 已存在笔记：比较当前JSON内容与JSON快照\n        var hasChanges = currentEditorContent !== noteSnapshot;\n        console.log(\"\\uD83D\\uDD27 JSON快照对比:\", {\n            hasChanges: hasChanges,\n            currentLength: currentEditorContent.length,\n            snapshotLength: noteSnapshot.length,\n            bothAreJson: currentEditorContent.startsWith(\"{\") && noteSnapshot.startsWith(\"{\")\n        });\n        return hasChanges;\n    }, [\n        noteSnapshot,\n        currentEditorContent\n    ]);\n    // 🔧 新增：获取当前编辑器状态 - 供SaveButton使用\n    var getEditorState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        return {\n            hasChanges: compareWithSnapshot(),\n            currentContent: currentEditorContent,\n            snapshot: noteSnapshot,\n            isNewNote: noteSnapshot === null\n        };\n    }, [\n        compareWithSnapshot,\n        currentEditorContent,\n        noteSnapshot\n    ]);\n    // 🔧 修复：保存当前JSON内容到IndexedDB - 供SaveButton调用\n    var saveCurrentContent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var title, titleInput, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        false\n                    ];\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        3,\n                        ,\n                        4\n                    ]);\n                    if (note === null || note === void 0 ? void 0 : note.isDailyNote) {\n                        title = note.title;\n                    } else {\n                        titleInput = document.querySelector(\"h1 textarea\");\n                        if (titleInput && titleInput.value) {\n                            title = titleInput.value.trim();\n                        } else {\n                            // 对于JSON格式，使用现有标题或默认标题\n                            title = (note === null || note === void 0 ? void 0 : note.title) || \"Untitled\";\n                        }\n                    }\n                    // 保存JSON内容到IndexedDB\n                    return [\n                        4,\n                        saveToIndexedDB({\n                            content: currentEditorContent,\n                            title: title,\n                            updated_at: new Date().toISOString()\n                        })\n                    ];\n                case 2:\n                    _state.sent();\n                    console.log(\"\\uD83D\\uDD27 JSON内容已保存到IndexedDB\", {\n                        noteId: note.id,\n                        title: title,\n                        contentLength: currentEditorContent.length,\n                        isJsonFormat: currentEditorContent.startsWith(\"{\")\n                    });\n                    return [\n                        2,\n                        true\n                    ];\n                case 3:\n                    error = _state.sent();\n                    console.error(\"\\uD83D\\uDD27 保存JSON到IndexedDB失败:\", error);\n                    return [\n                        2,\n                        false\n                    ];\n                case 4:\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        note,\n        currentEditorContent,\n        saveToIndexedDB\n    ]);\n    return {\n        onCreateLink: onCreateLink,\n        onSearchLink: onSearchLink,\n        onClickLink: onClickLink,\n        onUploadImage: onUploadImage,\n        onHoverLink: onHoverLink,\n        getBackLinks: getBackLinks,\n        onEditorChange: onEditorChange,\n        onTitleChange: onTitleChange,\n        saveToIndexedDB: saveToIndexedDB,\n        syncToServer: syncToServer,\n        backlinks: backlinks,\n        editorEl: editorEl,\n        note: note,\n        // 🔧 新增：快照对比相关功能\n        getEditorState: getEditorState,\n        saveCurrentContent: saveCurrentContent,\n        compareWithSnapshot: compareWithSnapshot\n    };\n};\nvar LexicalEditorState = (0,unstated_next__WEBPACK_IMPORTED_MODULE_11__.createContainer)(useLexicalEditor);\n/* harmony default export */ __webpack_exports__[\"default\"] = (LexicalEditorState);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./libs/web/state/lexical-editor.ts\n"));

/***/ })

});