"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./libs/web/state/lexical-editor.ts":
/*!******************************************!*\
  !*** ./libs/web/state/lexical-editor.ts ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/src/_object_spread.mjs */ \"./node_modules/@swc/helpers/src/_object_spread.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @swc/helpers/src/_object_spread_props.mjs */ \"./node_modules/@swc/helpers/src/_object_spread_props.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var libs_web_state_note__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! libs/web/state/note */ \"./libs/web/state/note.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var libs_shared_note__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! libs/shared/note */ \"./libs/shared/note.ts\");\n/* harmony import */ var libs_web_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! libs/web/hooks/use-toast */ \"./libs/web/hooks/use-toast.ts\");\n/* harmony import */ var libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! libs/web/cache/note */ \"./libs/web/cache/note.ts\");\n/* harmony import */ var unstated_next__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! unstated-next */ \"./node_modules/unstated-next/dist/unstated-next.mjs\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash */ \"./node_modules/lodash/lodash.js\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var libs_shared_markdown_parse_markdown_title__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! libs/shared/markdown/parse-markdown-title */ \"./libs/shared/markdown/parse-markdown-title.ts\");\n/* harmony import */ var libs_web_utils_ime_state_manager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! libs/web/utils/ime-state-manager */ \"./libs/web/utils/ime-state-manager.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar ROOT_ID = \"root\";\nvar useLexicalEditor = function(initNote) {\n    // Use initNote if provided, otherwise try to get from NoteState\n    var note = initNote;\n    var createNoteWithTitle, updateNote, createNote;\n    try {\n        var noteState = libs_web_state_note__WEBPACK_IMPORTED_MODULE_0__[\"default\"].useContainer();\n        createNoteWithTitle = noteState.createNoteWithTitle;\n        updateNote = noteState.updateNote;\n        createNote = noteState.createNote;\n        // Only use noteState.note if no initNote is provided\n        if (!note) {\n            note = noteState.note;\n        }\n    } catch (error) {\n        // If NoteState is not available, we'll work with just the initNote\n        console.warn(\"NoteState not available in LexicalEditorState, using initNote only\");\n        createNoteWithTitle = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n        updateNote = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n        createNote = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n    }\n    var router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    var toast = (0,libs_web_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    var editorEl = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // Manual save function for IndexedDB\n    var saveToIndexedDB = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function(data) {\n            var existingNote, baseNote, updatedNote;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                            2\n                        ];\n                        return [\n                            4,\n                            libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                        ];\n                    case 1:\n                        existingNote = _state.sent();\n                        baseNote = existingNote || note;\n                        updatedNote = (0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_11__[\"default\"])({}, baseNote, data);\n                        return [\n                            4,\n                            libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].setItem(note.id, updatedNote)\n                        ];\n                    case 2:\n                        _state.sent();\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function(data) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        note\n    ]);\n    var syncToServer = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function() {\n        var isNew, localNote, noteToSave, noteData, item, noteUrl, updatedNote, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        false\n                    ];\n                    isNew = (0,lodash__WEBPACK_IMPORTED_MODULE_6__.has)(router.query, \"new\");\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        11,\n                        ,\n                        12\n                    ]);\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                    ];\n                case 2:\n                    localNote = _state.sent();\n                    noteToSave = localNote || note;\n                    if (!isNew) return [\n                        3,\n                        7\n                    ];\n                    noteData = (0,_swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_12__[\"default\"])((0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_11__[\"default\"])({}, noteToSave), {\n                        pid: router.query.pid || ROOT_ID\n                    });\n                    return [\n                        4,\n                        createNote(noteData)\n                    ];\n                case 3:\n                    item = _state.sent();\n                    if (!item) return [\n                        3,\n                        6\n                    ];\n                    noteUrl = \"/\".concat(item.id);\n                    if (!(router.asPath !== noteUrl)) return [\n                        3,\n                        5\n                    ];\n                    return [\n                        4,\n                        router.replace(noteUrl, undefined, {\n                            shallow: true\n                        })\n                    ];\n                case 4:\n                    _state.sent();\n                    _state.label = 5;\n                case 5:\n                    toast(\"Note saved to server\", \"success\");\n                    return [\n                        2,\n                        true\n                    ];\n                case 6:\n                    return [\n                        3,\n                        10\n                    ];\n                case 7:\n                    return [\n                        4,\n                        updateNote(noteToSave)\n                    ];\n                case 8:\n                    updatedNote = _state.sent();\n                    if (!updatedNote) return [\n                        3,\n                        10\n                    ];\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].setItem(updatedNote.id, updatedNote)\n                    ];\n                case 9:\n                    _state.sent();\n                    toast(\"Note updated on server\", \"success\");\n                    // 🔧 保存成功后重置快照\n                    if ( true && window.lexicalContentComparison) {\n                        if (window.lexicalContentComparison.resetSnapshot) {\n                            window.lexicalContentComparison.resetSnapshot();\n                        }\n                    }\n                    return [\n                        2,\n                        true\n                    ];\n                case 10:\n                    return [\n                        3,\n                        12\n                    ];\n                case 11:\n                    error = _state.sent();\n                    toast(\"Failed to save note to server\", \"error\");\n                    return [\n                        2,\n                        false\n                    ];\n                case 12:\n                    return [\n                        2,\n                        false\n                    ];\n            }\n        });\n    }), [\n        note,\n        router,\n        createNote,\n        updateNote,\n        toast\n    ]);\n    var onCreateLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function(title) {\n            var result;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!createNoteWithTitle) return [\n                            2,\n                            \"\"\n                        ];\n                        return [\n                            4,\n                            createNoteWithTitle(title)\n                        ];\n                    case 1:\n                        result = _state.sent();\n                        if (result === null || result === void 0 ? void 0 : result.id) {\n                            return [\n                                2,\n                                \"/\".concat(result.id)\n                            ];\n                        }\n                        return [\n                            2,\n                            \"\"\n                        ];\n                }\n            });\n        });\n        return function(title) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        createNoteWithTitle\n    ]);\n    var onSearchLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function(term) {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    []\n                ];\n            });\n        });\n        return function(term) {\n            return _ref.apply(this, arguments);\n        };\n    }(), []);\n    var onClickLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(href, event) {\n        if ((0,libs_shared_note__WEBPACK_IMPORTED_MODULE_3__.isNoteLink)(href)) {\n            event.preventDefault();\n            router.push(href);\n        } else {\n            window.open(href, \"_blank\", \"noopener,noreferrer\");\n        }\n    }, [\n        router\n    ]);\n    var onUploadImage = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function(_file, _id) {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(this, function(_state) {\n                // Image upload is disabled in PostgreSQL version\n                toast(\"Image upload is not supported in this version\", \"error\");\n                throw new Error(\"Image upload is not supported\");\n            });\n        });\n        return function(_file, _id) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        toast\n    ]);\n    var onHoverLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(event) {\n        return true;\n    }, []);\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(), backlinks = ref[0], setBackLinks = ref[1];\n    var getBackLinks = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function() {\n        var linkNotes;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    console.log(note === null || note === void 0 ? void 0 : note.id);\n                    linkNotes = [];\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        linkNotes\n                    ];\n                    setBackLinks([]);\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].iterate(function(value) {\n                            var ref;\n                            if ((ref = value.linkIds) === null || ref === void 0 ? void 0 : ref.includes((note === null || note === void 0 ? void 0 : note.id) || \"\")) {\n                                linkNotes.push(value);\n                            }\n                        })\n                    ];\n                case 1:\n                    _state.sent();\n                    setBackLinks(linkNotes);\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        note === null || note === void 0 ? void 0 : note.id\n    ]);\n    // 原始的编辑器变化处理逻辑\n    var originalOnEditorChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function(value) {\n            var content, title, currentTitle, titleInput, localNote, error, parsed;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        content = value();\n                        if (!(note === null || note === void 0 ? void 0 : note.isDailyNote)) return [\n                            3,\n                            1\n                        ];\n                        title = note.title;\n                        return [\n                            3,\n                            9\n                        ];\n                    case 1:\n                        currentTitle = \"\";\n                        titleInput = document.querySelector(\"h1 textarea\");\n                        if (!(titleInput && titleInput.value)) return [\n                            3,\n                            2\n                        ];\n                        currentTitle = titleInput.value.trim();\n                        return [\n                            3,\n                            8\n                        ];\n                    case 2:\n                        if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                            3,\n                            7\n                        ];\n                        _state.label = 3;\n                    case 3:\n                        _state.trys.push([\n                            3,\n                            5,\n                            ,\n                            6\n                        ]);\n                        return [\n                            4,\n                            libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                        ];\n                    case 4:\n                        localNote = _state.sent();\n                        currentTitle = (localNote === null || localNote === void 0 ? void 0 : localNote.title) || \"\";\n                        return [\n                            3,\n                            6\n                        ];\n                    case 5:\n                        error = _state.sent();\n                        currentTitle = (note === null || note === void 0 ? void 0 : note.title) || \"\";\n                        return [\n                            3,\n                            6\n                        ];\n                    case 6:\n                        return [\n                            3,\n                            8\n                        ];\n                    case 7:\n                        currentTitle = (note === null || note === void 0 ? void 0 : note.title) || \"\";\n                        _state.label = 8;\n                    case 8:\n                        if (!currentTitle || currentTitle === \"Untitled\" || currentTitle === \"New Page\" || currentTitle === \"\" || currentTitle.includes(\"<\") && currentTitle.includes(\">\")) {\n                            parsed = (0,libs_shared_markdown_parse_markdown_title__WEBPACK_IMPORTED_MODULE_7__.parseMarkdownTitle)(content);\n                            title = parsed.title || \"Untitled\"; // Use 'Untitled' if no title found\n                        } else {\n                            title = currentTitle;\n                        }\n                        _state.label = 9;\n                    case 9:\n                        // Save to IndexedDB immediately for local persistence\n                        return [\n                            4,\n                            saveToIndexedDB({\n                                content: content,\n                                title: title,\n                                updated_at: new Date().toISOString()\n                            })\n                        ];\n                    case 10:\n                        _state.sent();\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function(value) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        saveToIndexedDB,\n        note === null || note === void 0 ? void 0 : note.isDailyNote,\n        note === null || note === void 0 ? void 0 : note.id\n    ]);\n    // 使用智能onChange包装器 - 基于输入状态智能处理\n    var onEditorChange = (0,libs_web_utils_ime_state_manager__WEBPACK_IMPORTED_MODULE_8__.createSmartOnChange)(originalOnEditorChange, {\n        delay: 200 // 快速输入结束后200ms执行\n    });\n    // Function to handle title changes specifically\n    var onTitleChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(title) {\n        var ref;\n        (ref = saveToIndexedDB({\n            title: title,\n            updated_at: new Date().toISOString()\n        })) === null || ref === void 0 ? void 0 : ref.catch(function(v) {\n            return console.error(\"Error whilst saving title to IndexedDB: %O\", v);\n        });\n    }, [\n        saveToIndexedDB\n    ]);\n    return {\n        onCreateLink: onCreateLink,\n        onSearchLink: onSearchLink,\n        onClickLink: onClickLink,\n        onUploadImage: onUploadImage,\n        onHoverLink: onHoverLink,\n        getBackLinks: getBackLinks,\n        onEditorChange: onEditorChange,\n        onTitleChange: onTitleChange,\n        saveToIndexedDB: saveToIndexedDB,\n        syncToServer: syncToServer,\n        backlinks: backlinks,\n        editorEl: editorEl,\n        note: note\n    };\n};\nvar LexicalEditorState = (0,unstated_next__WEBPACK_IMPORTED_MODULE_13__.createContainer)(useLexicalEditor);\n/* harmony default export */ __webpack_exports__[\"default\"] = (LexicalEditorState);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./libs/web/state/lexical-editor.ts\n"));

/***/ })

});