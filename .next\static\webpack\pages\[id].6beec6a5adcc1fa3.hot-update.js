"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./components/editor/plugins/floating-toolbar-plugin.tsx":
/*!***************************************************************!*\
  !*** ./components/editor/plugins/floating-toolbar-plugin.tsx ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FloatingToolbarPlugin; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/src/_sliced_to_array.mjs */ \"./node_modules/@swc/helpers/src/_sliced_to_array.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"./node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lexical */ \"./node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var _lexical_link__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @lexical/link */ \"./node_modules/@lexical/link/LexicalLink.dev.mjs\");\n/* harmony import */ var _lexical_list__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @lexical/list */ \"./node_modules/@lexical/list/LexicalList.dev.mjs\");\n/* harmony import */ var _highlight_plugin__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./highlight-plugin */ \"./components/editor/plugins/highlight-plugin.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-dom */ \"./node_modules/react-dom/index.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-themes */ \"./node_modules/next-themes/dist/index.modern.js\");\n/* harmony import */ var _heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @heroicons/react/outline */ \"./node_modules/@heroicons/react/outline/esm/index.js\");\n/**\n * Floating Toolbar Plugin for Lexical\n * Shows formatting options when text is selected\n */ \n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Heroicons\n\nfunction FloatingToolbarPlugin() {\n    var _this = this;\n    _s();\n    var ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_5__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_6__.useLexicalComposerContext)(), 1), editor = ref[0];\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isVisible = ref1[0], setIsVisible = ref1[1];\n    var ref2 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        top: 0,\n        left: 0\n    }), position = ref2[0], setPosition = ref2[1];\n    var ref3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isBold = ref3[0], setIsBold = ref3[1];\n    var ref4 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isUnderline = ref4[0], setIsUnderline = ref4[1];\n    var ref5 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isStrikethrough = ref5[0], setIsStrikethrough = ref5[1];\n    var ref6 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isCode = ref6[0], setIsCode = ref6[1];\n    var ref7 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isLink = ref7[0], setIsLink = ref7[1];\n    var ref8 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isHighlight = ref8[0], setIsHighlight = ref8[1];\n    var theme = (0,next_themes__WEBPACK_IMPORTED_MODULE_4__.useTheme)().theme;\n    var updateToolbar = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var selection = (0,lexical__WEBPACK_IMPORTED_MODULE_7__.$getSelection)();\n        if ((0,lexical__WEBPACK_IMPORTED_MODULE_7__.$isRangeSelection)(selection)) {\n            var anchorNode = selection.anchor.getNode();\n            var element = anchorNode.getKey() === \"root\" ? anchorNode : anchorNode.getTopLevelElementOrThrow();\n            var elementKey = element.getKey();\n            var elementDOM = editor.getElementByKey(elementKey);\n            // 显示条件：只有当有选中文本时才显示\n            var hasSelection = selection.getTextContent() !== \"\";\n            if (elementDOM !== null && hasSelection) {\n                var nativeSelection = window.getSelection();\n                var rootElement = editor.getRootElement();\n                if (nativeSelection !== null && rootElement !== null && rootElement.contains(nativeSelection.anchorNode)) {\n                    var rangeRect = nativeSelection.getRangeAt(0).getBoundingClientRect();\n                    setPosition({\n                        top: rangeRect.top - 60,\n                        left: rangeRect.left + rangeRect.width / 2 - 150\n                    });\n                    setIsVisible(true);\n                    // Update button states\n                    setIsBold(selection.hasFormat(\"bold\"));\n                    setIsUnderline(selection.hasFormat(\"underline\"));\n                    setIsStrikethrough(selection.hasFormat(\"strikethrough\"));\n                    setIsCode(selection.hasFormat(\"code\"));\n                    // Check if selection contains a link\n                    var node = selection.anchor.getNode();\n                    var parent = node.getParent();\n                    setIsLink((0,_lexical_link__WEBPACK_IMPORTED_MODULE_8__.$isLinkNode)(parent) || (0,_lexical_link__WEBPACK_IMPORTED_MODULE_8__.$isLinkNode)(node));\n                    // Check for highlight using Lexical's built-in format\n                    setIsHighlight(selection.hasFormat(\"highlight\"));\n                } else {\n                    setIsVisible(false);\n                }\n            } else {\n                setIsVisible(false);\n            }\n        } else {\n            setIsVisible(false);\n        }\n    }, [\n        editor\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n        return editor.registerUpdateListener(function(param) {\n            var editorState = param.editorState;\n            editorState.read(function() {\n                updateToolbar();\n            });\n        });\n    }, [\n        editor,\n        updateToolbar\n    ]);\n    var handleFormat = function(format) {\n        editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_7__.FORMAT_TEXT_COMMAND, format);\n    };\n    var handleLink = function() {\n        if (isLink) {\n            editor.dispatchCommand(_lexical_link__WEBPACK_IMPORTED_MODULE_8__.TOGGLE_LINK_COMMAND, null);\n        } else {\n            var url = prompt(\"Enter URL:\");\n            if (url) {\n                editor.dispatchCommand(_lexical_link__WEBPACK_IMPORTED_MODULE_8__.TOGGLE_LINK_COMMAND, url);\n            }\n        }\n    };\n    if (!isVisible) {\n        return null;\n    }\n    var toolbarBg = theme === \"dark\" ? \"border-gray-600\" : \"border-gray-200\";\n    var buttonText = theme === \"dark\" ? \"text-white\" : \"text-gray-700\";\n    var buttonHover = theme === \"dark\" ? \"hover:text-white\" : \"hover:text-gray-900\";\n    var buttonActive = theme === \"dark\" ? \"text-white\" : \"text-gray-900\";\n    var toolbarButtons = [\n        {\n            title: \"Bold\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"font-bold text-sm\",\n                children: \"B\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 139,\n                columnNumber: 19\n            }, this),\n            isActive: isBold,\n            action: function() {\n                return handleFormat(\"bold\");\n            }\n        },\n        {\n            title: \"Strikethrough\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"line-through text-sm\",\n                children: \"S\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 145,\n                columnNumber: 19\n            }, this),\n            isActive: isStrikethrough,\n            action: function() {\n                return handleFormat(\"strikethrough\");\n            }\n        },\n        {\n            title: \"Underline\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"underline text-sm\",\n                children: \"U\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 151,\n                columnNumber: 19\n            }, this),\n            isActive: isUnderline,\n            action: function() {\n                return handleFormat(\"underline\");\n            }\n        },\n        {\n            title: \"Highlight\",\n            icon: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-xs px-1 rounded text-white\",\n                style: {\n                    backgroundColor: \"#3185eb\"\n                },\n                children: \"H\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 158,\n                columnNumber: 19\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-xs px-1 rounded\",\n                style: {\n                    backgroundColor: \"#eab834\"\n                },\n                children: \"H\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 159,\n                columnNumber: 19\n            }, this),\n            isActive: isHighlight,\n            action: function() {\n                return editor.dispatchCommand(_highlight_plugin__WEBPACK_IMPORTED_MODULE_1__.TOGGLE_HIGHLIGHT_COMMAND, undefined);\n            }\n        },\n        {\n            title: \"Code\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__.CodeIcon, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 165,\n                columnNumber: 19\n            }, this),\n            isActive: isCode,\n            action: function() {\n                return handleFormat(\"code\");\n            }\n        },\n        {\n            title: isLink ? \"Remove Link\" : \"Add Link\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__.LinkIcon, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 171,\n                columnNumber: 19\n            }, this),\n            isActive: isLink,\n            action: handleLink\n        },\n        // 分隔符\n        {\n            type: \"separator\"\n        },\n        {\n            title: \"Checklist\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm\",\n                children: \"☑\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 179,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: function() {\n                return editor.dispatchCommand(_lexical_list__WEBPACK_IMPORTED_MODULE_10__.INSERT_CHECK_LIST_COMMAND, undefined);\n            }\n        },\n        {\n            title: \"Bullet List\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm\",\n                children: \"•\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 185,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: function() {\n                return editor.dispatchCommand(_lexical_list__WEBPACK_IMPORTED_MODULE_10__.INSERT_UNORDERED_LIST_COMMAND, undefined);\n            }\n        },\n        {\n            title: \"Numbered List\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm\",\n                children: \"1.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 191,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: function() {\n                return editor.dispatchCommand(_lexical_list__WEBPACK_IMPORTED_MODULE_10__.INSERT_ORDERED_LIST_COMMAND, undefined);\n            }\n        },\n        // 分隔符\n        {\n            type: \"separator\"\n        },\n        {\n            title: \"Indent\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__.ArrowRightIcon, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 199,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: function() {\n                editor.update(function() {\n                    var selection = (0,lexical__WEBPACK_IMPORTED_MODULE_7__.$getSelection)();\n                    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_7__.$isRangeSelection)(selection)) {\n                        return;\n                    }\n                    var nodes = selection.getNodes();\n                    var listItemsToIndent = [];\n                    // 收集所有需要缩进的列表项\n                    nodes.forEach(function(node) {\n                        var listItemNode = null;\n                        if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$isListItemNode)(node)) {\n                            listItemNode = node;\n                        } else if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$isListItemNode)(node.getParent())) {\n                            listItemNode = node.getParent();\n                        }\n                        if (listItemNode && (0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$isListItemNode)(listItemNode) && !listItemsToIndent.includes(listItemNode)) {\n                            listItemsToIndent.push(listItemNode);\n                        }\n                    });\n                    if (listItemsToIndent.length > 0) {\n                        console.log(\"\\uD83D\\uDD27 开始处理列表缩进，找到\", listItemsToIndent.length, \"个列表项\");\n                        // 按文档顺序排序列表项\n                        listItemsToIndent.sort(function(a, b) {\n                            var aIndex = a.getIndexWithinParent();\n                            var bIndex = b.getIndexWithinParent();\n                            return aIndex - bIndex;\n                        });\n                        // 处理每个列表项的缩进\n                        listItemsToIndent.forEach(function(listItemNode, index) {\n                            console.log(\"\\uD83D\\uDD27 处理第\".concat(index + 1, \"个列表项\"));\n                            // 找到前一个兄弟节点作为父系\n                            var previousSibling = listItemNode.getPreviousSibling();\n                            console.log(\"\\uD83D\\uDD27 前一个兄弟节点:\", previousSibling ? \"存在\" : \"不存在\");\n                            if (previousSibling && (0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$isListItemNode)(previousSibling)) {\n                                console.log(\"\\uD83D\\uDD27 前一个兄弟是列表项，开始创建嵌套结构\");\n                                // 检查前一个兄弟是否已经有子列表\n                                var nestedList = null;\n                                var children = previousSibling.getChildren();\n                                var _iteratorNormalCompletion = true, _didIteratorError = false, _iteratorError = undefined;\n                                try {\n                                    // 查找是否已经有子列表\n                                    for(var _iterator = children[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true){\n                                        var child = _step.value;\n                                        if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$isListNode)(child)) {\n                                            nestedList = child;\n                                            console.log(\"\\uD83D\\uDD27 找到已存在的子列表\");\n                                            break;\n                                        }\n                                    }\n                                } catch (err) {\n                                    _didIteratorError = true;\n                                    _iteratorError = err;\n                                } finally{\n                                    try {\n                                        if (!_iteratorNormalCompletion && _iterator.return != null) {\n                                            _iterator.return();\n                                        }\n                                    } finally{\n                                        if (_didIteratorError) {\n                                            throw _iteratorError;\n                                        }\n                                    }\n                                }\n                                // 如果没有子列表，创建一个新的无序子列表\n                                if (!nestedList) {\n                                    nestedList = (0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$createListNode)(\"bullet\");\n                                    previousSibling.append(nestedList);\n                                    console.log(\"\\uD83D\\uDD27 创建新的无序子列表\");\n                                }\n                                // 🔧 关键修复：确保父列表项有内容，子列表项也有内容\n                                // 获取当前列表项的文本内容\n                                var listItemContent = listItemNode.getTextContent();\n                                console.log(\"\\uD83D\\uDD27 列表项内容:\", listItemContent);\n                                // 将当前列表项移动到子列表中\n                                listItemNode.remove();\n                                nestedList.append(listItemNode);\n                                console.log(\"\\uD83D\\uDD27 列表项已移动到子列表中\");\n                            } else {\n                                console.log(\"\\uD83D\\uDD27 没有合适的前一个兄弟节点，跳过此项\");\n                            }\n                        });\n                        console.log(\"\\uD83D\\uDD27 列表缩进处理完成\");\n                    } else {\n                        console.log(\"\\uD83D\\uDD27 没有找到列表项，使用通用缩进\");\n                        // 如果没有列表项，使用通用缩进\n                        editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_7__.INDENT_CONTENT_COMMAND, undefined);\n                    }\n                });\n            }\n        },\n        {\n            title: \"Outdent\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__.ArrowLeftIcon, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 292,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: function() {\n                // 暂时使用简单的反缩进逻辑，确保列表符号正常显示\n                editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_7__.OUTDENT_CONTENT_COMMAND, undefined);\n            }\n        }, \n    ];\n    return /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_3__.createPortal)(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed z-50 \".concat(toolbarBg, \" border rounded-lg p-1.5 flex space-x-0.5 shadow-lg\"),\n        style: {\n            top: position.top,\n            left: position.left,\n            transform: \"translateX(-50%)\",\n            backgroundColor: theme === \"dark\" ? \"#3f3f46\" : \"#e4e4e7\"\n        },\n        children: toolbarButtons.map(function(button, index) {\n            if (button.type === \"separator\") {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-px h-6 \".concat(theme === \"dark\" ? \"bg-gray-600\" : \"bg-gray-300\", \" mx-1\")\n                }, index, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                    lineNumber: 315,\n                    columnNumber: 25\n                }, _this);\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: button.action,\n                title: button.title,\n                className: \"\\n                            px-2.5 py-1.5 rounded transition-colors duration-150 min-w-[30px] h-7 flex items-center justify-center text-sm font-medium\\n                            \".concat(button.isActive ? buttonActive : \"\".concat(buttonText, \" \").concat(buttonHover), \"\\n                        \"),\n                style: {\n                    backgroundColor: button.isActive ? theme === \"dark\" ? \"#3185eb\" : \"#eab834\" : \"transparent\"\n                },\n                onMouseEnter: function(e) {\n                    if (!button.isActive) {\n                        e.currentTarget.style.backgroundColor = theme === \"dark\" ? \"#3185eb\" : \"#eab834\";\n                        if (theme === \"dark\") {\n                            e.currentTarget.style.color = \"white\";\n                        }\n                    }\n                },\n                onMouseLeave: function(e) {\n                    if (!button.isActive) {\n                        e.currentTarget.style.backgroundColor = \"transparent\";\n                        e.currentTarget.style.color = \"\";\n                    }\n                },\n                children: button.icon\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 323,\n                columnNumber: 21\n            }, _this);\n        })\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n        lineNumber: 303,\n        columnNumber: 9\n    }, this), document.body);\n}\n_s(FloatingToolbarPlugin, \"FbU03gSrzEPMPysVlTtv8K886Dg=\", false, function() {\n    return [\n        _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_6__.useLexicalComposerContext,\n        next_themes__WEBPACK_IMPORTED_MODULE_4__.useTheme\n    ];\n});\n_c = FloatingToolbarPlugin;\nvar _c;\n$RefreshReg$(_c, \"FloatingToolbarPlugin\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/editor/plugins/floating-toolbar-plugin.tsx\n"));

/***/ })

});