{"version": 3, "names": ["VISITOR_KEYS", "ALIAS_KEYS", "FLIPPED_ALIAS_KEYS", "NODE_FIELDS", "BUILDER_KEYS", "DEPRECATED_KEYS", "NODE_PARENT_VALIDATIONS", "getType", "val", "Array", "isArray", "validate", "typeIs", "typeName", "assertNodeType", "validateType", "validateOptional", "optional", "validateOptionalType", "arrayOf", "elementType", "chain", "assertValueType", "assertEach", "arrayOfType", "validateArrayOfType", "callback", "validator", "node", "key", "i", "length", "subkey", "v", "process", "env", "BABEL_TYPES_8_BREAKING", "validate<PERSON><PERSON><PERSON>", "each", "assertOneOf", "values", "indexOf", "TypeError", "JSON", "stringify", "oneOf", "types", "type", "is", "oneOfNodeTypes", "assertNodeOrValueType", "oneOfNodeOrValueTypes", "valid", "assertShape", "shape", "errors", "property", "Object", "keys", "validateField", "error", "push", "message", "join", "shapeOf", "assertOptionalChainStart", "current", "callee", "object", "fns", "args", "fn", "chainOf", "Error", "validTypeOpts", "valid<PERSON>ield<PERSON>eys", "defineAliasedType", "aliases", "opts", "defined", "inherits", "store", "slice", "additional", "filter", "a", "includes", "unshift", "defineType", "fields", "getOwnPropertyNames", "field", "def", "default", "visitor", "builder", "k", "depre<PERSON><PERSON><PERSON><PERSON>", "concat", "undefined", "for<PERSON>ach", "alias"], "sources": ["../../src/definitions/utils.ts"], "sourcesContent": ["import is from \"../validators/is\";\nimport { validateField, validateChild } from \"../validators/validate\";\nimport type * as t from \"..\";\n\nexport const VISITOR_KEYS: Record<string, string[]> = {};\nexport const ALIAS_KEYS: Partial<Record<NodeTypesWithoutComment, string[]>> =\n  {};\nexport const FLIPPED_ALIAS_KEYS: Record<string, NodeTypesWithoutComment[]> = {};\nexport const NODE_FIELDS: Record<string, FieldDefinitions> = {};\nexport const BUILDER_KEYS: Record<string, string[]> = {};\nexport const DEPRECATED_KEYS: Record<string, NodeTypesWithoutComment> = {};\nexport const NODE_PARENT_VALIDATIONS: Record<string, Validator> = {};\n\nfunction getType(val: any) {\n  if (Array.isArray(val)) {\n    return \"array\";\n  } else if (val === null) {\n    return \"null\";\n  } else {\n    return typeof val;\n  }\n}\n\ntype NodeTypesWithoutComment = t.Node[\"type\"] | keyof t.Aliases;\n\ntype NodeTypes = NodeTypesWithoutComment | t.Comment[\"type\"];\n\ntype PrimitiveTypes = ReturnType<typeof getType>;\n\ntype FieldDefinitions = {\n  [x: string]: FieldOptions;\n};\n\ntype DefineTypeOpts = {\n  fields?: FieldDefinitions;\n  visitor?: Array<string>;\n  aliases?: Array<string>;\n  builder?: Array<string>;\n  inherits?: NodeTypes;\n  deprecatedAlias?: string;\n  validate?: Validator;\n};\n\nexport type Validator = (\n  | { type: PrimitiveTypes }\n  | { each: Validator }\n  | { chainOf: Validator[] }\n  | { oneOf: any[] }\n  | { oneOfNodeTypes: NodeTypes[] }\n  | { oneOfNodeOrValueTypes: (NodeTypes | PrimitiveTypes)[] }\n  | { shapeOf: { [x: string]: FieldOptions } }\n  | {}\n) &\n  ((node: t.Node, key: string, val: any) => void);\n\nexport type FieldOptions = {\n  default?: string | number | boolean | [];\n  optional?: boolean;\n  validate?: Validator;\n};\n\nexport function validate(validate: Validator): FieldOptions {\n  return { validate };\n}\n\nexport function typeIs(typeName: NodeTypes | NodeTypes[]) {\n  return typeof typeName === \"string\"\n    ? assertNodeType(typeName)\n    : assertNodeType(...typeName);\n}\n\nexport function validateType(typeName: NodeTypes | NodeTypes[]) {\n  return validate(typeIs(typeName));\n}\n\nexport function validateOptional(validate: Validator): FieldOptions {\n  return { validate, optional: true };\n}\n\nexport function validateOptionalType(\n  typeName: NodeTypes | NodeTypes[],\n): FieldOptions {\n  return { validate: typeIs(typeName), optional: true };\n}\n\nexport function arrayOf(elementType: Validator): Validator {\n  return chain(assertValueType(\"array\"), assertEach(elementType));\n}\n\nexport function arrayOfType(typeName: NodeTypes | NodeTypes[]) {\n  return arrayOf(typeIs(typeName));\n}\n\nexport function validateArrayOfType(typeName: NodeTypes | NodeTypes[]) {\n  return validate(arrayOfType(typeName));\n}\n\nexport function assertEach(callback: Validator): Validator {\n  function validator(node: t.Node, key: string, val: any) {\n    if (!Array.isArray(val)) return;\n\n    for (let i = 0; i < val.length; i++) {\n      const subkey = `${key}[${i}]`;\n      const v = val[i];\n      callback(node, subkey, v);\n      if (process.env.BABEL_TYPES_8_BREAKING) validateChild(node, subkey, v);\n    }\n  }\n  validator.each = callback;\n  return validator;\n}\n\nexport function assertOneOf(...values: Array<any>): Validator {\n  function validate(node: any, key: string, val: any) {\n    if (values.indexOf(val) < 0) {\n      throw new TypeError(\n        `Property ${key} expected value to be one of ${JSON.stringify(\n          values,\n        )} but got ${JSON.stringify(val)}`,\n      );\n    }\n  }\n\n  validate.oneOf = values;\n\n  return validate;\n}\n\nexport function assertNodeType(...types: NodeTypes[]): Validator {\n  function validate(node: t.Node, key: string, val: any) {\n    for (const type of types) {\n      if (is(type, val)) {\n        validateChild(node, key, val);\n        return;\n      }\n    }\n\n    throw new TypeError(\n      `Property ${key} of ${\n        node.type\n      } expected node to be of a type ${JSON.stringify(\n        types,\n      )} but instead got ${JSON.stringify(val?.type)}`,\n    );\n  }\n\n  validate.oneOfNodeTypes = types;\n\n  return validate;\n}\n\nexport function assertNodeOrValueType(\n  ...types: (NodeTypes | PrimitiveTypes)[]\n): Validator {\n  function validate(node: t.Node, key: string, val: any) {\n    for (const type of types) {\n      if (getType(val) === type || is(type, val)) {\n        validateChild(node, key, val);\n        return;\n      }\n    }\n\n    throw new TypeError(\n      `Property ${key} of ${\n        node.type\n      } expected node to be of a type ${JSON.stringify(\n        types,\n      )} but instead got ${JSON.stringify(val?.type)}`,\n    );\n  }\n\n  validate.oneOfNodeOrValueTypes = types;\n\n  return validate;\n}\n\nexport function assertValueType(type: PrimitiveTypes): Validator {\n  function validate(node: t.Node, key: string, val: any) {\n    const valid = getType(val) === type;\n\n    if (!valid) {\n      throw new TypeError(\n        `Property ${key} expected type of ${type} but got ${getType(val)}`,\n      );\n    }\n  }\n\n  validate.type = type;\n\n  return validate;\n}\n\nexport function assertShape(shape: { [x: string]: FieldOptions }): Validator {\n  function validate(node: t.Node, key: string, val: any) {\n    const errors = [];\n    for (const property of Object.keys(shape)) {\n      try {\n        validateField(node, property, val[property], shape[property]);\n      } catch (error) {\n        if (error instanceof TypeError) {\n          errors.push(error.message);\n          continue;\n        }\n        throw error;\n      }\n    }\n    if (errors.length) {\n      throw new TypeError(\n        `Property ${key} of ${\n          node.type\n        } expected to have the following:\\n${errors.join(\"\\n\")}`,\n      );\n    }\n  }\n\n  validate.shapeOf = shape;\n\n  return validate;\n}\n\nexport function assertOptionalChainStart(): Validator {\n  function validate(node: t.Node) {\n    let current = node;\n    while (node) {\n      const { type } = current;\n      if (type === \"OptionalCallExpression\") {\n        if (current.optional) return;\n        current = current.callee;\n        continue;\n      }\n\n      if (type === \"OptionalMemberExpression\") {\n        if (current.optional) return;\n        current = current.object;\n        continue;\n      }\n\n      break;\n    }\n\n    throw new TypeError(\n      `Non-optional ${node.type} must chain from an optional OptionalMemberExpression or OptionalCallExpression. Found chain from ${current?.type}`,\n    );\n  }\n\n  return validate;\n}\n\nexport function chain(...fns: Array<Validator>): Validator {\n  function validate(...args: Parameters<Validator>) {\n    for (const fn of fns) {\n      fn(...args);\n    }\n  }\n  validate.chainOf = fns;\n\n  if (\n    fns.length >= 2 &&\n    \"type\" in fns[0] &&\n    fns[0].type === \"array\" &&\n    !(\"each\" in fns[1])\n  ) {\n    throw new Error(\n      `An assertValueType(\"array\") validator can only be followed by an assertEach(...) validator.`,\n    );\n  }\n\n  return validate;\n}\n\nconst validTypeOpts = [\n  \"aliases\",\n  \"builder\",\n  \"deprecatedAlias\",\n  \"fields\",\n  \"inherits\",\n  \"visitor\",\n  \"validate\",\n];\nconst validFieldKeys = [\"default\", \"optional\", \"validate\"];\n\n// Wraps defineType to ensure these aliases are included.\nexport function defineAliasedType(...aliases: string[]) {\n  return (type: string, opts: DefineTypeOpts = {}) => {\n    let defined = opts.aliases;\n    if (!defined) {\n      if (opts.inherits) defined = store[opts.inherits].aliases?.slice();\n      defined ??= [];\n      opts.aliases = defined;\n    }\n    const additional = aliases.filter(a => !defined.includes(a));\n    defined.unshift(...additional);\n    return defineType(type, opts);\n  };\n}\n\nexport default function defineType(type: string, opts: DefineTypeOpts = {}) {\n  const inherits = (opts.inherits && store[opts.inherits]) || {};\n\n  let fields = opts.fields;\n  if (!fields) {\n    fields = {};\n    if (inherits.fields) {\n      const keys = Object.getOwnPropertyNames(inherits.fields);\n      for (const key of keys) {\n        const field = inherits.fields[key];\n        const def = field.default;\n        if (\n          Array.isArray(def) ? def.length > 0 : def && typeof def === \"object\"\n        ) {\n          throw new Error(\n            \"field defaults can only be primitives or empty arrays currently\",\n          );\n        }\n        fields[key] = {\n          default: Array.isArray(def) ? [] : def,\n          optional: field.optional,\n          validate: field.validate,\n        };\n      }\n    }\n  }\n\n  const visitor: Array<string> = opts.visitor || inherits.visitor || [];\n  const aliases: Array<string> = opts.aliases || inherits.aliases || [];\n  const builder: Array<string> =\n    opts.builder || inherits.builder || opts.visitor || [];\n\n  for (const k of Object.keys(opts)) {\n    if (validTypeOpts.indexOf(k) === -1) {\n      throw new Error(`Unknown type option \"${k}\" on ${type}`);\n    }\n  }\n\n  if (opts.deprecatedAlias) {\n    DEPRECATED_KEYS[opts.deprecatedAlias] = type as NodeTypesWithoutComment;\n  }\n\n  // ensure all field keys are represented in `fields`\n  for (const key of visitor.concat(builder)) {\n    fields[key] = fields[key] || {};\n  }\n\n  for (const key of Object.keys(fields)) {\n    const field = fields[key];\n\n    if (field.default !== undefined && builder.indexOf(key) === -1) {\n      field.optional = true;\n    }\n    if (field.default === undefined) {\n      field.default = null;\n    } else if (!field.validate && field.default != null) {\n      field.validate = assertValueType(getType(field.default));\n    }\n\n    for (const k of Object.keys(field)) {\n      if (validFieldKeys.indexOf(k) === -1) {\n        throw new Error(`Unknown field key \"${k}\" on ${type}.${key}`);\n      }\n    }\n  }\n\n  VISITOR_KEYS[type] = opts.visitor = visitor;\n  BUILDER_KEYS[type] = opts.builder = builder;\n  NODE_FIELDS[type] = opts.fields = fields;\n  ALIAS_KEYS[type as NodeTypesWithoutComment] = opts.aliases = aliases;\n  aliases.forEach(alias => {\n    FLIPPED_ALIAS_KEYS[alias] = FLIPPED_ALIAS_KEYS[alias] || [];\n    FLIPPED_ALIAS_KEYS[alias].push(type as NodeTypesWithoutComment);\n  });\n\n  if (opts.validate) {\n    NODE_PARENT_VALIDATIONS[type] = opts.validate;\n  }\n\n  store[type] = opts;\n}\n\nconst store = {} as Record<string, DefineTypeOpts>;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AACA;;AAGO,MAAMA,YAAsC,GAAG,EAA/C;;AACA,MAAMC,UAA8D,GACzE,EADK;;AAEA,MAAMC,kBAA6D,GAAG,EAAtE;;AACA,MAAMC,WAA6C,GAAG,EAAtD;;AACA,MAAMC,YAAsC,GAAG,EAA/C;;AACA,MAAMC,eAAwD,GAAG,EAAjE;;AACA,MAAMC,uBAAkD,GAAG,EAA3D;;;AAEP,SAASC,OAAT,CAAiBC,GAAjB,EAA2B;EACzB,IAAIC,KAAK,CAACC,OAAN,CAAcF,GAAd,CAAJ,EAAwB;IACtB,OAAO,OAAP;EACD,CAFD,MAEO,IAAIA,GAAG,KAAK,IAAZ,EAAkB;IACvB,OAAO,MAAP;EACD,CAFM,MAEA;IACL,OAAO,OAAOA,GAAd;EACD;AACF;;AAwCM,SAASG,QAAT,CAAkBA,QAAlB,EAAqD;EAC1D,OAAO;IAAEA;EAAF,CAAP;AACD;;AAEM,SAASC,MAAT,CAAgBC,QAAhB,EAAmD;EACxD,OAAO,OAAOA,QAAP,KAAoB,QAApB,GACHC,cAAc,CAACD,QAAD,CADX,GAEHC,cAAc,CAAC,GAAGD,QAAJ,CAFlB;AAGD;;AAEM,SAASE,YAAT,CAAsBF,QAAtB,EAAyD;EAC9D,OAAOF,QAAQ,CAACC,MAAM,CAACC,QAAD,CAAP,CAAf;AACD;;AAEM,SAASG,gBAAT,CAA0BL,QAA1B,EAA6D;EAClE,OAAO;IAAEA,QAAF;IAAYM,QAAQ,EAAE;EAAtB,CAAP;AACD;;AAEM,SAASC,oBAAT,CACLL,QADK,EAES;EACd,OAAO;IAAEF,QAAQ,EAAEC,MAAM,CAACC,QAAD,CAAlB;IAA8BI,QAAQ,EAAE;EAAxC,CAAP;AACD;;AAEM,SAASE,OAAT,CAAiBC,WAAjB,EAAoD;EACzD,OAAOC,KAAK,CAACC,eAAe,CAAC,OAAD,CAAhB,EAA2BC,UAAU,CAACH,WAAD,CAArC,CAAZ;AACD;;AAEM,SAASI,WAAT,CAAqBX,QAArB,EAAwD;EAC7D,OAAOM,OAAO,CAACP,MAAM,CAACC,QAAD,CAAP,CAAd;AACD;;AAEM,SAASY,mBAAT,CAA6BZ,QAA7B,EAAgE;EACrE,OAAOF,QAAQ,CAACa,WAAW,CAACX,QAAD,CAAZ,CAAf;AACD;;AAEM,SAASU,UAAT,CAAoBG,QAApB,EAAoD;EACzD,SAASC,SAAT,CAAmBC,IAAnB,EAAiCC,GAAjC,EAA8CrB,GAA9C,EAAwD;IACtD,IAAI,CAACC,KAAK,CAACC,OAAN,CAAcF,GAAd,CAAL,EAAyB;;IAEzB,KAAK,IAAIsB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGtB,GAAG,CAACuB,MAAxB,EAAgCD,CAAC,EAAjC,EAAqC;MACnC,MAAME,MAAM,GAAI,GAAEH,GAAI,IAAGC,CAAE,GAA3B;MACA,MAAMG,CAAC,GAAGzB,GAAG,CAACsB,CAAD,CAAb;MACAJ,QAAQ,CAACE,IAAD,EAAOI,MAAP,EAAeC,CAAf,CAAR;MACA,IAAIC,OAAO,CAACC,GAAR,CAAYC,sBAAhB,EAAwC,IAAAC,uBAAA,EAAcT,IAAd,EAAoBI,MAApB,EAA4BC,CAA5B;IACzC;EACF;;EACDN,SAAS,CAACW,IAAV,GAAiBZ,QAAjB;EACA,OAAOC,SAAP;AACD;;AAEM,SAASY,WAAT,CAAqB,GAAGC,MAAxB,EAAuD;EAC5D,SAAS7B,QAAT,CAAkBiB,IAAlB,EAA6BC,GAA7B,EAA0CrB,GAA1C,EAAoD;IAClD,IAAIgC,MAAM,CAACC,OAAP,CAAejC,GAAf,IAAsB,CAA1B,EAA6B;MAC3B,MAAM,IAAIkC,SAAJ,CACH,YAAWb,GAAI,gCAA+Bc,IAAI,CAACC,SAAL,CAC7CJ,MAD6C,CAE7C,YAAWG,IAAI,CAACC,SAAL,CAAepC,GAAf,CAAoB,EAH7B,CAAN;IAKD;EACF;;EAEDG,QAAQ,CAACkC,KAAT,GAAiBL,MAAjB;EAEA,OAAO7B,QAAP;AACD;;AAEM,SAASG,cAAT,CAAwB,GAAGgC,KAA3B,EAA0D;EAC/D,SAASnC,QAAT,CAAkBiB,IAAlB,EAAgCC,GAAhC,EAA6CrB,GAA7C,EAAuD;IACrD,KAAK,MAAMuC,IAAX,IAAmBD,KAAnB,EAA0B;MACxB,IAAI,IAAAE,WAAA,EAAGD,IAAH,EAASvC,GAAT,CAAJ,EAAmB;QACjB,IAAA6B,uBAAA,EAAcT,IAAd,EAAoBC,GAApB,EAAyBrB,GAAzB;QACA;MACD;IACF;;IAED,MAAM,IAAIkC,SAAJ,CACH,YAAWb,GAAI,OACdD,IAAI,CAACmB,IACN,kCAAiCJ,IAAI,CAACC,SAAL,CAChCE,KADgC,CAEhC,oBAAmBH,IAAI,CAACC,SAAL,CAAepC,GAAf,oBAAeA,GAAG,CAAEuC,IAApB,CAA0B,EAL3C,CAAN;EAOD;;EAEDpC,QAAQ,CAACsC,cAAT,GAA0BH,KAA1B;EAEA,OAAOnC,QAAP;AACD;;AAEM,SAASuC,qBAAT,CACL,GAAGJ,KADE,EAEM;EACX,SAASnC,QAAT,CAAkBiB,IAAlB,EAAgCC,GAAhC,EAA6CrB,GAA7C,EAAuD;IACrD,KAAK,MAAMuC,IAAX,IAAmBD,KAAnB,EAA0B;MACxB,IAAIvC,OAAO,CAACC,GAAD,CAAP,KAAiBuC,IAAjB,IAAyB,IAAAC,WAAA,EAAGD,IAAH,EAASvC,GAAT,CAA7B,EAA4C;QAC1C,IAAA6B,uBAAA,EAAcT,IAAd,EAAoBC,GAApB,EAAyBrB,GAAzB;QACA;MACD;IACF;;IAED,MAAM,IAAIkC,SAAJ,CACH,YAAWb,GAAI,OACdD,IAAI,CAACmB,IACN,kCAAiCJ,IAAI,CAACC,SAAL,CAChCE,KADgC,CAEhC,oBAAmBH,IAAI,CAACC,SAAL,CAAepC,GAAf,oBAAeA,GAAG,CAAEuC,IAApB,CAA0B,EAL3C,CAAN;EAOD;;EAEDpC,QAAQ,CAACwC,qBAAT,GAAiCL,KAAjC;EAEA,OAAOnC,QAAP;AACD;;AAEM,SAASW,eAAT,CAAyByB,IAAzB,EAA0D;EAC/D,SAASpC,QAAT,CAAkBiB,IAAlB,EAAgCC,GAAhC,EAA6CrB,GAA7C,EAAuD;IACrD,MAAM4C,KAAK,GAAG7C,OAAO,CAACC,GAAD,CAAP,KAAiBuC,IAA/B;;IAEA,IAAI,CAACK,KAAL,EAAY;MACV,MAAM,IAAIV,SAAJ,CACH,YAAWb,GAAI,qBAAoBkB,IAAK,YAAWxC,OAAO,CAACC,GAAD,CAAM,EAD7D,CAAN;IAGD;EACF;;EAEDG,QAAQ,CAACoC,IAAT,GAAgBA,IAAhB;EAEA,OAAOpC,QAAP;AACD;;AAEM,SAAS0C,WAAT,CAAqBC,KAArB,EAAsE;EAC3E,SAAS3C,QAAT,CAAkBiB,IAAlB,EAAgCC,GAAhC,EAA6CrB,GAA7C,EAAuD;IACrD,MAAM+C,MAAM,GAAG,EAAf;;IACA,KAAK,MAAMC,QAAX,IAAuBC,MAAM,CAACC,IAAP,CAAYJ,KAAZ,CAAvB,EAA2C;MACzC,IAAI;QACF,IAAAK,uBAAA,EAAc/B,IAAd,EAAoB4B,QAApB,EAA8BhD,GAAG,CAACgD,QAAD,CAAjC,EAA6CF,KAAK,CAACE,QAAD,CAAlD;MACD,CAFD,CAEE,OAAOI,KAAP,EAAc;QACd,IAAIA,KAAK,YAAYlB,SAArB,EAAgC;UAC9Ba,MAAM,CAACM,IAAP,CAAYD,KAAK,CAACE,OAAlB;UACA;QACD;;QACD,MAAMF,KAAN;MACD;IACF;;IACD,IAAIL,MAAM,CAACxB,MAAX,EAAmB;MACjB,MAAM,IAAIW,SAAJ,CACH,YAAWb,GAAI,OACdD,IAAI,CAACmB,IACN,qCAAoCQ,MAAM,CAACQ,IAAP,CAAY,IAAZ,CAAkB,EAHnD,CAAN;IAKD;EACF;;EAEDpD,QAAQ,CAACqD,OAAT,GAAmBV,KAAnB;EAEA,OAAO3C,QAAP;AACD;;AAEM,SAASsD,wBAAT,GAA+C;EACpD,SAAStD,QAAT,CAAkBiB,IAAlB,EAAgC;IAAA;;IAC9B,IAAIsC,OAAO,GAAGtC,IAAd;;IACA,OAAOA,IAAP,EAAa;MACX,MAAM;QAAEmB;MAAF,IAAWmB,OAAjB;;MACA,IAAInB,IAAI,KAAK,wBAAb,EAAuC;QACrC,IAAImB,OAAO,CAACjD,QAAZ,EAAsB;QACtBiD,OAAO,GAAGA,OAAO,CAACC,MAAlB;QACA;MACD;;MAED,IAAIpB,IAAI,KAAK,0BAAb,EAAyC;QACvC,IAAImB,OAAO,CAACjD,QAAZ,EAAsB;QACtBiD,OAAO,GAAGA,OAAO,CAACE,MAAlB;QACA;MACD;;MAED;IACD;;IAED,MAAM,IAAI1B,SAAJ,CACH,gBAAed,IAAI,CAACmB,IAAK,qGAA1B,YAA8HmB,OAA9H,qBAA8H,SAASnB,IAAK,EADxI,CAAN;EAGD;;EAED,OAAOpC,QAAP;AACD;;AAEM,SAASU,KAAT,CAAe,GAAGgD,GAAlB,EAAoD;EACzD,SAAS1D,QAAT,CAAkB,GAAG2D,IAArB,EAAkD;IAChD,KAAK,MAAMC,EAAX,IAAiBF,GAAjB,EAAsB;MACpBE,EAAE,CAAC,GAAGD,IAAJ,CAAF;IACD;EACF;;EACD3D,QAAQ,CAAC6D,OAAT,GAAmBH,GAAnB;;EAEA,IACEA,GAAG,CAACtC,MAAJ,IAAc,CAAd,IACA,UAAUsC,GAAG,CAAC,CAAD,CADb,IAEAA,GAAG,CAAC,CAAD,CAAH,CAAOtB,IAAP,KAAgB,OAFhB,IAGA,EAAE,UAAUsB,GAAG,CAAC,CAAD,CAAf,CAJF,EAKE;IACA,MAAM,IAAII,KAAJ,CACH,6FADG,CAAN;EAGD;;EAED,OAAO9D,QAAP;AACD;;AAED,MAAM+D,aAAa,GAAG,CACpB,SADoB,EAEpB,SAFoB,EAGpB,iBAHoB,EAIpB,QAJoB,EAKpB,UALoB,EAMpB,SANoB,EAOpB,UAPoB,CAAtB;AASA,MAAMC,cAAc,GAAG,CAAC,SAAD,EAAY,UAAZ,EAAwB,UAAxB,CAAvB;;AAGO,SAASC,iBAAT,CAA2B,GAAGC,OAA9B,EAAiD;EACtD,OAAO,CAAC9B,IAAD,EAAe+B,IAAoB,GAAG,EAAtC,KAA6C;IAClD,IAAIC,OAAO,GAAGD,IAAI,CAACD,OAAnB;;IACA,IAAI,CAACE,OAAL,EAAc;MAAA;;MACZ,IAAID,IAAI,CAACE,QAAT,EAAmBD,OAAO,4BAAGE,KAAK,CAACH,IAAI,CAACE,QAAN,CAAL,CAAqBH,OAAxB,qBAAG,sBAA8BK,KAA9B,EAAV;MACnB,YAAAH,OAAO,SAAP,cAAAA,OAAO,GAAK,EAAZ;MACAD,IAAI,CAACD,OAAL,GAAeE,OAAf;IACD;;IACD,MAAMI,UAAU,GAAGN,OAAO,CAACO,MAAR,CAAeC,CAAC,IAAI,CAACN,OAAO,CAACO,QAAR,CAAiBD,CAAjB,CAArB,CAAnB;IACAN,OAAO,CAACQ,OAAR,CAAgB,GAAGJ,UAAnB;IACA,OAAOK,UAAU,CAACzC,IAAD,EAAO+B,IAAP,CAAjB;EACD,CAVD;AAWD;;AAEc,SAASU,UAAT,CAAoBzC,IAApB,EAAkC+B,IAAoB,GAAG,EAAzD,EAA6D;EAC1E,MAAME,QAAQ,GAAIF,IAAI,CAACE,QAAL,IAAiBC,KAAK,CAACH,IAAI,CAACE,QAAN,CAAvB,IAA2C,EAA5D;EAEA,IAAIS,MAAM,GAAGX,IAAI,CAACW,MAAlB;;EACA,IAAI,CAACA,MAAL,EAAa;IACXA,MAAM,GAAG,EAAT;;IACA,IAAIT,QAAQ,CAACS,MAAb,EAAqB;MACnB,MAAM/B,IAAI,GAAGD,MAAM,CAACiC,mBAAP,CAA2BV,QAAQ,CAACS,MAApC,CAAb;;MACA,KAAK,MAAM5D,GAAX,IAAkB6B,IAAlB,EAAwB;QACtB,MAAMiC,KAAK,GAAGX,QAAQ,CAACS,MAAT,CAAgB5D,GAAhB,CAAd;QACA,MAAM+D,GAAG,GAAGD,KAAK,CAACE,OAAlB;;QACA,IACEpF,KAAK,CAACC,OAAN,CAAckF,GAAd,IAAqBA,GAAG,CAAC7D,MAAJ,GAAa,CAAlC,GAAsC6D,GAAG,IAAI,OAAOA,GAAP,KAAe,QAD9D,EAEE;UACA,MAAM,IAAInB,KAAJ,CACJ,iEADI,CAAN;QAGD;;QACDgB,MAAM,CAAC5D,GAAD,CAAN,GAAc;UACZgE,OAAO,EAAEpF,KAAK,CAACC,OAAN,CAAckF,GAAd,IAAqB,EAArB,GAA0BA,GADvB;UAEZ3E,QAAQ,EAAE0E,KAAK,CAAC1E,QAFJ;UAGZN,QAAQ,EAAEgF,KAAK,CAAChF;QAHJ,CAAd;MAKD;IACF;EACF;;EAED,MAAMmF,OAAsB,GAAGhB,IAAI,CAACgB,OAAL,IAAgBd,QAAQ,CAACc,OAAzB,IAAoC,EAAnE;EACA,MAAMjB,OAAsB,GAAGC,IAAI,CAACD,OAAL,IAAgBG,QAAQ,CAACH,OAAzB,IAAoC,EAAnE;EACA,MAAMkB,OAAsB,GAC1BjB,IAAI,CAACiB,OAAL,IAAgBf,QAAQ,CAACe,OAAzB,IAAoCjB,IAAI,CAACgB,OAAzC,IAAoD,EADtD;;EAGA,KAAK,MAAME,CAAX,IAAgBvC,MAAM,CAACC,IAAP,CAAYoB,IAAZ,CAAhB,EAAmC;IACjC,IAAIJ,aAAa,CAACjC,OAAd,CAAsBuD,CAAtB,MAA6B,CAAC,CAAlC,EAAqC;MACnC,MAAM,IAAIvB,KAAJ,CAAW,wBAAuBuB,CAAE,QAAOjD,IAAK,EAAhD,CAAN;IACD;EACF;;EAED,IAAI+B,IAAI,CAACmB,eAAT,EAA0B;IACxB5F,eAAe,CAACyE,IAAI,CAACmB,eAAN,CAAf,GAAwClD,IAAxC;EACD;;EAGD,KAAK,MAAMlB,GAAX,IAAkBiE,OAAO,CAACI,MAAR,CAAeH,OAAf,CAAlB,EAA2C;IACzCN,MAAM,CAAC5D,GAAD,CAAN,GAAc4D,MAAM,CAAC5D,GAAD,CAAN,IAAe,EAA7B;EACD;;EAED,KAAK,MAAMA,GAAX,IAAkB4B,MAAM,CAACC,IAAP,CAAY+B,MAAZ,CAAlB,EAAuC;IACrC,MAAME,KAAK,GAAGF,MAAM,CAAC5D,GAAD,CAApB;;IAEA,IAAI8D,KAAK,CAACE,OAAN,KAAkBM,SAAlB,IAA+BJ,OAAO,CAACtD,OAAR,CAAgBZ,GAAhB,MAAyB,CAAC,CAA7D,EAAgE;MAC9D8D,KAAK,CAAC1E,QAAN,GAAiB,IAAjB;IACD;;IACD,IAAI0E,KAAK,CAACE,OAAN,KAAkBM,SAAtB,EAAiC;MAC/BR,KAAK,CAACE,OAAN,GAAgB,IAAhB;IACD,CAFD,MAEO,IAAI,CAACF,KAAK,CAAChF,QAAP,IAAmBgF,KAAK,CAACE,OAAN,IAAiB,IAAxC,EAA8C;MACnDF,KAAK,CAAChF,QAAN,GAAiBW,eAAe,CAACf,OAAO,CAACoF,KAAK,CAACE,OAAP,CAAR,CAAhC;IACD;;IAED,KAAK,MAAMG,CAAX,IAAgBvC,MAAM,CAACC,IAAP,CAAYiC,KAAZ,CAAhB,EAAoC;MAClC,IAAIhB,cAAc,CAAClC,OAAf,CAAuBuD,CAAvB,MAA8B,CAAC,CAAnC,EAAsC;QACpC,MAAM,IAAIvB,KAAJ,CAAW,sBAAqBuB,CAAE,QAAOjD,IAAK,IAAGlB,GAAI,EAArD,CAAN;MACD;IACF;EACF;;EAED7B,YAAY,CAAC+C,IAAD,CAAZ,GAAqB+B,IAAI,CAACgB,OAAL,GAAeA,OAApC;EACA1F,YAAY,CAAC2C,IAAD,CAAZ,GAAqB+B,IAAI,CAACiB,OAAL,GAAeA,OAApC;EACA5F,WAAW,CAAC4C,IAAD,CAAX,GAAoB+B,IAAI,CAACW,MAAL,GAAcA,MAAlC;EACAxF,UAAU,CAAC8C,IAAD,CAAV,GAA8C+B,IAAI,CAACD,OAAL,GAAeA,OAA7D;EACAA,OAAO,CAACuB,OAAR,CAAgBC,KAAK,IAAI;IACvBnG,kBAAkB,CAACmG,KAAD,CAAlB,GAA4BnG,kBAAkB,CAACmG,KAAD,CAAlB,IAA6B,EAAzD;IACAnG,kBAAkB,CAACmG,KAAD,CAAlB,CAA0BxC,IAA1B,CAA+Bd,IAA/B;EACD,CAHD;;EAKA,IAAI+B,IAAI,CAACnE,QAAT,EAAmB;IACjBL,uBAAuB,CAACyC,IAAD,CAAvB,GAAgC+B,IAAI,CAACnE,QAArC;EACD;;EAEDsE,KAAK,CAAClC,IAAD,CAAL,GAAc+B,IAAd;AACD;;AAED,MAAMG,KAAK,GAAG,EAAd"}