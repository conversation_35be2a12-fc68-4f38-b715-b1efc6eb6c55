/**
 * Test utilities for custom indent functionality
 * Used to verify that indentation is properly saved and restored
 */

// Test markdown with indentation
export const TEST_INDENT_MARKDOWN = `# Normal Heading

This is a normal paragraph.

<!-- indent:1 -->
This is an indented paragraph.

<!-- indent:2 -->
This is a double-indented paragraph.

<!-- indent:1 -->
## Indented Heading

<!-- indent:0 -->
Back to normal.

- Normal list item
<!-- indent:1 -->
- Indented list item
<!-- indent:2 -->
- Double-indented list item

1. Normal numbered item
<!-- indent:1 -->
1. Indented numbered item
`;

// Expected structure after parsing
export const EXPECTED_STRUCTURE = [
    { type: 'heading', level: 1, indent: 0, content: 'Normal Heading' },
    { type: 'paragraph', indent: 0, content: 'This is a normal paragraph.' },
    { type: 'paragraph', indent: 1, content: 'This is an indented paragraph.' },
    { type: 'paragraph', indent: 2, content: 'This is a double-indented paragraph.' },
    { type: 'heading', level: 2, indent: 1, content: 'Indented Heading' },
    { type: 'paragraph', indent: 0, content: 'Back to normal.' },
    { type: 'list-item', indent: 0, content: 'Normal list item' },
    { type: 'list-item', indent: 1, content: 'Indented list item' },
    { type: 'list-item', indent: 2, content: 'Double-indented list item' },
    { type: 'list-item', indent: 0, content: 'Normal numbered item' },
    { type: 'list-item', indent: 1, content: 'Indented numbered item' },
];

// Function to validate indent preservation
export function validateIndentPreservation(originalMarkdown: string, convertedMarkdown: string): boolean {
    const originalIndents = extractIndentLevels(originalMarkdown);
    const convertedIndents = extractIndentLevels(convertedMarkdown);
    
    if (originalIndents.length !== convertedIndents.length) {
        console.error('Indent count mismatch:', originalIndents.length, 'vs', convertedIndents.length);
        return false;
    }
    
    for (let i = 0; i < originalIndents.length; i++) {
        if (originalIndents[i] !== convertedIndents[i]) {
            console.error(`Indent level mismatch at position ${i}:`, originalIndents[i], 'vs', convertedIndents[i]);
            return false;
        }
    }
    
    return true;
}

// Extract indent levels from markdown
function extractIndentLevels(markdown: string): number[] {
    const indentRegex = /<!-- indent:(\d+) -->/g;
    const levels: number[] = [];
    let match;
    
    while ((match = indentRegex.exec(markdown)) !== null) {
        levels.push(parseInt(match[1]));
    }
    
    return levels;
}

// Debug function to log indent structure
export function debugIndentStructure(markdown: string): void {
    console.log('=== Indent Structure Debug ===');
    const lines = markdown.split('\n');
    let currentIndent = 0;
    
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        const indentMatch = line.match(/<!-- indent:(\d+) -->/);
        
        if (indentMatch) {
            currentIndent = parseInt(indentMatch[1]);
            console.log(`Line ${i + 1}: Set indent to ${currentIndent}`);
        } else if (line.trim()) {
            console.log(`Line ${i + 1}: [${currentIndent}] ${line.trim()}`);
        }
    }
    console.log('=== End Debug ===');
}
