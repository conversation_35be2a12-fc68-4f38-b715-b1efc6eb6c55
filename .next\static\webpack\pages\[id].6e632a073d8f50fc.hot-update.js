"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./libs/web/hooks/use-auto-save-on-leave.ts":
/*!**************************************************!*\
  !*** ./libs/web/hooks/use-auto-save-on-leave.ts ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/**\n * Auto Save on Leave Hook\n *\n * Copyright (c) 2025 waycaan\n * Licensed under the MIT License\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n */ \n\n\n\nvar useAutoSaveOnLeave = function() {\n    var options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    var _enabled = options.enabled, enabled = _enabled === void 0 ? true : _enabled;\n    var router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    var isAutoSavingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    var shouldAutoSave = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        // 🔧 新逻辑：基于内容比较而非按钮状态\n        if ( true && window.lexicalContentComparison) {\n            var hasChanges = window.lexicalContentComparison.hasChanges();\n            console.log(\"\\uD83D\\uDD27 useAutoSaveOnLeave: 检查内容变化 =\", hasChanges);\n            return hasChanges;\n        }\n        return false;\n    }, []);\n    var performAutoSave = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(function() {\n        var error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!( true && window.saveButtonAutoSave)) return [\n                        3,\n                        4\n                    ];\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        3,\n                        ,\n                        4\n                    ]);\n                    return [\n                        4,\n                        window.saveButtonAutoSave()\n                    ];\n                case 2:\n                    _state.sent();\n                    return [\n                        2,\n                        true\n                    ];\n                case 3:\n                    error = _state.sent();\n                    return [\n                        2,\n                        false\n                    ];\n                case 4:\n                    return [\n                        2,\n                        false\n                    ];\n            }\n        });\n    }), []);\n    var handleBeforeUnload = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(event) {\n        if (!enabled) return;\n        if (shouldAutoSave()) {\n            // 显示确认对话框，但不执行任何保存操作\n            event.preventDefault();\n            event.returnValue = \"您有未保存的更改。确定要离开吗？\";\n            // 使用一个简单的延迟来检测用户选择\n            // 如果用户选择\"离开\"，页面会立即卸载，这个setTimeout不会执行\n            // 如果用户选择\"取消\"，这个setTimeout会在用户回到页面后执行\n            setTimeout(function() {\n                // 如果能执行到这里，说明用户选择了\"取消\"\n                performAutoSave();\n            }, 100);\n            return \"您有未保存的更改。确定要离开吗？\";\n        }\n    }, [\n        enabled,\n        shouldAutoSave,\n        performAutoSave\n    ]);\n    var handleRouteChangeStart = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(function(url) {\n            var success, confirmed, error, confirmed1;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!enabled || isAutoSavingRef.current) return [\n                            2\n                        ];\n                        if (!shouldAutoSave()) return [\n                            3,\n                            4\n                        ];\n                        isAutoSavingRef.current = true;\n                        // 阻止路由跳转\n                        router.events.emit(\"routeChangeError\", new Error(\"Auto-saving before route change\"), url);\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            ,\n                            4\n                        ]);\n                        return [\n                            4,\n                            performAutoSave()\n                        ];\n                    case 2:\n                        success = _state.sent();\n                        isAutoSavingRef.current = false;\n                        if (success) {\n                            // 自动保存成功，继续跳转\n                            router.push(url);\n                        } else {\n                            confirmed = window.confirm(\"自动保存失败。是否强制离开？\");\n                            if (confirmed) {\n                                router.push(url);\n                            }\n                        }\n                        return [\n                            3,\n                            4\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        isAutoSavingRef.current = false;\n                        confirmed1 = window.confirm(\"自动保存出错。是否强制离开？\");\n                        if (confirmed1) {\n                            router.push(url);\n                        }\n                        return [\n                            3,\n                            4\n                        ];\n                    case 4:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function(url) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        enabled,\n        shouldAutoSave,\n        performAutoSave,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (!enabled) return;\n        window.addEventListener(\"beforeunload\", handleBeforeUnload);\n        return function() {\n            window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n        };\n    }, [\n        enabled,\n        handleBeforeUnload\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (!enabled) return;\n        router.events.on(\"routeChangeStart\", handleRouteChangeStart);\n        return function() {\n            router.events.off(\"routeChangeStart\", handleRouteChangeStart);\n        };\n    }, [\n        enabled,\n        handleRouteChangeStart,\n        router.events\n    ]);\n    return {\n        shouldAutoSave: shouldAutoSave,\n        performAutoSave: performAutoSave\n    };\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (useAutoSaveOnLeave);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./libs/web/hooks/use-auto-save-on-leave.ts\n"));

/***/ })

});