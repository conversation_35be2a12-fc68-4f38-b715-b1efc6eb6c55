"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./libs/web/state/lexical-editor.ts":
/*!******************************************!*\
  !*** ./libs/web/state/lexical-editor.ts ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/src/_object_spread.mjs */ \"./node_modules/@swc/helpers/src/_object_spread.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @swc/helpers/src/_object_spread_props.mjs */ \"./node_modules/@swc/helpers/src/_object_spread_props.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var libs_web_state_note__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! libs/web/state/note */ \"./libs/web/state/note.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var libs_shared_note__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! libs/shared/note */ \"./libs/shared/note.ts\");\n/* harmony import */ var libs_web_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! libs/web/hooks/use-toast */ \"./libs/web/hooks/use-toast.ts\");\n/* harmony import */ var libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! libs/web/cache/note */ \"./libs/web/cache/note.ts\");\n/* harmony import */ var unstated_next__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! unstated-next */ \"./node_modules/unstated-next/dist/unstated-next.mjs\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash */ \"./node_modules/lodash/lodash.js\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var libs_shared_markdown_parse_markdown_title__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! libs/shared/markdown/parse-markdown-title */ \"./libs/shared/markdown/parse-markdown-title.ts\");\n/* harmony import */ var libs_web_utils_ime_state_manager__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! libs/web/utils/ime-state-manager */ \"./libs/web/utils/ime-state-manager.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar ROOT_ID = \"root\";\nvar useLexicalEditor = function(initNote) {\n    // Use initNote if provided, otherwise try to get from NoteState\n    var note = initNote;\n    var createNoteWithTitle, updateNote, createNote;\n    try {\n        var noteState = libs_web_state_note__WEBPACK_IMPORTED_MODULE_0__[\"default\"].useContainer();\n        createNoteWithTitle = noteState.createNoteWithTitle;\n        updateNote = noteState.updateNote;\n        createNote = noteState.createNote;\n        // Only use noteState.note if no initNote is provided\n        if (!note) {\n            note = noteState.note;\n        }\n    } catch (error) {\n        // If NoteState is not available, we'll work with just the initNote\n        console.warn(\"NoteState not available in LexicalEditorState, using initNote only\");\n        createNoteWithTitle = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n        updateNote = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n        createNote = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n    }\n    var router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    var toast = (0,libs_web_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    var editorEl = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // Manual save function for IndexedDB\n    var saveToIndexedDB = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function(data) {\n            var existingNote, baseNote, updatedNote;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                            2\n                        ];\n                        return [\n                            4,\n                            libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                        ];\n                    case 1:\n                        existingNote = _state.sent();\n                        baseNote = existingNote || note;\n                        updatedNote = (0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_11__[\"default\"])({}, baseNote, data);\n                        return [\n                            4,\n                            libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].setItem(note.id, updatedNote)\n                        ];\n                    case 2:\n                        _state.sent();\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function(data) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        note\n    ]);\n    var syncToServer = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function() {\n        var isNew, localNote, noteToSave, noteData, item, noteUrl, updatedNote, currentContent, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        false\n                    ];\n                    isNew = (0,lodash__WEBPACK_IMPORTED_MODULE_6__.has)(router.query, \"new\");\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        11,\n                        ,\n                        12\n                    ]);\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                    ];\n                case 2:\n                    localNote = _state.sent();\n                    noteToSave = localNote || note;\n                    if (!isNew) return [\n                        3,\n                        7\n                    ];\n                    noteData = (0,_swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_12__[\"default\"])((0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_11__[\"default\"])({}, noteToSave), {\n                        pid: router.query.pid || ROOT_ID\n                    });\n                    return [\n                        4,\n                        createNote(noteData)\n                    ];\n                case 3:\n                    item = _state.sent();\n                    if (!item) return [\n                        3,\n                        6\n                    ];\n                    noteUrl = \"/\".concat(item.id);\n                    if (!(router.asPath !== noteUrl)) return [\n                        3,\n                        5\n                    ];\n                    return [\n                        4,\n                        router.replace(noteUrl, undefined, {\n                            shallow: true\n                        })\n                    ];\n                case 4:\n                    _state.sent();\n                    _state.label = 5;\n                case 5:\n                    toast(\"Note saved to server\", \"success\");\n                    return [\n                        2,\n                        true\n                    ];\n                case 6:\n                    return [\n                        3,\n                        10\n                    ];\n                case 7:\n                    return [\n                        4,\n                        updateNote(noteToSave)\n                    ];\n                case 8:\n                    updatedNote = _state.sent();\n                    if (!updatedNote) return [\n                        3,\n                        10\n                    ];\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].setItem(updatedNote.id, updatedNote)\n                    ];\n                case 9:\n                    _state.sent();\n                    toast(\"Note updated on server\", \"success\");\n                    // 🔧 保存成功后重置快照\n                    if ( true && window.lexicalContentComparison) {\n                        currentContent = window.lexicalContentComparison.getCurrentContent();\n                        window.lexicalContentComparison.resetSnapshot = function() {\n                            console.log(\"\\uD83D\\uDD27 保存成功，重置快照\");\n                        };\n                        // 触发快照重置\n                        if (window.lexicalContentComparison.resetSnapshot) {\n                            window.lexicalContentComparison.resetSnapshot();\n                        }\n                    }\n                    return [\n                        2,\n                        true\n                    ];\n                case 10:\n                    return [\n                        3,\n                        12\n                    ];\n                case 11:\n                    error = _state.sent();\n                    toast(\"Failed to save note to server\", \"error\");\n                    return [\n                        2,\n                        false\n                    ];\n                case 12:\n                    return [\n                        2,\n                        false\n                    ];\n            }\n        });\n    }), [\n        note,\n        router,\n        createNote,\n        updateNote,\n        toast\n    ]);\n    var onCreateLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function(title) {\n            var result;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!createNoteWithTitle) return [\n                            2,\n                            \"\"\n                        ];\n                        return [\n                            4,\n                            createNoteWithTitle(title)\n                        ];\n                    case 1:\n                        result = _state.sent();\n                        if (result === null || result === void 0 ? void 0 : result.id) {\n                            return [\n                                2,\n                                \"/\".concat(result.id)\n                            ];\n                        }\n                        return [\n                            2,\n                            \"\"\n                        ];\n                }\n            });\n        });\n        return function(title) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        createNoteWithTitle\n    ]);\n    var onSearchLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function(term) {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    []\n                ];\n            });\n        });\n        return function(term) {\n            return _ref.apply(this, arguments);\n        };\n    }(), []);\n    var onClickLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(href, event) {\n        if ((0,libs_shared_note__WEBPACK_IMPORTED_MODULE_3__.isNoteLink)(href)) {\n            event.preventDefault();\n            router.push(href);\n        } else {\n            window.open(href, \"_blank\", \"noopener,noreferrer\");\n        }\n    }, [\n        router\n    ]);\n    var onUploadImage = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function(_file, _id) {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(this, function(_state) {\n                // Image upload is disabled in PostgreSQL version\n                toast(\"Image upload is not supported in this version\", \"error\");\n                throw new Error(\"Image upload is not supported\");\n            });\n        });\n        return function(_file, _id) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        toast\n    ]);\n    var onHoverLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(event) {\n        return true;\n    }, []);\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(), backlinks = ref[0], setBackLinks = ref[1];\n    var getBackLinks = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function() {\n        var linkNotes;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    console.log(note === null || note === void 0 ? void 0 : note.id);\n                    linkNotes = [];\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        linkNotes\n                    ];\n                    setBackLinks([]);\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].iterate(function(value) {\n                            var ref;\n                            if ((ref = value.linkIds) === null || ref === void 0 ? void 0 : ref.includes((note === null || note === void 0 ? void 0 : note.id) || \"\")) {\n                                linkNotes.push(value);\n                            }\n                        })\n                    ];\n                case 1:\n                    _state.sent();\n                    setBackLinks(linkNotes);\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        note === null || note === void 0 ? void 0 : note.id\n    ]);\n    // 原始的编辑器变化处理逻辑\n    var originalOnEditorChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])(function(value) {\n            var content, title, currentTitle, titleInput, localNote, error, parsed;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        content = value();\n                        if (!(note === null || note === void 0 ? void 0 : note.isDailyNote)) return [\n                            3,\n                            1\n                        ];\n                        title = note.title;\n                        return [\n                            3,\n                            9\n                        ];\n                    case 1:\n                        currentTitle = \"\";\n                        titleInput = document.querySelector(\"h1 textarea\");\n                        if (!(titleInput && titleInput.value)) return [\n                            3,\n                            2\n                        ];\n                        currentTitle = titleInput.value.trim();\n                        return [\n                            3,\n                            8\n                        ];\n                    case 2:\n                        if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                            3,\n                            7\n                        ];\n                        _state.label = 3;\n                    case 3:\n                        _state.trys.push([\n                            3,\n                            5,\n                            ,\n                            6\n                        ]);\n                        return [\n                            4,\n                            libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                        ];\n                    case 4:\n                        localNote = _state.sent();\n                        currentTitle = (localNote === null || localNote === void 0 ? void 0 : localNote.title) || \"\";\n                        return [\n                            3,\n                            6\n                        ];\n                    case 5:\n                        error = _state.sent();\n                        currentTitle = (note === null || note === void 0 ? void 0 : note.title) || \"\";\n                        return [\n                            3,\n                            6\n                        ];\n                    case 6:\n                        return [\n                            3,\n                            8\n                        ];\n                    case 7:\n                        currentTitle = (note === null || note === void 0 ? void 0 : note.title) || \"\";\n                        _state.label = 8;\n                    case 8:\n                        if (!currentTitle || currentTitle === \"Untitled\" || currentTitle === \"New Page\" || currentTitle === \"\" || currentTitle.includes(\"<\") && currentTitle.includes(\">\")) {\n                            parsed = (0,libs_shared_markdown_parse_markdown_title__WEBPACK_IMPORTED_MODULE_7__.parseMarkdownTitle)(content);\n                            title = parsed.title || \"Untitled\"; // Use 'Untitled' if no title found\n                        } else {\n                            title = currentTitle;\n                        }\n                        _state.label = 9;\n                    case 9:\n                        // Save to IndexedDB immediately for local persistence\n                        return [\n                            4,\n                            saveToIndexedDB({\n                                content: content,\n                                title: title,\n                                updated_at: new Date().toISOString()\n                            })\n                        ];\n                    case 10:\n                        _state.sent();\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function(value) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        saveToIndexedDB,\n        note === null || note === void 0 ? void 0 : note.isDailyNote,\n        note === null || note === void 0 ? void 0 : note.id\n    ]);\n    // 使用智能onChange包装器 - 基于输入状态智能处理\n    var onEditorChange = (0,libs_web_utils_ime_state_manager__WEBPACK_IMPORTED_MODULE_8__.createSmartOnChange)(originalOnEditorChange, {\n        delay: 200 // 快速输入结束后200ms执行\n    });\n    // Function to handle title changes specifically\n    var onTitleChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(title) {\n        var ref;\n        (ref = saveToIndexedDB({\n            title: title,\n            updated_at: new Date().toISOString()\n        })) === null || ref === void 0 ? void 0 : ref.catch(function(v) {\n            return console.error(\"Error whilst saving title to IndexedDB: %O\", v);\n        });\n    }, [\n        saveToIndexedDB\n    ]);\n    return {\n        onCreateLink: onCreateLink,\n        onSearchLink: onSearchLink,\n        onClickLink: onClickLink,\n        onUploadImage: onUploadImage,\n        onHoverLink: onHoverLink,\n        getBackLinks: getBackLinks,\n        onEditorChange: onEditorChange,\n        onTitleChange: onTitleChange,\n        saveToIndexedDB: saveToIndexedDB,\n        syncToServer: syncToServer,\n        backlinks: backlinks,\n        editorEl: editorEl,\n        note: note\n    };\n};\nvar LexicalEditorState = (0,unstated_next__WEBPACK_IMPORTED_MODULE_13__.createContainer)(useLexicalEditor);\n/* harmony default export */ __webpack_exports__[\"default\"] = (LexicalEditorState);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./libs/web/state/lexical-editor.ts\n"));

/***/ })

});