"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./components/editor/plugins/floating-toolbar-plugin.tsx":
/*!***************************************************************!*\
  !*** ./components/editor/plugins/floating-toolbar-plugin.tsx ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FloatingToolbarPlugin; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/src/_sliced_to_array.mjs */ \"./node_modules/@swc/helpers/src/_sliced_to_array.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"./node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lexical */ \"./node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var _lexical_link__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @lexical/link */ \"./node_modules/@lexical/link/LexicalLink.dev.mjs\");\n/* harmony import */ var _lexical_list__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @lexical/list */ \"./node_modules/@lexical/list/LexicalList.dev.mjs\");\n/* harmony import */ var _highlight_plugin__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./highlight-plugin */ \"./components/editor/plugins/highlight-plugin.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-dom */ \"./node_modules/react-dom/index.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-themes */ \"./node_modules/next-themes/dist/index.modern.js\");\n/* harmony import */ var _heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @heroicons/react/outline */ \"./node_modules/@heroicons/react/outline/esm/index.js\");\n/**\n * Floating Toolbar Plugin for Lexical\n * Shows formatting options when text is selected\n */ \n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Heroicons\n\nfunction FloatingToolbarPlugin() {\n    var _this = this;\n    _s();\n    var ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_5__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_6__.useLexicalComposerContext)(), 1), editor = ref[0];\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isVisible = ref1[0], setIsVisible = ref1[1];\n    var ref2 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        top: 0,\n        left: 0\n    }), position = ref2[0], setPosition = ref2[1];\n    var ref3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isBold = ref3[0], setIsBold = ref3[1];\n    var ref4 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isUnderline = ref4[0], setIsUnderline = ref4[1];\n    var ref5 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isStrikethrough = ref5[0], setIsStrikethrough = ref5[1];\n    var ref6 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isCode = ref6[0], setIsCode = ref6[1];\n    var ref7 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isLink = ref7[0], setIsLink = ref7[1];\n    var ref8 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isHighlight = ref8[0], setIsHighlight = ref8[1];\n    var theme = (0,next_themes__WEBPACK_IMPORTED_MODULE_4__.useTheme)().theme;\n    var updateToolbar = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var selection = (0,lexical__WEBPACK_IMPORTED_MODULE_7__.$getSelection)();\n        if ((0,lexical__WEBPACK_IMPORTED_MODULE_7__.$isRangeSelection)(selection)) {\n            var anchorNode = selection.anchor.getNode();\n            var element = anchorNode.getKey() === \"root\" ? anchorNode : anchorNode.getTopLevelElementOrThrow();\n            var elementKey = element.getKey();\n            var elementDOM = editor.getElementByKey(elementKey);\n            // 显示条件：只有当有选中文本时才显示\n            var hasSelection = selection.getTextContent() !== \"\";\n            if (elementDOM !== null && hasSelection) {\n                var nativeSelection = window.getSelection();\n                var rootElement = editor.getRootElement();\n                if (nativeSelection !== null && rootElement !== null && rootElement.contains(nativeSelection.anchorNode)) {\n                    var rangeRect = nativeSelection.getRangeAt(0).getBoundingClientRect();\n                    setPosition({\n                        top: rangeRect.top - 60,\n                        left: rangeRect.left + rangeRect.width / 2 - 150\n                    });\n                    setIsVisible(true);\n                    // Update button states\n                    setIsBold(selection.hasFormat(\"bold\"));\n                    setIsUnderline(selection.hasFormat(\"underline\"));\n                    setIsStrikethrough(selection.hasFormat(\"strikethrough\"));\n                    setIsCode(selection.hasFormat(\"code\"));\n                    // Check if selection contains a link\n                    var node = selection.anchor.getNode();\n                    var parent = node.getParent();\n                    setIsLink((0,_lexical_link__WEBPACK_IMPORTED_MODULE_8__.$isLinkNode)(parent) || (0,_lexical_link__WEBPACK_IMPORTED_MODULE_8__.$isLinkNode)(node));\n                    // Check for highlight using Lexical's built-in format\n                    setIsHighlight(selection.hasFormat(\"highlight\"));\n                } else {\n                    setIsVisible(false);\n                }\n            } else {\n                setIsVisible(false);\n            }\n        } else {\n            setIsVisible(false);\n        }\n    }, [\n        editor\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n        return editor.registerUpdateListener(function(param) {\n            var editorState = param.editorState;\n            editorState.read(function() {\n                updateToolbar();\n            });\n        });\n    }, [\n        editor,\n        updateToolbar\n    ]);\n    var handleFormat = function(format) {\n        editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_7__.FORMAT_TEXT_COMMAND, format);\n    };\n    var handleLink = function() {\n        if (isLink) {\n            editor.dispatchCommand(_lexical_link__WEBPACK_IMPORTED_MODULE_8__.TOGGLE_LINK_COMMAND, null);\n        } else {\n            var url = prompt(\"Enter URL:\");\n            if (url) {\n                editor.dispatchCommand(_lexical_link__WEBPACK_IMPORTED_MODULE_8__.TOGGLE_LINK_COMMAND, url);\n            }\n        }\n    };\n    if (!isVisible) {\n        return null;\n    }\n    var toolbarBg = theme === \"dark\" ? \"border-gray-600\" : \"border-gray-200\";\n    var buttonText = theme === \"dark\" ? \"text-white\" : \"text-gray-700\";\n    var buttonHover = theme === \"dark\" ? \"hover:text-white\" : \"hover:text-gray-900\";\n    var buttonActive = theme === \"dark\" ? \"text-white\" : \"text-gray-900\";\n    var toolbarButtons = [\n        {\n            title: \"Bold\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"font-bold text-sm\",\n                children: \"B\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 139,\n                columnNumber: 19\n            }, this),\n            isActive: isBold,\n            action: function() {\n                return handleFormat(\"bold\");\n            }\n        },\n        {\n            title: \"Strikethrough\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"line-through text-sm\",\n                children: \"S\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 145,\n                columnNumber: 19\n            }, this),\n            isActive: isStrikethrough,\n            action: function() {\n                return handleFormat(\"strikethrough\");\n            }\n        },\n        {\n            title: \"Underline\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"underline text-sm\",\n                children: \"U\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 151,\n                columnNumber: 19\n            }, this),\n            isActive: isUnderline,\n            action: function() {\n                return handleFormat(\"underline\");\n            }\n        },\n        {\n            title: \"Highlight\",\n            icon: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-xs px-1 rounded text-white\",\n                style: {\n                    backgroundColor: \"#3185eb\"\n                },\n                children: \"H\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 158,\n                columnNumber: 19\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-xs px-1 rounded\",\n                style: {\n                    backgroundColor: \"#eab834\"\n                },\n                children: \"H\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 159,\n                columnNumber: 19\n            }, this),\n            isActive: isHighlight,\n            action: function() {\n                return editor.dispatchCommand(_highlight_plugin__WEBPACK_IMPORTED_MODULE_1__.TOGGLE_HIGHLIGHT_COMMAND, undefined);\n            }\n        },\n        {\n            title: \"Code\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__.CodeIcon, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 165,\n                columnNumber: 19\n            }, this),\n            isActive: isCode,\n            action: function() {\n                return handleFormat(\"code\");\n            }\n        },\n        {\n            title: isLink ? \"Remove Link\" : \"Add Link\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__.LinkIcon, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 171,\n                columnNumber: 19\n            }, this),\n            isActive: isLink,\n            action: handleLink\n        },\n        // 分隔符\n        {\n            type: \"separator\"\n        },\n        {\n            title: \"Checklist\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm\",\n                children: \"☑\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 179,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: function() {\n                return editor.dispatchCommand(_lexical_list__WEBPACK_IMPORTED_MODULE_10__.INSERT_CHECK_LIST_COMMAND, undefined);\n            }\n        },\n        {\n            title: \"Bullet List\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm\",\n                children: \"•\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 185,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: function() {\n                return editor.dispatchCommand(_lexical_list__WEBPACK_IMPORTED_MODULE_10__.INSERT_UNORDERED_LIST_COMMAND, undefined);\n            }\n        },\n        {\n            title: \"Numbered List\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm\",\n                children: \"1.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 191,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: function() {\n                return editor.dispatchCommand(_lexical_list__WEBPACK_IMPORTED_MODULE_10__.INSERT_ORDERED_LIST_COMMAND, undefined);\n            }\n        }\n    ];\n    return /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_3__.createPortal)(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed z-50 \".concat(toolbarBg, \" border rounded-lg p-1.5 flex space-x-0.5 shadow-lg\"),\n        style: {\n            top: position.top,\n            left: position.left,\n            transform: \"translateX(-50%)\",\n            backgroundColor: theme === \"dark\" ? \"#3f3f46\" : \"#e4e4e7\"\n        },\n        children: toolbarButtons.map(function(button, index) {\n            if (button.type === \"separator\") {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-px h-6 \".concat(theme === \"dark\" ? \"bg-gray-600\" : \"bg-gray-300\", \" mx-1\")\n                }, index, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 25\n                }, _this);\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: button.action,\n                title: button.title,\n                className: \"\\n                            px-2.5 py-1.5 rounded transition-colors duration-150 min-w-[30px] h-7 flex items-center justify-center text-sm font-medium\\n                            \".concat(button.isActive ? buttonActive : \"\".concat(buttonText, \" \").concat(buttonHover), \"\\n                        \"),\n                style: {\n                    backgroundColor: button.isActive ? theme === \"dark\" ? \"#3185eb\" : \"#eab834\" : \"transparent\"\n                },\n                onMouseEnter: function(e) {\n                    if (!button.isActive) {\n                        e.currentTarget.style.backgroundColor = theme === \"dark\" ? \"#3185eb\" : \"#eab834\";\n                        if (theme === \"dark\") {\n                            e.currentTarget.style.color = \"white\";\n                        }\n                    }\n                },\n                onMouseLeave: function(e) {\n                    if (!button.isActive) {\n                        e.currentTarget.style.backgroundColor = \"transparent\";\n                        e.currentTarget.style.color = \"\";\n                    }\n                },\n                children: button.icon\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 220,\n                columnNumber: 21\n            }, _this);\n        })\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n        lineNumber: 200,\n        columnNumber: 9\n    }, this), document.body);\n}\n_s(FloatingToolbarPlugin, \"FbU03gSrzEPMPysVlTtv8K886Dg=\", false, function() {\n    return [\n        _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_6__.useLexicalComposerContext,\n        next_themes__WEBPACK_IMPORTED_MODULE_4__.useTheme\n    ];\n});\n_c = FloatingToolbarPlugin;\nvar _c;\n$RefreshReg$(_c, \"FloatingToolbarPlugin\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/editor/plugins/floating-toolbar-plugin.tsx\n"));

/***/ })

});