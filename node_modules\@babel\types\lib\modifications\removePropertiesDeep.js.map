{"version": 3, "names": ["removePropertiesDeep", "tree", "opts", "traverseFast", "removeProperties"], "sources": ["../../src/modifications/removePropertiesDeep.ts"], "sourcesContent": ["import traverseFast from \"../traverse/traverseFast\";\nimport removeProperties from \"./removeProperties\";\nimport type * as t from \"..\";\n\nexport default function removePropertiesDeep<T extends t.Node>(\n  tree: T,\n  opts?: { preserveComments: boolean } | null,\n): T {\n  traverseFast(tree, removeProperties, opts);\n\n  return tree;\n}\n"], "mappings": ";;;;;;;AAAA;;AACA;;AAGe,SAASA,oBAAT,CACbC,IADa,EAEbC,IAFa,EAGV;EACH,IAAAC,qBAAA,EAAaF,IAAb,EAAmBG,yBAAnB,EAAqCF,IAArC;EAEA,OAAOD,IAAP;AACD"}