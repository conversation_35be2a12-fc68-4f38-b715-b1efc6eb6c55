{"version": 3, "names": ["shallowEqual", "actual", "expected", "keys", "Object", "key"], "sources": ["../../src/utils/shallowEqual.ts"], "sourcesContent": ["export default function shallowEqual<T extends object>(\n  actual: object,\n  expected: T,\n): actual is T {\n  const keys = Object.keys(expected) as (keyof T)[];\n\n  for (const key of keys) {\n    if (\n      // @ts-expect-error maybe we should check whether key exists first\n      actual[key] !== expected[key]\n    ) {\n      return false;\n    }\n  }\n\n  return true;\n}\n"], "mappings": ";;;;;;;AAAe,SAASA,YAAT,CACbC,MADa,EAEbC,QAFa,EAGA;EACb,MAAMC,IAAI,GAAGC,MAAM,CAACD,IAAP,CAAYD,QAAZ,CAAb;;EAEA,KAAK,MAAMG,GAAX,IAAkBF,IAAlB,EAAwB;IACtB,IAEEF,MAAM,CAACI,GAAD,CAAN,KAAgBH,QAAQ,CAACG,GAAD,CAF1B,EAGE;MACA,OAAO,KAAP;IACD;EACF;;EAED,OAAO,IAAP;AACD"}