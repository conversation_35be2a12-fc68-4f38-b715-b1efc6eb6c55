import { useAuth } from 'libs/server/middlewares/auth';
import { useStore } from 'libs/server/middlewares/store';
import AdmZip from 'adm-zip';
import { api } from 'libs/server/connect';
import TreeActions, {
    ROOT_ID,
    HierarchicalTreeItemModel,
} from 'libs/shared/tree';
import { getPathNoteById } from 'libs/server/note-path';
import { NOTE_DELETED } from 'libs/shared/meta';
import { metaToJson } from 'libs/server/meta';
import { toBuffer } from 'libs/shared/str';
import { convertHtmlToMarkdown } from 'libs/shared/html-to-markdown';

/**
 * 将Lexical JSON格式转换为Markdown
 * 这个函数需要在服务器端运行，所以不能直接使用Lexical编辑器
 */
function convertLexicalJSONToMarkdown(jsonContent: string): string {
    try {
        const editorState = JSON.parse(jsonContent);

        // 递归处理节点
        function processNode(node: any): string {
            if (!node) return '';

            switch (node.type) {
                case 'root':
                    return node.children ? node.children.map(processNode).join('') : '';

                case 'paragraph':
                    const paragraphText = node.children ? node.children.map(processNode).join('') : '';
                    return paragraphText ? paragraphText + '\n\n' : '';

                case 'heading':
                    const headingText = node.children ? node.children.map(processNode).join('') : '';
                    const level = node.tag ? parseInt(node.tag.replace('h', '')) : 1;
                    return '#'.repeat(level) + ' ' + headingText + '\n\n';

                case 'list':
                    const listType = node.listType || 'bullet';
                    const start = node.start || 1;
                    return processListNode(node, listType, start, 0);

                case 'listitem':
                    // 这个会在processListNode中处理
                    return '';

                case 'text':
                    let text = node.text || '';

                    // 处理格式
                    if (node.format) {
                        if (node.format & 1) text = `**${text}**`; // bold
                        if (node.format & 2) text = `*${text}*`;   // italic
                        if (node.format & 8) text = `\`${text}\``;  // code
                    }

                    return text;

                case 'code':
                    const codeText = node.children ? node.children.map(processNode).join('') : '';
                    const language = node.language || '';
                    return '```' + language + '\n' + codeText + '\n```\n\n';

                case 'quote':
                    const quoteText = node.children ? node.children.map(processNode).join('') : '';
                    return '> ' + quoteText.replace(/\n/g, '\n> ') + '\n\n';

                case 'horizontalrule':
                    return '---\n\n';

                default:
                    // 对于未知类型，尝试处理子节点
                    return node.children ? node.children.map(processNode).join('') : '';
            }
        }

        function processListNode(listNode: any, listType: string, start: number, depth: number): string {
            if (!listNode.children) return '';

            let result = '';
            const indent = '    '.repeat(depth);

            listNode.children.forEach((item: any, index: number) => {
                if (item.type === 'listitem') {
                    const itemText = item.children ? item.children.map((child: any) => {
                        if (child.type === 'list') {
                            // 嵌套列表
                            return '\n' + processListNode(child, child.listType || 'bullet', child.start || 1, depth + 1);
                        } else {
                            return processNode(child);
                        }
                    }).join('') : '';

                    if (listType === 'number') {
                        result += `${indent}${start + index}. ${itemText}\n`;
                    } else if (listType === 'check') {
                        const checked = item.checked ? 'x' : ' ';
                        result += `${indent}- [${checked}] ${itemText}\n`;
                    } else {
                        result += `${indent}- ${itemText}\n`;
                    }
                }
            });

            return result + (depth === 0 ? '\n' : '');
        }

        const markdown = processNode(editorState.root);
        return markdown.trim();

    } catch (error) {
        console.error('Error converting Lexical JSON to Markdown:', error);
        // 如果JSON解析失败，可能是旧的HTML格式，使用原有的转换方法
        return convertHtmlToMarkdown(jsonContent);
    }
}

export function escapeFileName(name: string): string {
    // list of characters taken from https://www.mtu.edu/umc/services/websites/writing/characters-avoid/
    return name.replace(/[#%&{}\\<>*?/$!'":@+`|=]/g, "_");
}

export default api()
    .use(useAuth)
    .use(useStore)
    .get(async (req, res) => {
        const pid = (req.query.pid as string) || ROOT_ID;
        const zip = new AdmZip();
        const tree = await req.state.treeStore.get();
        const rootItem = TreeActions.makeHierarchy(tree, pid);
        const duplicate: Record<string, number> = {};

        async function addItem(
            item: HierarchicalTreeItemModel,
            prefix: string = ''
        ): Promise<void> {
            const note = await req.state.store.getObjectAndMeta(
                getPathNoteById(item.id)
            );
            const metaJson = metaToJson(note.meta);

            if (metaJson.deleted === NOTE_DELETED.DELETED) {
                return;
            }
            const title = escapeFileName(metaJson.title ?? 'Untitled');

            const resolvedPrefix = prefix.length === 0 ? '' : prefix + '/';
            const basePath = resolvedPrefix + title;
            const uniquePath = duplicate[basePath]
                ? `${basePath} (${duplicate[basePath]})`
                : basePath;
            duplicate[basePath] = (duplicate[basePath] ?? 0) + 1;

            // 🔧 新逻辑：处理JSON格式的内容
            const markdownContent = note.content ? convertLexicalJSONToMarkdown(note.content) : '';
            zip.addFile(`${uniquePath}.md`, toBuffer(markdownContent));
            await Promise.all(item.children.map((v) => addItem(v, uniquePath)));
        }

        if (rootItem) {
            await Promise.all(rootItem.children.map((v) => addItem(v)));
        }

        res.setHeader('content-type', 'application/zip');
        res.setHeader('content-disposition', `attachment; filename=export.zip`);
        res.send(zip.toBuffer());
    });
