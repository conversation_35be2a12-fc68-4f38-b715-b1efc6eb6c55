"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./components/editor/lexical-editor.tsx":
/*!**********************************************!*\
  !*** ./components/editor/lexical-editor.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @swc/helpers/src/_sliced_to_array.mjs */ \"./node_modules/@swc/helpers/src/_sliced_to_array.mjs\");\n/* harmony import */ var _swc_helpers_src_to_consumable_array_mjs__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @swc/helpers/src/_to_consumable_array.mjs */ \"./node_modules/@swc/helpers/src/_to_consumable_array.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! lexical */ \"./node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalComposer__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @lexical/react/LexicalComposer */ \"./node_modules/@lexical/react/LexicalComposer.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"./node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalRichTextPlugin__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @lexical/react/LexicalRichTextPlugin */ \"./node_modules/@lexical/react/LexicalRichTextPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalContentEditable__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @lexical/react/LexicalContentEditable */ \"./node_modules/@lexical/react/LexicalContentEditable.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalHistoryPlugin__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @lexical/react/LexicalHistoryPlugin */ \"./node_modules/@lexical/react/LexicalHistoryPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalAutoFocusPlugin__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @lexical/react/LexicalAutoFocusPlugin */ \"./node_modules/@lexical/react/LexicalAutoFocusPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalOnChangePlugin__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @lexical/react/LexicalOnChangePlugin */ \"./node_modules/@lexical/react/LexicalOnChangePlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalLinkPlugin__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @lexical/react/LexicalLinkPlugin */ \"./node_modules/@lexical/react/LexicalLinkPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalListPlugin__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @lexical/react/LexicalListPlugin */ \"./node_modules/@lexical/react/LexicalListPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalMarkdownShortcutPlugin__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @lexical/react/LexicalMarkdownShortcutPlugin */ \"./node_modules/@lexical/react/LexicalMarkdownShortcutPlugin.dev.mjs\");\n/* harmony import */ var _lexical_markdown__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @lexical/markdown */ \"./node_modules/@lexical/markdown/LexicalMarkdown.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalErrorBoundary__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @lexical/react/LexicalErrorBoundary */ \"./node_modules/@lexical/react/LexicalErrorBoundary.dev.mjs\");\n/* harmony import */ var _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @lexical/rich-text */ \"./node_modules/@lexical/rich-text/LexicalRichText.dev.mjs\");\n/* harmony import */ var _lexical_list__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @lexical/list */ \"./node_modules/@lexical/list/LexicalList.dev.mjs\");\n/* harmony import */ var _lexical_code__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @lexical/code */ \"./node_modules/@lexical/code/LexicalCode.dev.mjs\");\n/* harmony import */ var _lexical_link__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @lexical/link */ \"./node_modules/@lexical/link/LexicalLink.dev.mjs\");\n/* harmony import */ var react_div_100vh__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-div-100vh */ \"./node_modules/react-div-100vh/dist/esm/index.js\");\n/* harmony import */ var libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! libs/web/hooks/use-mounted */ \"./libs/web/hooks/use-mounted.ts\");\n/* harmony import */ var libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! libs/web/hooks/use-i18n */ \"./libs/web/hooks/use-i18n.tsx\");\n/* harmony import */ var _plugins_slash_commands_plugin__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./plugins/slash-commands-plugin */ \"./components/editor/plugins/slash-commands-plugin.tsx\");\n/* harmony import */ var _plugins_floating_toolbar_plugin__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./plugins/floating-toolbar-plugin */ \"./components/editor/plugins/floating-toolbar-plugin.tsx\");\n/* harmony import */ var _lexical_react_LexicalCheckListPlugin__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @lexical/react/LexicalCheckListPlugin */ \"./node_modules/@lexical/react/LexicalCheckListPlugin.dev.mjs\");\n/* harmony import */ var _plugins_highlight_plugin__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./plugins/highlight-plugin */ \"./components/editor/plugins/highlight-plugin.tsx\");\n/* harmony import */ var _plugins_image_plugin__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./plugins/image-plugin */ \"./components/editor/plugins/image-plugin.tsx\");\n/* harmony import */ var _plugins_ime_plugin__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./plugins/ime-plugin */ \"./components/editor/plugins/ime-plugin.tsx\");\n/* harmony import */ var _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./nodes/image-node */ \"./components/editor/nodes/image-node.tsx\");\n/* harmony import */ var _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @lexical/react/LexicalHorizontalRuleNode */ \"./node_modules/@lexical/react/LexicalHorizontalRuleNode.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalHorizontalRulePlugin__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @lexical/react/LexicalHorizontalRulePlugin */ \"./node_modules/@lexical/react/LexicalHorizontalRulePlugin.dev.mjs\");\n/**\n * Lexical Editor Component\n * Migrated from TipTap to Lexical for better performance and modern architecture\n */ \n\nvar _this = undefined;\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Import custom plugins and nodes\n\n\n\n\n\n\n\n\n\nvar theme = {\n    ltr: \"ltr\",\n    rtl: \"rtl\",\n    placeholder: \"editor-placeholder\",\n    paragraph: \"editor-paragraph\",\n    quote: \"editor-quote\",\n    heading: {\n        h1: \"editor-heading-h1\",\n        h2: \"editor-heading-h2\",\n        h3: \"editor-heading-h3\",\n        h4: \"editor-heading-h4\",\n        h5: \"editor-heading-h5\",\n        h6: \"editor-heading-h6\"\n    },\n    list: {\n        nested: {\n            listitem: \"editor-nested-listitem\"\n        },\n        ol: \"editor-list-ol\",\n        ul: \"editor-list-ul\",\n        listitem: \"editor-listitem\",\n        // 按深度定义不同的列表样式 - 这是关键！\n        olDepth: [\n            \"editor-list-ol-1\",\n            \"editor-list-ol-2\",\n            \"editor-list-ol-3\",\n            \"editor-list-ol-4\",\n            \"editor-list-ol-5\" // I, II, III...\n        ],\n        ulDepth: [\n            \"editor-list-ul-1\",\n            \"editor-list-ul-2\",\n            \"editor-list-ul-3\",\n            \"editor-list-ul-4\",\n            \"editor-list-ul-5\" // ‣\n        ],\n        checklist: \"PlaygroundEditorTheme__checklist\",\n        listitemChecked: \"PlaygroundEditorTheme__listItemChecked\",\n        listitemUnchecked: \"PlaygroundEditorTheme__listItemUnchecked\"\n    },\n    // Lexical 0.32.1 原生缩进支持\n    indent: \"lexical-indent\",\n    image: \"editor-image\",\n    link: \"editor-link\",\n    text: {\n        bold: \"editor-text-bold\",\n        italic: \"editor-text-italic\",\n        overflowed: \"editor-text-overflowed\",\n        hashtag: \"editor-text-hashtag\",\n        underline: \"editor-text-underline\",\n        strikethrough: \"editor-text-strikethrough\",\n        underlineStrikethrough: \"editor-text-underlineStrikethrough\",\n        code: \"editor-text-code\",\n        highlight: \"editor-text-highlight\"\n    },\n    code: \"editor-code\",\n    codeHighlight: {\n        atrule: \"editor-tokenAttr\",\n        attr: \"editor-tokenAttr\",\n        boolean: \"editor-tokenProperty\",\n        builtin: \"editor-tokenSelector\",\n        cdata: \"editor-tokenComment\",\n        char: \"editor-tokenSelector\",\n        class: \"editor-tokenFunction\",\n        \"class-name\": \"editor-tokenFunction\",\n        comment: \"editor-tokenComment\",\n        constant: \"editor-tokenProperty\",\n        deleted: \"editor-tokenProperty\",\n        doctype: \"editor-tokenComment\",\n        entity: \"editor-tokenOperator\",\n        function: \"editor-tokenFunction\",\n        important: \"editor-tokenVariable\",\n        inserted: \"editor-tokenSelector\",\n        keyword: \"editor-tokenAttr\",\n        namespace: \"editor-tokenVariable\",\n        number: \"editor-tokenProperty\",\n        operator: \"editor-tokenOperator\",\n        prolog: \"editor-tokenComment\",\n        property: \"editor-tokenProperty\",\n        punctuation: \"editor-tokenPunctuation\",\n        regex: \"editor-tokenVariable\",\n        selector: \"editor-tokenSelector\",\n        string: \"editor-tokenSelector\",\n        symbol: \"editor-tokenProperty\",\n        tag: \"editor-tokenProperty\",\n        url: \"editor-tokenOperator\",\n        variable: \"editor-tokenVariable\"\n    }\n};\nfunction Placeholder() {\n    _s();\n    var t = (0,libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__[\"default\"])().t;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"editor-placeholder\",\n        children: t(\"Start writing...\")\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n        lineNumber: 153,\n        columnNumber: 12\n    }, this);\n}\n_s(Placeholder, \"82N5KF9nLzZ6+2WH7KIjzIXRkLw=\", false, function() {\n    return [\n        libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    ];\n});\n_c = Placeholder;\nvar LexicalEditor = /*#__PURE__*/ _s1((0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(_c1 = _s1(function(param, ref) {\n    var _readOnly = param.readOnly, readOnly = _readOnly === void 0 ? false : _readOnly, _value = param.value, value = _value === void 0 ? \"\" : _value, onChange = param.onChange, onClickLink = param.onClickLink, onHoverLink = param.onHoverLink, _className = param.className, className = _className === void 0 ? \"\" : _className;\n    _s1();\n    var _s = $RefreshSig$(), _s2 = $RefreshSig$();\n    var height = (0,react_div_100vh__WEBPACK_IMPORTED_MODULE_4__.use100vh)();\n    var mounted = (0,libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    var initialConfig = {\n        namespace: \"LexicalEditor\",\n        theme: theme,\n        onError: function onError(error) {\n            console.error(\"Lexical Error:\", error);\n        },\n        nodes: [\n            _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.HeadingNode,\n            _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListNode,\n            _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListItemNode,\n            _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.QuoteNode,\n            _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeNode,\n            _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeHighlightNode,\n            _lexical_link__WEBPACK_IMPORTED_MODULE_15__.AutoLinkNode,\n            _lexical_link__WEBPACK_IMPORTED_MODULE_15__.LinkNode,\n            // Lexical原生的ListItemNode已经支持checkbox功能\n            // 使用Lexical内置的highlight格式，不需要自定义HighlightNode\n            _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.ImageNode,\n            _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.HorizontalRuleNode, \n        ],\n        editable: !readOnly,\n        // 设置初始内容\n        editorState: value ? undefined : null\n    };\n    // 创建自定义transformers，包含图片支持\n    var IMAGE_TRANSFORMER = {\n        dependencies: [\n            _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.ImageNode\n        ],\n        export: function(node) {\n            if (!(0,_nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.$isImageNode)(node)) {\n                return null;\n            }\n            return \"![\".concat(node.getAltText(), \"](\").concat(node.getSrc(), \")\");\n        },\n        regExp: /!\\[([^\\]]*)\\]\\(([^)]+)\\)/,\n        replace: function(parentNode, children, match) {\n            var _match = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(match, 3), altText = _match[1], src = _match[2];\n            var imageNode = (0,_nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.$createImageNode)({\n                altText: altText,\n                src: src,\n                maxWidth: 800\n            });\n            children.forEach(function(child) {\n                return child.remove();\n            });\n            parentNode.append(imageNode);\n        },\n        type: \"element\"\n    };\n    // 创建自定义的下划线转换器，使用 <u>text</u> 语法\n    var UNDERLINE_TRANSFORMER = {\n        format: [\n            \"underline\"\n        ],\n        tag: \"<u>\",\n        type: \"text-format\"\n    };\n    // 移除段落缩进转换器 - 专注于列表缩进功能\n    // 段落缩进不是标准markdown语法，我们只处理列表缩进\n    // 创建水平分割线转换器\n    var HR_TRANSFORMER = {\n        dependencies: [\n            _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.HorizontalRuleNode\n        ],\n        export: function(node) {\n            return (0,_lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.$isHorizontalRuleNode)(node) ? \"---\" : null;\n        },\n        regExp: /^(---|\\*\\*\\*|___)\\s?$/,\n        replace: function(parentNode, children, match, isImport) {\n            var line = (0,_lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.$createHorizontalRuleNode)();\n            if (isImport || parentNode.getNextSibling() != null) {\n                parentNode.replace(line);\n            } else {\n                parentNode.insertBefore(line);\n            }\n            line.selectNext();\n        },\n        type: \"element\"\n    };\n    // 🔧 修复嵌套列表的Markdown输出 - 后处理方法\n    var fixNestedListMarkdown = function(markdown) {\n        var lines = markdown.split(\"\\n\");\n        var result = [];\n        for(var i = 0; i < lines.length; i++){\n            var line = lines[i];\n            // 检查是否是列表项\n            var listMatch = line.match(/^(\\d+)\\.\\s(.+)$/);\n            if (listMatch) {\n                var _listMatch = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(listMatch, 3), number = _listMatch[1], content = _listMatch[2];\n                // 检查内容中是否包含多个项目（被Lexical错误合并的）\n                // 寻找模式：文本 + emoji + 文本 (表示被合并的列表项)\n                var parts = content.split(/(?=[🎯✍️🧠💾⚙️🧱🔐🔁🖼️📦])/);\n                if (parts.length > 1 && parts[0].trim()) {\n                    // 第一部分作为主列表项\n                    result.push(\"\".concat(number, \". \").concat(parts[0].trim()));\n                    // 其余部分作为缩进的子列表项\n                    for(var j = 1; j < parts.length; j++){\n                        var part = parts[j].trim();\n                        if (part) {\n                            // 根据内容判断使用什么样的子列表符号\n                            if (j === 1) {\n                                result.push(\"    - \".concat(part)); // 第一个子项用 bullet\n                            } else {\n                                // 后续子项用字母编号\n                                var letter = String.fromCharCode(96 + j); // a, b, c...\n                                result.push(\"    \".concat(letter, \". \").concat(part));\n                            }\n                        }\n                    }\n                } else {\n                    // 没有被合并的内容，保持原样\n                    result.push(line);\n                }\n            } else {\n                // 非列表项，保持原样\n                result.push(line);\n            }\n        }\n        return result.join(\"\\n\");\n    };\n    // 使用标准的Lexical transformers\n    var customTransformers = (0,_swc_helpers_src_to_consumable_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])(_lexical_markdown__WEBPACK_IMPORTED_MODULE_19__.TRANSFORMERS).concat([\n        // 添加我们的自定义转换器\n        HR_TRANSFORMER,\n        UNDERLINE_TRANSFORMER,\n        IMAGE_TRANSFORMER\n    ]);\n    // 移除了自动完成插件 - 现在通过正确的transformer优先级来解决问题\n    var handleChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(editorState, _editor, tags) {\n        if (onChange) {\n            // 模仿TipTap的简单检查：忽略历史合并和内容同步触发的更新\n            if (tags.has(\"history-merge\") || tags.has(\"content-sync\")) {\n                return;\n            }\n            editorState.read(function() {\n                try {\n                    // 🔧 添加调试：检查编辑器状态中的列表结构\n                    var root = (0,lexical__WEBPACK_IMPORTED_MODULE_20__.$getRoot)();\n                    var children = root.getChildren();\n                    console.log(\"\\uD83D\\uDD27 编辑器根节点子元素数量:\", children.length);\n                    children.forEach(function(child, index) {\n                        if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListNode)(child)) {\n                            console.log(\"\\uD83D\\uDD27 第\".concat(index, \"个子元素是列表:\"), {\n                                type: child.getListType(),\n                                childrenCount: child.getChildren().length\n                            });\n                            var listItems = child.getChildren();\n                            listItems.forEach(function(item, itemIndex) {\n                                if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListItemNode)(item)) {\n                                    var itemChildren = item.getChildren();\n                                    console.log(\"\\uD83D\\uDD27   列表项\".concat(itemIndex, \":\"), {\n                                        childrenCount: itemChildren.length,\n                                        textContent: item.getTextContent(),\n                                        hasNestedList: itemChildren.some(function(c) {\n                                            return (0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListNode)(c);\n                                        })\n                                    });\n                                }\n                            });\n                        }\n                    });\n                    // 🔧 新方案：使用Lexical原生JSON格式保存，完美保持嵌套关系\n                    var editorStateJSON = JSON.stringify(editorState.toJSON());\n                    console.log(\"\\uD83D\\uDD27 Lexical原生JSON格式:\");\n                    console.log(editorStateJSON);\n                    // 简单的内容变化检查（比较JSON字符串）\n                    if (editorStateJSON !== value) {\n                        console.log(\"\\uD83D\\uDD27 编辑器状态改变，保存JSON格式\");\n                        onChange(function() {\n                            return editorStateJSON;\n                        });\n                        return;\n                    }\n                    // 如果需要Markdown格式（比如导出），可以单独生成\n                    var markdownContent = (0,_lexical_markdown__WEBPACK_IMPORTED_MODULE_19__.$convertToMarkdownString)(customTransformers);\n                    console.log(\"\\uD83D\\uDD27 Markdown格式（仅用于显示/导出）:\");\n                    console.log(markdownContent);\n                    // 调试：检查是否包含checkbox语法\n                    if (markdownContent.includes(\"[ ]\") || markdownContent.includes(\"[x]\")) {\n                        console.log(\"\\uD83D\\uDD0D Checkbox detected in markdown\");\n                    }\n                    // 不做任何额外处理，保持Lexical原生的markdown输出\n                    // Lexical的transformers已经正确处理了列表、checkbox等格式\n                    // 简单的内容变化检查\n                    if (markdownContent !== value) {\n                        console.log(\"\\uD83D\\uDD0D Content changed, calling onChange\");\n                        onChange(function() {\n                            return markdownContent;\n                        });\n                    }\n                } catch (error) {\n                    console.error(\"\\uD83D\\uDD0D Error in markdown conversion:\", error);\n                // 如果转换出错，保持原有内容不变\n                }\n            });\n        }\n    }, [\n        onChange,\n        value\n    ]);\n    // 列表退出处理插件 - 处理Enter+Enter退出列表的逻辑\n    var ListExitPlugin = function() {\n        _s();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_17__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            return editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_20__.KEY_ENTER_COMMAND, function(event) {\n                var selection = (0,lexical__WEBPACK_IMPORTED_MODULE_20__.$getSelection)();\n                if (!(0,lexical__WEBPACK_IMPORTED_MODULE_20__.$isRangeSelection)(selection)) {\n                    return false;\n                }\n                var anchorNode = selection.anchor.getNode();\n                // 检查是否在空的列表项中\n                if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListItemNode)(anchorNode)) {\n                    var textContent = anchorNode.getTextContent().trim();\n                    if (textContent === \"\") {\n                        var listNode = anchorNode.getParent();\n                        if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListNode)(listNode)) {\n                            // 如果是空的列表项，退出列表\n                            event === null || event === void 0 ? void 0 : event.preventDefault();\n                            // 创建新段落并在列表后插入\n                            var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_20__.$createParagraphNode)();\n                            listNode.insertAfter(paragraph);\n                            // 删除空的列表项\n                            anchorNode.remove();\n                            // 选中新段落\n                            paragraph.select();\n                            return true;\n                        }\n                    }\n                }\n                return false;\n            }, lexical__WEBPACK_IMPORTED_MODULE_20__.COMMAND_PRIORITY_HIGH);\n        }, [\n            editor\n        ]);\n        return null;\n    };\n    _s(ListExitPlugin, \"mCqe7sh4aC9mLBXPHfG3d/PNTaQ=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext\n        ];\n    });\n    // 内容同步组件 - 模仿TipTap的方式\n    var ContentSyncPlugin = function() {\n        _s2();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_17__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            if (editor && value !== undefined && mounted) {\n                editor.getEditorState().read(function() {\n                    var root = (0,lexical__WEBPACK_IMPORTED_MODULE_20__.$getRoot)();\n                    var currentContent = root.getTextContent();\n                    // 只有当内容真的不同时才更新\n                    if (value !== currentContent) {\n                        editor.update(function() {\n                            if (value.trim()) {\n                                try {\n                                    // 🔧 新方案：尝试解析JSON格式的编辑器状态\n                                    var editorStateData = JSON.parse(value);\n                                    console.log(\"\\uD83D\\uDD27 加载Lexical JSON格式内容\");\n                                    // 从JSON恢复编辑器状态\n                                    var newEditorState = editor.parseEditorState(editorStateData);\n                                    editor.setEditorState(newEditorState);\n                                } catch (jsonError) {\n                                    // 如果不是JSON格式，尝试作为Markdown解析（向后兼容）\n                                    console.log(\"\\uD83D\\uDD27 JSON解析失败，尝试Markdown格式\");\n                                    try {\n                                        (0,_lexical_markdown__WEBPACK_IMPORTED_MODULE_19__.$convertFromMarkdownString)(value, customTransformers);\n                                    } catch (markdownError) {\n                                        console.error(\"\\uD83D\\uDD27 Markdown解析也失败:\", markdownError);\n                                        // 创建空段落作为fallback\n                                        var root = (0,lexical__WEBPACK_IMPORTED_MODULE_20__.$getRoot)();\n                                        root.clear();\n                                        var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_20__.$createParagraphNode)();\n                                        root.append(paragraph);\n                                    }\n                                }\n                            } else {\n                                // 空内容时清空并创建一个空段落\n                                var root1 = (0,lexical__WEBPACK_IMPORTED_MODULE_20__.$getRoot)();\n                                root1.clear();\n                                var paragraph1 = (0,lexical__WEBPACK_IMPORTED_MODULE_20__.$createParagraphNode)();\n                                root1.append(paragraph1);\n                            }\n                        }, {\n                            tag: \"content-sync\"\n                        });\n                    }\n                });\n            }\n        }, [\n            editor,\n            value,\n            mounted\n        ]);\n        return null;\n    };\n    _s2(ContentSyncPlugin, \"mCqe7sh4aC9mLBXPHfG3d/PNTaQ=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext\n        ];\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useImperativeHandle)(ref, function() {\n        return {\n            focusAtEnd: function() {\n            // TODO: Implement focus at end\n            },\n            focusAtStart: function() {\n            // TODO: Implement focus at start\n            }\n        };\n    });\n    if (!mounted) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n            [\n                \"91be92fd7d9e7d9b\",\n                [\n                    height ? height + \"px\" : \"100vh\"\n                ]\n            ]\n        ]) + \" \" + \"lexical-editor \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalComposer__WEBPACK_IMPORTED_MODULE_22__.LexicalComposer, {\n                initialConfig: initialConfig,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                        [\n                            \"91be92fd7d9e7d9b\",\n                            [\n                                height ? height + \"px\" : \"100vh\"\n                            ]\n                        ]\n                    ]) + \" \" + \"editor-container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalRichTextPlugin__WEBPACK_IMPORTED_MODULE_23__.RichTextPlugin, {\n                            contentEditable: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalContentEditable__WEBPACK_IMPORTED_MODULE_24__.ContentEditable, {\n                                className: \"editor-input focus:outline-none w-full\",\n                                spellCheck: false\n                            }, void 0, false, void 0, void 0),\n                            placeholder: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Placeholder, {}, void 0, false, void 0, void 0),\n                            ErrorBoundary: _lexical_react_LexicalErrorBoundary__WEBPACK_IMPORTED_MODULE_25__.LexicalErrorBoundary\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 495,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalHistoryPlugin__WEBPACK_IMPORTED_MODULE_26__.HistoryPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 505,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalAutoFocusPlugin__WEBPACK_IMPORTED_MODULE_27__.AutoFocusPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 506,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalLinkPlugin__WEBPACK_IMPORTED_MODULE_28__.LinkPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 507,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalListPlugin__WEBPACK_IMPORTED_MODULE_29__.ListPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 508,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalCheckListPlugin__WEBPACK_IMPORTED_MODULE_30__.CheckListPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 509,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalMarkdownShortcutPlugin__WEBPACK_IMPORTED_MODULE_31__.MarkdownShortcutPlugin, {\n                            transformers: customTransformers\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_slash_commands_plugin__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 511,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_floating_toolbar_plugin__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 512,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_highlight_plugin__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 513,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_image_plugin__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 514,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalHorizontalRulePlugin__WEBPACK_IMPORTED_MODULE_32__.HorizontalRulePlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 515,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_ime_plugin__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            enabled: true,\n                            debug: \"development\" === \"development\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 516,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ListExitPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"91be92fd7d9e7d9b\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 518,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentSyncPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"91be92fd7d9e7d9b\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 521,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalOnChangePlugin__WEBPACK_IMPORTED_MODULE_33__.OnChangePlugin, {\n                            onChange: handleChange,\n                            ignoreHistoryMergeTagChange: true,\n                            ignoreSelectionChange: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 522,\n                            columnNumber: 21\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                    lineNumber: 494,\n                    columnNumber: 17\n                }, _this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                lineNumber: 493,\n                columnNumber: 13\n            }, _this),\n            (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"91be92fd7d9e7d9b\",\n                dynamic: [\n                    height ? height + \"px\" : \"100vh\"\n                ],\n                children: \".lexical-editor{position:relative}.editor-container{position:relative}.editor-input{outline:none;padding:1rem 0;min-height:-webkit-calc(\".concat(height ? height + \"px\" : \"100vh\", \" - 14rem);min-height:-moz-calc(\").concat(height ? height + \"px\" : \"100vh\", \" - 14rem);min-height:calc(\").concat(height ? height + \"px\" : \"100vh\", ' - 14rem);padding-bottom:10rem;width:100%;max-width:none;line-height:1.7;font-size:1rem;color:inherit;-webkit-spellcheck:false;-moz-spellcheck:false;-ms-spellcheck:false;spellcheck:false}.lexical-editor{--lexical-indent-base-value:32px}.lexical-indent{--lexical-indent-base-value:32px}.editor-input p[style*=\"margin-left\"],.editor-input h1[style*=\"margin-left\"],.editor-input h2[style*=\"margin-left\"],.editor-input h3[style*=\"margin-left\"],.editor-input h4[style*=\"margin-left\"],.editor-input h5[style*=\"margin-left\"],.editor-input h6[style*=\"margin-left\"],.editor-input li[style*=\"margin-left\"]{-webkit-transition:margin-left.2s ease;-moz-transition:margin-left.2s ease;-o-transition:margin-left.2s ease;transition:margin-left.2s ease}.editor-placeholder{color:#999;overflow:hidden;position:absolute;-o-text-overflow:ellipsis;text-overflow:ellipsis;top:1rem;left:0;font-size:1rem;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;display:inline-block;pointer-events:none}.editor-paragraph{margin:1.2rem 0;line-height:1.8}.editor-heading-h1{font-size:2.8em;font-weight:bold;margin:1.5rem 0 1rem 0;line-height:1.2}.editor-heading-h2{font-size:2.2em;font-weight:bold;margin:1.4rem 0 .8rem 0;line-height:1.3}.editor-heading-h3{font-size:1.8em;font-weight:bold;margin:1.3rem 0 .6rem 0;line-height:1.4}.editor-heading-h4{font-size:1.5em;font-weight:bold;margin:1.2rem 0 .5rem 0;line-height:1.4}.editor-heading-h5{font-size:1.3em;font-weight:bold;margin:1.1rem 0 .4rem 0;line-height:1.5}.editor-heading-h6{font-size:1.2em;font-weight:bold;margin:1rem 0 .3rem 0;line-height:1.5}.editor-quote{margin:1rem 0;padding-left:1rem;border-left:4px solid#ccc;font-style:italic;color:#666}.editor-list-ol,.editor-list-ul{margin:1rem 0;padding-left:2rem}.editor-listitem{margin:.5rem 0}.editor-link{color:#3b82f6;text-decoration:underline;cursor:pointer}.editor-link:hover{color:#1d4ed8}.editor-text-bold{font-weight:bold}.editor-text-italic{font-style:italic}.editor-text-underline{text-decoration:underline}.editor-text-strikethrough{text-decoration:line-through}.editor-text-code{background-color:#e4e4e7;color:black;padding:.2rem .4rem;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;font-family:monospace;font-size:.9em}.editor-code{background-color:#e4e4e7;color:black;border:1px solid#e9ecef;-webkit-border-radius:.375rem;-moz-border-radius:.375rem;border-radius:.375rem;padding:1rem;margin:1rem 0;font-family:\"Courier New\",Courier,monospace;font-size:.9em;line-height:1.4;overflow-x:auto}[data-theme=\"dark\"] .editor-text-code{background-color:#3f3f46;color:white}[data-theme=\"dark\"] .editor-code{background-color:#3f3f46;color:white;border-color:#4b5563}.editor-list-ol{list-style-type:decimal;margin:.5rem 0;padding-left:1.5rem}.editor-list-ul{list-style-type:disc;margin:.5rem 0;padding-left:1.5rem}.editor-listitem{margin:.4rem 0;line-height:1.8}.editor-nested-listitem{margin:.3rem 0}.editor-list-ol-1{list-style-type:decimal}.editor-list-ol-2{list-style-type:lower-alpha}.editor-list-ol-3{list-style-type:lower-roman}.editor-list-ol-4{list-style-type:upper-alpha}.editor-list-ol-5{list-style-type:upper-roman}.editor-list-ul-1{list-style-type:disc}.editor-list-ul-2{list-style-type:circle}.editor-list-ul-3{list-style-type:square}.editor-list-ul-4{list-style-type:disc}.editor-list-ul-5{list-style-type:circle}.task-list{list-style:none;padding-left:0;margin:1rem 0}.task-item{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:start;-webkit-align-items:flex-start;-moz-box-align:start;-ms-flex-align:start;align-items:flex-start;margin:.5rem 0;list-style:none}.task-checkbox{margin-right:.5rem;margin-top:.125rem;cursor:pointer;width:1rem;height:1rem;border:1px solid#d1d5db;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;background:white;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;-moz-appearance:none;-ms-appearance:none;appearance:none;-webkit-appearance:none}.task-checkbox:checked{background-color:#3b82f6;border-color:#3b82f6;color:white}.task-checkbox:checked::after{content:\"✓\";font-size:.75rem;font-weight:bold;color:white}.task-content{-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1;line-height:1.7}.task-item[data-checked=\"true\"] .task-content{text-decoration:line-through;opacity:.6}.lexical-highlight,mark,.editor-text-highlight{background-color:#eab834!important;color:black!important;padding:.1rem .2rem;-webkit-border-radius:.125rem;-moz-border-radius:.125rem;border-radius:.125rem}.dark .lexical-highlight,.dark mark,.dark .editor-text-highlight,[data-theme=\"dark\"] .lexical-highlight,[data-theme=\"dark\"] mark,[data-theme=\"dark\"] .editor-text-highlight,html.dark .lexical-highlight,html.dark mark,html.dark .editor-text-highlight{background-color:#3185eb!important;color:white!important}.editor-image img{max-width:100%;height:auto;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;margin:1rem 0}@media(prefers-color-scheme:dark){.editor-placeholder{color:#6b7280}.editor-quote{border-left-color:#4b5563;color:#9ca3af}.editor-text-code{background-color:#374151;color:#f9fafb}.editor-code{background-color:#1f2937;border-color:#374151;color:#f9fafb}}.lexical-editor ul:not([data-lexical-list-type=\"check\"]) li::marker{color:#6b7280}.lexical-editor ol li::marker{color:#6b7280}.lexical-editor .PlaygroundEditorTheme__listItemChecked,.lexical-editor .PlaygroundEditorTheme__listItemUnchecked{position:relative;margin-left:.5em;margin-right:.5em;padding-left:1.5em;padding-right:1.5em;list-style-type:none;outline:none;display:block;min-height:1.5em}.lexical-editor .PlaygroundEditorTheme__listItemChecked>*,.lexical-editor .PlaygroundEditorTheme__listItemUnchecked>*{margin-left:.01em}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before,.lexical-editor .PlaygroundEditorTheme__listItemChecked:before{content:\"\";width:.9em;height:.9em;top:50%;left:0;cursor:pointer;display:block;-webkit-background-size:cover;-moz-background-size:cover;-o-background-size:cover;background-size:cover;position:absolute;-webkit-transform:translateY(-50%);-moz-transform:translateY(-50%);-ms-transform:translateY(-50%);-o-transform:translateY(-50%);transform:translateY(-50%)}.lexical-editor .PlaygroundEditorTheme__listItemChecked{text-decoration:line-through}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:focus:before,.lexical-editor .PlaygroundEditorTheme__listItemChecked:focus:before{-webkit-box-shadow:0 0 0 2px#a6cdfe;-moz-box-shadow:0 0 0 2px#a6cdfe;box-shadow:0 0 0 2px#a6cdfe;-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before{border:1px solid#999;-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px}.lexical-editor .PlaygroundEditorTheme__listItemChecked:before{border:1px solid rgb(61,135,245);-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px;background-color:#3d87f5;background-repeat:no-repeat}.lexical-editor .PlaygroundEditorTheme__listItemChecked:after{content:\"\";cursor:pointer;border-color:#fff;border-style:solid;position:absolute;display:block;top:45%;width:.2em;left:.35em;height:.4em;-webkit-transform:translateY(-50%)rotate(45deg);-moz-transform:translateY(-50%)rotate(45deg);-ms-transform:translateY(-50%)rotate(45deg);-o-transform:translateY(-50%)rotate(45deg);transform:translateY(-50%)rotate(45deg);border-width:0 .1em .1em 0}')\n            }, void 0, false, void 0, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n        lineNumber: 492,\n        columnNumber: 9\n    }, _this);\n}, \"cp2mgoBaCDop5vv5Ezf6hVUYfiE=\", false, function() {\n    return [\n        libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n})), \"cp2mgoBaCDop5vv5Ezf6hVUYfiE=\", false, function() {\n    return [\n        libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n});\n_c2 = LexicalEditor;\nLexicalEditor.displayName = \"LexicalEditor\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (LexicalEditor);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Placeholder\");\n$RefreshReg$(_c1, \"LexicalEditor$forwardRef\");\n$RefreshReg$(_c2, \"LexicalEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/editor/lexical-editor.tsx\n"));

/***/ })

});