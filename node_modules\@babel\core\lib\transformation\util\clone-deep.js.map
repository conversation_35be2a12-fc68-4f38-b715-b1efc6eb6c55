{"version": 3, "names": ["deepClone", "value", "cache", "has", "get", "cloned", "Array", "isArray", "length", "i", "keys", "Object", "key", "set", "Map"], "sources": ["../../../src/transformation/util/clone-deep.ts"], "sourcesContent": ["//https://github.com/babel/babel/pull/14583#discussion_r882828856\nfunction deepClone(value: any, cache: Map<any, any>): any {\n  if (value !== null) {\n    if (cache.has(value)) return cache.get(value);\n    let cloned: any;\n    if (Array.isArray(value)) {\n      cloned = new Array(value.length);\n      for (let i = 0; i < value.length; i++) {\n        cloned[i] =\n          typeof value[i] !== \"object\" ? value[i] : deepClone(value[i], cache);\n      }\n    } else {\n      cloned = {};\n      const keys = Object.keys(value);\n      for (let i = 0; i < keys.length; i++) {\n        const key = keys[i];\n        cloned[key] =\n          typeof value[key] !== \"object\"\n            ? value[key]\n            : deepClone(value[key], cache);\n      }\n    }\n    cache.set(value, cloned);\n    return cloned;\n  }\n  return value;\n}\n\nexport default function <T>(value: T): T {\n  if (typeof value !== \"object\") return value;\n  return deepClone(value, new Map());\n}\n"], "mappings": ";;;;;;;AACA,SAASA,SAAT,CAAmBC,KAAnB,EAA+BC,KAA/B,EAA0D;EACxD,IAAID,KAAK,KAAK,IAAd,EAAoB;IAClB,IAAIC,KAAK,CAACC,GAAN,CAAUF,KAAV,CAAJ,EAAsB,OAAOC,KAAK,CAACE,GAAN,CAAUH,KAAV,CAAP;IACtB,IAAII,MAAJ;;IACA,IAAIC,KAAK,CAACC,OAAN,CAAcN,KAAd,CAAJ,EAA0B;MACxBI,MAAM,GAAG,IAAIC,KAAJ,CAAUL,KAAK,CAACO,MAAhB,CAAT;;MACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGR,KAAK,CAACO,MAA1B,EAAkCC,CAAC,EAAnC,EAAuC;QACrCJ,MAAM,CAACI,CAAD,CAAN,GACE,OAAOR,KAAK,CAACQ,CAAD,CAAZ,KAAoB,QAApB,GAA+BR,KAAK,CAACQ,CAAD,CAApC,GAA0CT,SAAS,CAACC,KAAK,CAACQ,CAAD,CAAN,EAAWP,KAAX,CADrD;MAED;IACF,CAND,MAMO;MACLG,MAAM,GAAG,EAAT;MACA,MAAMK,IAAI,GAAGC,MAAM,CAACD,IAAP,CAAYT,KAAZ,CAAb;;MACA,KAAK,IAAIQ,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGC,IAAI,CAACF,MAAzB,EAAiCC,CAAC,EAAlC,EAAsC;QACpC,MAAMG,GAAG,GAAGF,IAAI,CAACD,CAAD,CAAhB;QACAJ,MAAM,CAACO,GAAD,CAAN,GACE,OAAOX,KAAK,CAACW,GAAD,CAAZ,KAAsB,QAAtB,GACIX,KAAK,CAACW,GAAD,CADT,GAEIZ,SAAS,CAACC,KAAK,CAACW,GAAD,CAAN,EAAaV,KAAb,CAHf;MAID;IACF;;IACDA,KAAK,CAACW,GAAN,CAAUZ,KAAV,EAAiBI,MAAjB;IACA,OAAOA,MAAP;EACD;;EACD,OAAOJ,KAAP;AACD;;AAEc,kBAAaA,KAAb,EAA0B;EACvC,IAAI,OAAOA,KAAP,KAAiB,QAArB,EAA+B,OAAOA,KAAP;EAC/B,OAAOD,SAAS,CAACC,KAAD,EAAQ,IAAIa,GAAJ,EAAR,CAAhB;AACD"}