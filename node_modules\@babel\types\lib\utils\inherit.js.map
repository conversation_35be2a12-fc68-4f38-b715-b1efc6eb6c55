{"version": 3, "names": ["inherit", "key", "child", "parent", "Array", "from", "Set", "concat", "filter", "Boolean"], "sources": ["../../src/utils/inherit.ts"], "sourcesContent": ["import type * as t from \"..\";\n\nexport default function inherit<\n  C extends t.Node | undefined,\n  P extends t.Node | undefined,\n>(key: keyof C & keyof P, child: C, parent: P): void {\n  if (child && parent) {\n    // @ts-expect-error Could further refine key definitions\n    child[key] = Array.from(\n      new Set([].concat(child[key], parent[key]).filter(Boolean)),\n    );\n  }\n}\n"], "mappings": ";;;;;;;AAEe,SAASA,OAAT,CAGbC,GAHa,EAGWC,KAHX,EAGqBC,MAHrB,EAGsC;EACnD,IAAID,KAAK,IAAIC,MAAb,EAAqB;IAEnBD,KAAK,CAACD,GAAD,CAAL,GAAaG,KAAK,CAACC,IAAN,CACX,IAAIC,GAAJ,CAAQ,GAAGC,MAAH,CAAUL,KAAK,CAACD,GAAD,CAAf,EAAsBE,MAAM,CAACF,GAAD,CAA5B,EAAmCO,MAAnC,CAA0CC,OAA1C,CAAR,CADW,CAAb;EAGD;AACF"}