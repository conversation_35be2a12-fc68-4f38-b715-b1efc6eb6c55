"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./libs/web/state/lexical-editor.ts":
/*!******************************************!*\
  !*** ./libs/web/state/lexical-editor.ts ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/src/_object_spread.mjs */ \"./node_modules/@swc/helpers/src/_object_spread.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/src/_object_spread_props.mjs */ \"./node_modules/@swc/helpers/src/_object_spread_props.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var libs_web_state_note__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! libs/web/state/note */ \"./libs/web/state/note.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var libs_shared_note__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! libs/shared/note */ \"./libs/shared/note.ts\");\n/* harmony import */ var libs_web_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! libs/web/hooks/use-toast */ \"./libs/web/hooks/use-toast.ts\");\n/* harmony import */ var libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! libs/web/cache/note */ \"./libs/web/cache/note.ts\");\n/* harmony import */ var unstated_next__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! unstated-next */ \"./node_modules/unstated-next/dist/unstated-next.mjs\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash */ \"./node_modules/lodash/lodash.js\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_6__);\n\n\n\n\n\n\n\n\n\n\n\n\nvar ROOT_ID = \"root\";\nvar useLexicalEditor = function(initNote) {\n    // Use initNote if provided, otherwise try to get from NoteState\n    var note = initNote;\n    var createNoteWithTitle, updateNote, createNote;\n    try {\n        var noteState = libs_web_state_note__WEBPACK_IMPORTED_MODULE_0__[\"default\"].useContainer();\n        createNoteWithTitle = noteState.createNoteWithTitle;\n        updateNote = noteState.updateNote;\n        createNote = noteState.createNote;\n        // Only use noteState.note if no initNote is provided\n        if (!note) {\n            note = noteState.note;\n        }\n    } catch (error) {\n        // If NoteState is not available, we'll work with just the initNote\n        console.warn(\"NoteState not available in LexicalEditorState, using initNote only\");\n        createNoteWithTitle = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n        updateNote = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n        createNote = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n    }\n    var router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    var toast = (0,libs_web_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    var editorEl = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // 🔧 新增：快照状态管理\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null), noteSnapshot = ref[0], setNoteSnapshot = ref[1];\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\"), currentEditorContent = ref1[0], setCurrentEditorContent = ref1[1];\n    // Manual save function for IndexedDB\n    var saveToIndexedDB = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(data) {\n            var existingNote, baseNote, updatedNote;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                            2\n                        ];\n                        return [\n                            4,\n                            libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                        ];\n                    case 1:\n                        existingNote = _state.sent();\n                        baseNote = existingNote || note;\n                        updatedNote = (0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, baseNote, data);\n                        return [\n                            4,\n                            libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].setItem(note.id, updatedNote)\n                        ];\n                    case 2:\n                        _state.sent();\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function(data) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        note\n    ]);\n    var syncToServer = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var isNew, localNote, noteToSave, noteData, item, noteUrl, updatedNote, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        false\n                    ];\n                    isNew = (0,lodash__WEBPACK_IMPORTED_MODULE_6__.has)(router.query, \"new\");\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        11,\n                        ,\n                        12\n                    ]);\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                    ];\n                case 2:\n                    localNote = _state.sent();\n                    noteToSave = localNote || note;\n                    if (!isNew) return [\n                        3,\n                        7\n                    ];\n                    noteData = (0,_swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])((0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, noteToSave), {\n                        pid: router.query.pid || ROOT_ID\n                    });\n                    return [\n                        4,\n                        createNote(noteData)\n                    ];\n                case 3:\n                    item = _state.sent();\n                    if (!item) return [\n                        3,\n                        6\n                    ];\n                    noteUrl = \"/\".concat(item.id);\n                    if (!(router.asPath !== noteUrl)) return [\n                        3,\n                        5\n                    ];\n                    return [\n                        4,\n                        router.replace(noteUrl, undefined, {\n                            shallow: true\n                        })\n                    ];\n                case 4:\n                    _state.sent();\n                    _state.label = 5;\n                case 5:\n                    toast(\"Note saved to server\", \"success\");\n                    return [\n                        2,\n                        true\n                    ];\n                case 6:\n                    return [\n                        3,\n                        10\n                    ];\n                case 7:\n                    return [\n                        4,\n                        updateNote(noteToSave)\n                    ];\n                case 8:\n                    updatedNote = _state.sent();\n                    if (!updatedNote) return [\n                        3,\n                        10\n                    ];\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].setItem(updatedNote.id, updatedNote)\n                    ];\n                case 9:\n                    _state.sent();\n                    toast(\"Note updated on server\", \"success\");\n                    return [\n                        2,\n                        true\n                    ];\n                case 10:\n                    return [\n                        3,\n                        12\n                    ];\n                case 11:\n                    error = _state.sent();\n                    toast(\"Failed to save note to server\", \"error\");\n                    return [\n                        2,\n                        false\n                    ];\n                case 12:\n                    return [\n                        2,\n                        false\n                    ];\n            }\n        });\n    }), [\n        note,\n        router,\n        createNote,\n        updateNote,\n        toast\n    ]);\n    var onCreateLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(title) {\n            var result;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!createNoteWithTitle) return [\n                            2,\n                            \"\"\n                        ];\n                        return [\n                            4,\n                            createNoteWithTitle(title)\n                        ];\n                    case 1:\n                        result = _state.sent();\n                        if (result === null || result === void 0 ? void 0 : result.id) {\n                            return [\n                                2,\n                                \"/\".concat(result.id)\n                            ];\n                        }\n                        return [\n                            2,\n                            \"\"\n                        ];\n                }\n            });\n        });\n        return function(title) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        createNoteWithTitle\n    ]);\n    var onSearchLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(term) {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    []\n                ];\n            });\n        });\n        return function(term) {\n            return _ref.apply(this, arguments);\n        };\n    }(), []);\n    var onClickLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(href, event) {\n        if ((0,libs_shared_note__WEBPACK_IMPORTED_MODULE_3__.isNoteLink)(href)) {\n            event.preventDefault();\n            router.push(href);\n        } else {\n            window.open(href, \"_blank\", \"noopener,noreferrer\");\n        }\n    }, [\n        router\n    ]);\n    var onUploadImage = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(_file, _id) {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                // Image upload is disabled in PostgreSQL version\n                toast(\"Image upload is not supported in this version\", \"error\");\n                throw new Error(\"Image upload is not supported\");\n            });\n        });\n        return function(_file, _id) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        toast\n    ]);\n    var onHoverLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(event) {\n        return true;\n    }, []);\n    var ref2 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(), backlinks = ref2[0], setBackLinks = ref2[1];\n    var getBackLinks = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var linkNotes;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    console.log(note === null || note === void 0 ? void 0 : note.id);\n                    linkNotes = [];\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        linkNotes\n                    ];\n                    setBackLinks([]);\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].iterate(function(value) {\n                            var ref;\n                            if ((ref = value.linkIds) === null || ref === void 0 ? void 0 : ref.includes((note === null || note === void 0 ? void 0 : note.id) || \"\")) {\n                                linkNotes.push(value);\n                            }\n                        })\n                    ];\n                case 1:\n                    _state.sent();\n                    setBackLinks(linkNotes);\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        note === null || note === void 0 ? void 0 : note.id\n    ]);\n    // 🔧 快照初始化逻辑 - 打开笔记时设置JSON快照\n    var initializeSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var cachedNote, snapshotJsonContent, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) {\n                        // 新建笔记：快照为空值\n                        setNoteSnapshot(null);\n                        setCurrentEditorContent(\"\");\n                        console.log(\"\\uD83D\\uDD27 新建笔记：JSON快照设置为空值\");\n                        return [\n                            2\n                        ];\n                    }\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        3,\n                        ,\n                        4\n                    ]);\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                    ];\n                case 2:\n                    cachedNote = _state.sent();\n                    snapshotJsonContent = (cachedNote === null || cachedNote === void 0 ? void 0 : cachedNote.content) || \"\";\n                    setNoteSnapshot(snapshotJsonContent);\n                    setCurrentEditorContent(snapshotJsonContent);\n                    console.log(\"\\uD83D\\uDD27 已存在笔记：JSON快照设置为缓存内容\", {\n                        noteId: note.id,\n                        hasSnapshot: !!snapshotJsonContent,\n                        isJsonFormat: snapshotJsonContent.startsWith(\"{\")\n                    });\n                    return [\n                        3,\n                        4\n                    ];\n                case 3:\n                    error = _state.sent();\n                    console.error(\"\\uD83D\\uDD27 JSON快照初始化失败:\", error);\n                    // 失败时设置为空快照\n                    setNoteSnapshot(null);\n                    setCurrentEditorContent(\"\");\n                    return [\n                        3,\n                        4\n                    ];\n                case 4:\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        note === null || note === void 0 ? void 0 : note.id\n    ]);\n    // 当笔记ID变化时初始化快照\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n        initializeSnapshot();\n    }, [\n        initializeSnapshot\n    ]);\n    // 🔧 修复：恢复编辑器基本功能 - 更新JSON内容状态\n    var originalOnEditorChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(value) {\n        var jsonContent = value(); // 这里是JSON格式的内容\n        // 更新当前编辑器JSON内容状态\n        setCurrentEditorContent(jsonContent);\n        console.log(\"\\uD83D\\uDD27 编辑器JSON内容更新:\", {\n            contentLength: jsonContent.length\n        });\n    // 保存逻辑现在完全由SaveButton的快照对比机制处理\n    }, []);\n    // 🔧 临时移除智能包装器，直接使用原始onChange测试输入问题\n    var onEditorChange = originalOnEditorChange;\n    // Function to handle title changes specifically\n    var onTitleChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(title) {\n        var ref;\n        (ref = saveToIndexedDB({\n            title: title,\n            updated_at: new Date().toISOString()\n        })) === null || ref === void 0 ? void 0 : ref.catch(function(v) {\n            return console.error(\"Error whilst saving title to IndexedDB: %O\", v);\n        });\n    }, [\n        saveToIndexedDB\n    ]);\n    // 🔧 修复：JSON快照对比功能 - 供SaveButton使用\n    var compareWithSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        // 如果是新建笔记（快照为null），任何JSON内容都算作变化\n        if (noteSnapshot === null) {\n            return currentEditorContent.trim() !== \"\";\n        }\n        // 已存在笔记：比较当前JSON内容与JSON快照\n        var hasChanges = currentEditorContent !== noteSnapshot;\n        console.log(\"\\uD83D\\uDD27 JSON快照对比:\", {\n            hasChanges: hasChanges,\n            currentLength: currentEditorContent.length,\n            snapshotLength: noteSnapshot.length,\n            bothAreJson: currentEditorContent.startsWith(\"{\") && noteSnapshot.startsWith(\"{\")\n        });\n        return hasChanges;\n    }, [\n        noteSnapshot,\n        currentEditorContent\n    ]);\n    // 🔧 新增：获取当前编辑器状态 - 供SaveButton使用\n    var getEditorState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        return {\n            hasChanges: compareWithSnapshot(),\n            currentContent: currentEditorContent,\n            snapshot: noteSnapshot,\n            isNewNote: noteSnapshot === null\n        };\n    }, [\n        compareWithSnapshot,\n        currentEditorContent,\n        noteSnapshot\n    ]);\n    // 🔧 修复：保存当前JSON内容到IndexedDB - 供SaveButton调用\n    var saveCurrentContent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var title, titleInput, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        false\n                    ];\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        3,\n                        ,\n                        4\n                    ]);\n                    if (note === null || note === void 0 ? void 0 : note.isDailyNote) {\n                        title = note.title;\n                    } else {\n                        titleInput = document.querySelector(\"h1 textarea\");\n                        if (titleInput && titleInput.value) {\n                            title = titleInput.value.trim();\n                        } else {\n                            // 对于JSON格式，使用现有标题或默认标题\n                            title = (note === null || note === void 0 ? void 0 : note.title) || \"Untitled\";\n                        }\n                    }\n                    // 保存JSON内容到IndexedDB\n                    return [\n                        4,\n                        saveToIndexedDB({\n                            content: currentEditorContent,\n                            title: title,\n                            updated_at: new Date().toISOString()\n                        })\n                    ];\n                case 2:\n                    _state.sent();\n                    console.log(\"\\uD83D\\uDD27 JSON内容已保存到IndexedDB\", {\n                        noteId: note.id,\n                        title: title,\n                        contentLength: currentEditorContent.length,\n                        isJsonFormat: currentEditorContent.startsWith(\"{\")\n                    });\n                    return [\n                        2,\n                        true\n                    ];\n                case 3:\n                    error = _state.sent();\n                    console.error(\"\\uD83D\\uDD27 保存JSON到IndexedDB失败:\", error);\n                    return [\n                        2,\n                        false\n                    ];\n                case 4:\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        note,\n        currentEditorContent,\n        saveToIndexedDB\n    ]);\n    return {\n        onCreateLink: onCreateLink,\n        onSearchLink: onSearchLink,\n        onClickLink: onClickLink,\n        onUploadImage: onUploadImage,\n        onHoverLink: onHoverLink,\n        getBackLinks: getBackLinks,\n        onEditorChange: onEditorChange,\n        onTitleChange: onTitleChange,\n        saveToIndexedDB: saveToIndexedDB,\n        syncToServer: syncToServer,\n        backlinks: backlinks,\n        editorEl: editorEl,\n        note: note,\n        // 🔧 新增：快照对比相关功能\n        getEditorState: getEditorState,\n        saveCurrentContent: saveCurrentContent,\n        compareWithSnapshot: compareWithSnapshot\n    };\n};\nvar LexicalEditorState = (0,unstated_next__WEBPACK_IMPORTED_MODULE_11__.createContainer)(useLexicalEditor);\n/* harmony default export */ __webpack_exports__[\"default\"] = (LexicalEditorState);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./libs/web/state/lexical-editor.ts\n"));

/***/ })

});