[{"traceId": "34dd0ab9d05d812e", "name": "hot-reloader", "id": 1, "timestamp": 27029252187, "duration": 101, "tags": {"version": "12.3.7"}, "startTime": 1751709303400}, {"traceId": "34dd0ab9d05d812e", "parentId": 1, "name": "start", "id": 2, "timestamp": 27029253995, "duration": 5, "tags": {}, "startTime": 1751709303402}, {"traceId": "34dd0ab9d05d812e", "parentId": 2, "name": "clean", "id": 3, "timestamp": 27029254109, "duration": 33429, "tags": {}, "startTime": 1751709303402}, {"traceId": "34dd0ab9d05d812e", "parentId": 4, "name": "get-page-paths", "id": 5, "timestamp": 27029292029, "duration": 5636, "tags": {}, "startTime": 1751709303440}, {"traceId": "34dd0ab9d05d812e", "parentId": 4, "name": "create-pages-mapping", "id": 6, "timestamp": 27029297750, "duration": 1409, "tags": {}, "startTime": 1751709303446}, {"traceId": "34dd0ab9d05d812e", "parentId": 4, "name": "create-entrypoints", "id": 7, "timestamp": 27029299233, "duration": 6755, "tags": {}, "startTime": 1751709303447}, {"traceId": "34dd0ab9d05d812e", "parentId": 4, "name": "generate-webpack-config", "id": 8, "timestamp": 27029306095, "duration": 509795, "tags": {}, "startTime": 1751709303454}, {"traceId": "34dd0ab9d05d812e", "parentId": 2, "name": "get-webpack-config", "id": 4, "timestamp": 27029291745, "duration": 524229, "tags": {}, "startTime": 1751709303440}, {"traceId": "34dd0ab9d05d812e", "parentId": 9, "name": "add-entry", "id": 11, "timestamp": 27030490566, "duration": 996692, "tags": {"request": "C:\\Users\\<USER>\\Documents\\motea-docker-main\\node_modules\\next\\dist\\compiled\\@next\\react-refresh-utils\\dist\\runtime.js"}, "startTime": 1751709304639}, {"traceId": "34dd0ab9d05d812e", "parentId": 9, "name": "add-entry", "id": 12, "timestamp": 27030492829, "duration": 1266917, "tags": {"request": "./node_modules/next/dist/client/dev/amp-dev"}, "startTime": 1751709304641}, {"traceId": "34dd0ab9d05d812e", "parentId": 9, "name": "add-entry", "id": 16, "timestamp": 27030493895, "duration": 1269479, "tags": {"request": "next-client-pages-loader?absolutePagePath=private-next-pages%2F_error&page=%2F_error!"}, "startTime": 1751709304642}, {"traceId": "34dd0ab9d05d812e", "parentId": 9, "name": "add-entry", "id": 15, "timestamp": 27030493656, "duration": 1269782, "tags": {"request": "C:\\Users\\<USER>\\Documents\\motea-docker-main\\node_modules\\next\\dist\\client\\router.js"}, "startTime": 1751709304642}, {"traceId": "34dd0ab9d05d812e", "parentId": 17, "name": "read-resource", "id": 18, "timestamp": 27031884866, "duration": 96755, "tags": {}, "startTime": 1751709306033}, {"traceId": "34dd0ab9d05d812e", "parentId": 19, "name": "postcss-process", "id": 20, "timestamp": 27034076784, "duration": 3302671, "tags": {}, "startTime": 1751709308225}, {"traceId": "34dd0ab9d05d812e", "parentId": 17, "name": "postcss-loader", "id": 19, "timestamp": 27031982367, "duration": 5407769, "tags": {}, "startTime": 1751709306130}, {"traceId": "34dd0ab9d05d812e", "parentId": 17, "name": "css-loader", "id": 21, "timestamp": 27037390959, "duration": 418844, "tags": {"astUsed": "true"}, "startTime": 1751709311539}, {"traceId": "34dd0ab9d05d812e", "parentId": 9, "name": "build-module-css", "id": 17, "timestamp": 27031870095, "duration": 5988241, "tags": {"name": "C:\\Users\\<USER>\\Documents\\motea-docker-main\\node_modules\\next\\dist\\build\\webpack\\loaders\\css-loader\\src\\index.js??ruleSet[1].rules[2].oneOf[7].use[1]!C:\\Users\\<USER>\\Documents\\motea-docker-main\\node_modules\\next\\dist\\build\\webpack\\loaders\\postcss-loader\\src\\index.js??ruleSet[1].rules[2].oneOf[7].use[2]!C:\\Users\\<USER>\\Documents\\motea-docker-main\\node_modules\\tailwindcss\\tailwind.css"}, "startTime": 1751709306018}, {"traceId": "34dd0ab9d05d812e", "parentId": 9, "name": "add-entry", "id": 13, "timestamp": 27030493070, "duration": 7527590, "tags": {"request": "./node_modules/next/dist/client/next-dev.js"}, "startTime": 1751709304641}, {"traceId": "34dd0ab9d05d812e", "parentId": 9, "name": "add-entry", "id": 14, "timestamp": 27030493234, "duration": 7690434, "tags": {"request": "next-client-pages-loader?absolutePagePath=private-next-pages%2F_app&page=%2F_app!"}, "startTime": 1751709304641}, {"traceId": "34dd0ab9d05d812e", "parentId": 1, "name": "make", "id": 10, "timestamp": 27030485183, "duration": 7699066, "tags": {}, "startTime": 1751709304633}, {"traceId": "34dd0ab9d05d812e", "parentId": 9, "name": "webpack-compilation-chunk-graph", "id": 23, "timestamp": 27038248399, "duration": 66696, "tags": {}, "startTime": 1751709312396}, {"traceId": "34dd0ab9d05d812e", "parentId": 9, "name": "webpack-compilation-optimize-modules", "id": 25, "timestamp": 27038315576, "duration": 184, "tags": {}, "startTime": 1751709312464}, {"traceId": "34dd0ab9d05d812e", "parentId": 9, "name": "webpack-compilation-optimize-chunks", "id": 26, "timestamp": 27038316165, "duration": 2078, "tags": {}, "startTime": 1751709312464}, {"traceId": "34dd0ab9d05d812e", "parentId": 9, "name": "webpack-compilation-optimize-tree", "id": 27, "timestamp": 27038318563, "duration": 510, "tags": {}, "startTime": 1751709312467}, {"traceId": "34dd0ab9d05d812e", "parentId": 9, "name": "webpack-compilation-optimize", "id": 24, "timestamp": 27038315351, "duration": 4641, "tags": {}, "startTime": 1751709312463}, {"traceId": "34dd0ab9d05d812e", "parentId": 9, "name": "webpack-compilation-hash", "id": 28, "timestamp": 27038495231, "duration": 34739, "tags": {}, "startTime": 1751709312643}, {"traceId": "34dd0ab9d05d812e", "parentId": 9, "name": "NextJsBuildManifest-generateClientManifest", "id": 30, "timestamp": 27038782914, "duration": 2165, "tags": {}, "startTime": 1751709312931}, {"traceId": "34dd0ab9d05d812e", "parentId": 9, "name": "NextJsBuildManifest-createassets", "id": 29, "timestamp": 27038780969, "duration": 4140, "tags": {}, "startTime": 1751709312929}, {"traceId": "34dd0ab9d05d812e", "parentId": 9, "name": "webpack-compilation-seal", "id": 22, "timestamp": 27038239257, "duration": 553034, "tags": {}, "startTime": 1751709312387}, {"traceId": "34dd0ab9d05d812e", "parentId": 1, "name": "webpack-compilation", "id": 9, "timestamp": 27030455794, "duration": 8337363, "tags": {"name": "client"}, "startTime": 1751709304604}, {"traceId": "34dd0ab9d05d812e", "parentId": 1, "name": "emit", "id": 31, "timestamp": 27038794254, "duration": 331532, "tags": {}, "startTime": 1751709312942}, {"traceId": "34dd0ab9d05d812e", "parentId": 32, "name": "add-entry", "id": 36, "timestamp": 27039504220, "duration": 2060401, "tags": {"request": "private-next-pages/_document"}, "startTime": 1751709313652}, {"traceId": "34dd0ab9d05d812e", "parentId": 32, "name": "add-entry", "id": 35, "timestamp": 27039504097, "duration": 2060638, "tags": {"request": "private-next-pages/_error"}, "startTime": 1751709313652}, {"traceId": "34dd0ab9d05d812e", "parentId": 32, "name": "add-entry", "id": 34, "timestamp": 27039503683, "duration": 2163269, "tags": {"request": "private-next-pages/_app"}, "startTime": 1751709313652}, {"traceId": "34dd0ab9d05d812e", "parentId": 1, "name": "make", "id": 33, "timestamp": 27039161102, "duration": 2506621, "tags": {}, "startTime": 1751709313309}, {"traceId": "34dd0ab9d05d812e", "parentId": 32, "name": "webpack-compilation-chunk-graph", "id": 38, "timestamp": 27041671278, "duration": 4154, "tags": {}, "startTime": 1751709315819}, {"traceId": "34dd0ab9d05d812e", "parentId": 32, "name": "webpack-compilation-optimize-modules", "id": 40, "timestamp": 27041675691, "duration": 74, "tags": {}, "startTime": 1751709315824}, {"traceId": "34dd0ab9d05d812e", "parentId": 32, "name": "webpack-compilation-optimize-chunks", "id": 41, "timestamp": 27041676166, "duration": 943, "tags": {}, "startTime": 1751709315824}, {"traceId": "34dd0ab9d05d812e", "parentId": 32, "name": "webpack-compilation-optimize-tree", "id": 42, "timestamp": 27041677371, "duration": 152, "tags": {}, "startTime": 1751709315825}, {"traceId": "34dd0ab9d05d812e", "parentId": 32, "name": "webpack-compilation-optimize", "id": 39, "timestamp": 27041675555, "duration": 2650, "tags": {}, "startTime": 1751709315824}, {"traceId": "34dd0ab9d05d812e", "parentId": 32, "name": "webpack-compilation-hash", "id": 43, "timestamp": 27041699275, "duration": 9958, "tags": {}, "startTime": 1751709315847}, {"traceId": "34dd0ab9d05d812e", "parentId": 32, "name": "webpack-compilation-seal", "id": 37, "timestamp": 27041670731, "duration": 47575, "tags": {}, "startTime": 1751709315819}, {"traceId": "34dd0ab9d05d812e", "parentId": 1, "name": "webpack-compilation", "id": 32, "timestamp": 27039156243, "duration": 2562491, "tags": {"name": "server"}, "startTime": 1751709313304}, {"traceId": "34dd0ab9d05d812e", "parentId": 1, "name": "emit", "id": 44, "timestamp": 27041719204, "duration": 24984, "tags": {}, "startTime": 1751709315867}, {"traceId": "34dd0ab9d05d812e", "parentId": 1, "name": "make", "id": 46, "timestamp": 27041764631, "duration": 6668, "tags": {}, "startTime": 1751709315913}, {"traceId": "34dd0ab9d05d812e", "parentId": 45, "name": "webpack-compilation-chunk-graph", "id": 48, "timestamp": 27041776119, "duration": 204, "tags": {}, "startTime": 1751709315924}, {"traceId": "34dd0ab9d05d812e", "parentId": 45, "name": "webpack-compilation-optimize-modules", "id": 50, "timestamp": 27041776466, "duration": 53, "tags": {}, "startTime": 1751709315924}, {"traceId": "34dd0ab9d05d812e", "parentId": 45, "name": "webpack-compilation-optimize-chunks", "id": 51, "timestamp": 27041776607, "duration": 90, "tags": {}, "startTime": 1751709315925}, {"traceId": "34dd0ab9d05d812e", "parentId": 45, "name": "webpack-compilation-optimize-tree", "id": 52, "timestamp": 27041776793, "duration": 53, "tags": {}, "startTime": 1751709315925}, {"traceId": "34dd0ab9d05d812e", "parentId": 45, "name": "webpack-compilation-optimize", "id": 49, "timestamp": 27041776406, "duration": 635, "tags": {}, "startTime": 1751709315924}, {"traceId": "34dd0ab9d05d812e", "parentId": 45, "name": "webpack-compilation-hash", "id": 53, "timestamp": 27041778091, "duration": 347, "tags": {}, "startTime": 1751709315926}, {"traceId": "34dd0ab9d05d812e", "parentId": 45, "name": "webpack-compilation-seal", "id": 47, "timestamp": 27041775794, "duration": 4903, "tags": {}, "startTime": 1751709315924}, {"traceId": "34dd0ab9d05d812e", "parentId": 1, "name": "webpack-compilation", "id": 45, "timestamp": 27041759176, "duration": 21717, "tags": {"name": "edge-server"}, "startTime": 1751709315907}, {"traceId": "34dd0ab9d05d812e", "parentId": 1, "name": "emit", "id": 54, "timestamp": 27041781160, "duration": 162060, "tags": {}, "startTime": 1751709315929}]