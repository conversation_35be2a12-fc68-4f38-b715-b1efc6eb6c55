{"version": 3, "names": ["cloneNode", "interpreterDirective", "errorVisitor", "enter", "path", "state", "loc", "node", "stop", "File", "constructor", "options", "code", "ast", "inputMap", "_map", "Map", "opts", "declarations", "scope", "metadata", "hub", "file", "getCode", "getScope", "addHelper", "bind", "buildError", "buildCodeFrameError", "NodePath", "get", "parentPath", "parent", "container", "key", "setContext", "shebang", "interpreter", "value", "replaceWith", "remove", "set", "val", "Error", "has", "getModuleName", "addImport", "availableHelper", "name", "versionRange", "minVersion", "helpers", "err", "semver", "valid", "intersects", "declar", "generator", "res", "ensure", "uid", "generateUidIdentifier", "dependencies", "dep", "getDependencies", "nodes", "globals", "Object", "keys", "getAllBindings", "for<PERSON>ach", "hasBinding", "rename", "_compact", "unshiftContainer", "indexOf", "isVariableDeclaration", "registerDeclaration", "addTemplateObject", "msg", "_Error", "SyntaxError", "_loc", "traverse", "txt", "highlightCode", "codeFrameColumns", "start", "line", "column", "end", "undefined"], "sources": ["../../../src/transformation/file/file.ts"], "sourcesContent": ["import * as helpers from \"@babel/helpers\";\nimport { NodePath } from \"@babel/traverse\";\nimport type { HubInterface, Visitor, Scope } from \"@babel/traverse\";\nimport { codeFrameColumns } from \"@babel/code-frame\";\nimport traverse from \"@babel/traverse\";\nimport { cloneNode, interpreterDirective } from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport { getModuleName } from \"@babel/helper-module-transforms\";\nimport semver from \"semver\";\n\nimport type { NormalizedFile } from \"../normalize-file\";\n\nconst errorVisitor: Visitor<{ loc: NodeLocation[\"loc\"] | null }> = {\n  enter(path, state) {\n    const loc = path.node.loc;\n    if (loc) {\n      state.loc = loc;\n      path.stop();\n    }\n  },\n};\n\nexport type NodeLocation = {\n  loc?: {\n    end?: {\n      line: number;\n      column: number;\n    };\n    start: {\n      line: number;\n      column: number;\n    };\n  };\n  _loc?: {\n    end?: {\n      line: number;\n      column: number;\n    };\n    start: {\n      line: number;\n      column: number;\n    };\n  };\n};\n\nexport default class File {\n  _map: Map<unknown, unknown> = new Map();\n  opts: { [key: string]: any };\n  declarations: { [key: string]: t.Identifier } = {};\n  path: NodePath<t.Program>;\n  ast: t.File;\n  scope: Scope;\n  metadata: { [key: string]: any } = {};\n  code: string = \"\";\n  inputMap: any;\n\n  hub: HubInterface & { file: File } = {\n    // keep it for the usage in babel-core, ex: path.hub.file.opts.filename\n    file: this,\n    getCode: () => this.code,\n    getScope: () => this.scope,\n    addHelper: this.addHelper.bind(this),\n    buildError: this.buildCodeFrameError.bind(this),\n  };\n\n  constructor(options: {}, { code, ast, inputMap }: NormalizedFile) {\n    this.opts = options;\n    this.code = code;\n    this.ast = ast;\n    this.inputMap = inputMap;\n\n    this.path = NodePath.get({\n      hub: this.hub,\n      parentPath: null,\n      parent: this.ast,\n      container: this.ast,\n      key: \"program\",\n    }).setContext() as NodePath<t.Program>;\n    this.scope = this.path.scope;\n  }\n\n  /**\n   * Provide backward-compatible access to the interpreter directive handling\n   * in Babel 6.x. If you are writing a plugin for Babel 7.x, it would be\n   * best to use 'program.interpreter' directly.\n   */\n  get shebang(): string {\n    const { interpreter } = this.path.node;\n    return interpreter ? interpreter.value : \"\";\n  }\n  set shebang(value: string) {\n    if (value) {\n      this.path.get(\"interpreter\").replaceWith(interpreterDirective(value));\n    } else {\n      this.path.get(\"interpreter\").remove();\n    }\n  }\n\n  set(key: unknown, val: unknown) {\n    if (key === \"helpersNamespace\") {\n      throw new Error(\n        \"Babel 7.0.0-beta.56 has dropped support for the 'helpersNamespace' utility.\" +\n          \"If you are using @babel/plugin-external-helpers you will need to use a newer \" +\n          \"version than the one you currently have installed. \" +\n          \"If you have your own implementation, you'll want to explore using 'helperGenerator' \" +\n          \"alongside 'file.availableHelper()'.\",\n      );\n    }\n\n    this._map.set(key, val);\n  }\n\n  get(key: unknown): any {\n    return this._map.get(key);\n  }\n\n  has(key: unknown): boolean {\n    return this._map.has(key);\n  }\n\n  getModuleName(): string | undefined | null {\n    return getModuleName(this.opts, this.opts);\n  }\n\n  addImport() {\n    throw new Error(\n      \"This API has been removed. If you're looking for this \" +\n        \"functionality in Babel 7, you should import the \" +\n        \"'@babel/helper-module-imports' module and use the functions exposed \" +\n        \" from that module, such as 'addNamed' or 'addDefault'.\",\n    );\n  }\n\n  /**\n   * Check if a given helper is available in @babel/core's helper list.\n   *\n   * This _also_ allows you to pass a Babel version specifically. If the\n   * helper exists, but was not available for the full given range, it will be\n   * considered unavailable.\n   */\n  availableHelper(name: string, versionRange?: string | null): boolean {\n    let minVersion;\n    try {\n      minVersion = helpers.minVersion(name);\n    } catch (err) {\n      if (err.code !== \"BABEL_HELPER_UNKNOWN\") throw err;\n\n      return false;\n    }\n\n    if (typeof versionRange !== \"string\") return true;\n\n    // semver.intersects() has some surprising behavior with comparing ranges\n    // with pre-release versions. We add '^' to ensure that we are always\n    // comparing ranges with ranges, which sidesteps this logic.\n    // For example:\n    //\n    //   semver.intersects(`<7.0.1`, \"7.0.0-beta.0\") // false - surprising\n    //   semver.intersects(`<7.0.1`, \"^7.0.0-beta.0\") // true - expected\n    //\n    // This is because the first falls back to\n    //\n    //   semver.satisfies(\"7.0.0-beta.0\", `<7.0.1`) // false - surprising\n    //\n    // and this fails because a prerelease version can only satisfy a range\n    // if it is a prerelease within the same major/minor/patch range.\n    //\n    // Note: If this is found to have issues, please also revisit the logic in\n    // transform-runtime's definitions.js file.\n    if (semver.valid(versionRange)) versionRange = `^${versionRange}`;\n\n    return (\n      !semver.intersects(`<${minVersion}`, versionRange) &&\n      !semver.intersects(`>=8.0.0`, versionRange)\n    );\n  }\n\n  addHelper(name: string): t.Identifier {\n    const declar = this.declarations[name];\n    if (declar) return cloneNode(declar);\n\n    const generator = this.get(\"helperGenerator\");\n    if (generator) {\n      const res = generator(name);\n      if (res) return res;\n    }\n\n    // make sure that the helper exists\n    helpers.ensure(name, File);\n\n    const uid = (this.declarations[name] =\n      this.scope.generateUidIdentifier(name));\n\n    const dependencies: { [key: string]: t.Identifier } = {};\n    for (const dep of helpers.getDependencies(name)) {\n      dependencies[dep] = this.addHelper(dep);\n    }\n\n    const { nodes, globals } = helpers.get(\n      name,\n      dep => dependencies[dep],\n      uid,\n      Object.keys(this.scope.getAllBindings()),\n    );\n\n    globals.forEach(name => {\n      if (this.path.scope.hasBinding(name, true /* noGlobals */)) {\n        this.path.scope.rename(name);\n      }\n    });\n\n    nodes.forEach(node => {\n      // @ts-expect-error Fixeme: document _compact node property\n      node._compact = true;\n    });\n\n    this.path.unshiftContainer(\"body\", nodes);\n    // TODO: NodePath#unshiftContainer should automatically register new\n    // bindings.\n    this.path.get(\"body\").forEach(path => {\n      if (nodes.indexOf(path.node) === -1) return;\n      if (path.isVariableDeclaration()) this.scope.registerDeclaration(path);\n    });\n\n    return uid;\n  }\n\n  addTemplateObject() {\n    throw new Error(\n      \"This function has been moved into the template literal transform itself.\",\n    );\n  }\n\n  buildCodeFrameError(\n    node: NodeLocation | undefined | null,\n    msg: string,\n    _Error: typeof Error = SyntaxError,\n  ): Error {\n    let loc = node && (node.loc || node._loc);\n\n    if (!loc && node) {\n      const state: { loc?: NodeLocation[\"loc\"] | null } = {\n        loc: null,\n      };\n      traverse(node as t.Node, errorVisitor, this.scope, state);\n      loc = state.loc;\n\n      let txt =\n        \"This is an error on an internal node. Probably an internal error.\";\n      if (loc) txt += \" Location has been estimated.\";\n\n      msg += ` (${txt})`;\n    }\n\n    if (loc) {\n      const { highlightCode = true } = this.opts;\n\n      msg +=\n        \"\\n\" +\n        codeFrameColumns(\n          this.code,\n          {\n            start: {\n              line: loc.start.line,\n              column: loc.start.column + 1,\n            },\n            end:\n              loc.end && loc.start.line === loc.end.line\n                ? {\n                    line: loc.end.line,\n                    column: loc.end.column + 1,\n                  }\n                : undefined,\n          },\n          { highlightCode },\n        );\n    }\n\n    return new _Error(msg);\n  }\n}\n"], "mappings": ";;;;;;;AAAA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AACA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AAEA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AAEA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AAEA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AACA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;;EAHSA,S;EAAWC;;;AAOpB,MAAMC,YAA0D,GAAG;EACjEC,KAAK,CAACC,IAAD,EAAOC,KAAP,EAAc;IACjB,MAAMC,GAAG,GAAGF,IAAI,CAACG,IAAL,CAAUD,GAAtB;;IACA,IAAIA,GAAJ,EAAS;MACPD,KAAK,CAACC,GAAN,GAAYA,GAAZ;MACAF,IAAI,CAACI,IAAL;IACD;EACF;;AAPgE,CAAnE;;AAiCe,MAAMC,IAAN,CAAW;EAoBxBC,WAAW,CAACC,OAAD,EAAc;IAAEC,IAAF;IAAQC,GAAR;IAAaC;EAAb,CAAd,EAAuD;IAAA,KAnBlEC,IAmBkE,GAnBpC,IAAIC,GAAJ,EAmBoC;IAAA,KAlBlEC,IAkBkE;IAAA,KAjBlEC,YAiBkE,GAjBlB,EAiBkB;IAAA,KAhBlEd,IAgBkE;IAAA,KAflES,GAekE;IAAA,KAdlEM,KAckE;IAAA,KAblEC,QAakE,GAb/B,EAa+B;IAAA,KAZlER,IAYkE,GAZnD,EAYmD;IAAA,KAXlEE,QAWkE;IAAA,KATlEO,GASkE,GAT7B;MAEnCC,IAAI,EAAE,IAF6B;MAGnCC,OAAO,EAAE,MAAM,KAAKX,IAHe;MAInCY,QAAQ,EAAE,MAAM,KAAKL,KAJc;MAKnCM,SAAS,EAAE,KAAKA,SAAL,CAAeC,IAAf,CAAoB,IAApB,CALwB;MAMnCC,UAAU,EAAE,KAAKC,mBAAL,CAAyBF,IAAzB,CAA8B,IAA9B;IANuB,CAS6B;IAChE,KAAKT,IAAL,GAAYN,OAAZ;IACA,KAAKC,IAAL,GAAYA,IAAZ;IACA,KAAKC,GAAL,GAAWA,GAAX;IACA,KAAKC,QAAL,GAAgBA,QAAhB;IAEA,KAAKV,IAAL,GAAYyB,oBAAA,CAASC,GAAT,CAAa;MACvBT,GAAG,EAAE,KAAKA,GADa;MAEvBU,UAAU,EAAE,IAFW;MAGvBC,MAAM,EAAE,KAAKnB,GAHU;MAIvBoB,SAAS,EAAE,KAAKpB,GAJO;MAKvBqB,GAAG,EAAE;IALkB,CAAb,EAMTC,UANS,EAAZ;IAOA,KAAKhB,KAAL,GAAa,KAAKf,IAAL,CAAUe,KAAvB;EACD;;EAOU,IAAPiB,OAAO,GAAW;IACpB,MAAM;MAAEC;IAAF,IAAkB,KAAKjC,IAAL,CAAUG,IAAlC;IACA,OAAO8B,WAAW,GAAGA,WAAW,CAACC,KAAf,GAAuB,EAAzC;EACD;;EACU,IAAPF,OAAO,CAACE,KAAD,EAAgB;IACzB,IAAIA,KAAJ,EAAW;MACT,KAAKlC,IAAL,CAAU0B,GAAV,CAAc,aAAd,EAA6BS,WAA7B,CAAyCtC,oBAAoB,CAACqC,KAAD,CAA7D;IACD,CAFD,MAEO;MACL,KAAKlC,IAAL,CAAU0B,GAAV,CAAc,aAAd,EAA6BU,MAA7B;IACD;EACF;;EAEDC,GAAG,CAACP,GAAD,EAAeQ,GAAf,EAA6B;IAC9B,IAAIR,GAAG,KAAK,kBAAZ,EAAgC;MAC9B,MAAM,IAAIS,KAAJ,CACJ,gFACE,+EADF,GAEE,qDAFF,GAGE,sFAHF,GAIE,qCALE,CAAN;IAOD;;IAED,KAAK5B,IAAL,CAAU0B,GAAV,CAAcP,GAAd,EAAmBQ,GAAnB;EACD;;EAEDZ,GAAG,CAACI,GAAD,EAAoB;IACrB,OAAO,KAAKnB,IAAL,CAAUe,GAAV,CAAcI,GAAd,CAAP;EACD;;EAEDU,GAAG,CAACV,GAAD,EAAwB;IACzB,OAAO,KAAKnB,IAAL,CAAU6B,GAAV,CAAcV,GAAd,CAAP;EACD;;EAEDW,aAAa,GAA8B;IACzC,OAAO,IAAAA,uCAAA,EAAc,KAAK5B,IAAnB,EAAyB,KAAKA,IAA9B,CAAP;EACD;;EAED6B,SAAS,GAAG;IACV,MAAM,IAAIH,KAAJ,CACJ,2DACE,kDADF,GAEE,sEAFF,GAGE,wDAJE,CAAN;EAMD;;EASDI,eAAe,CAACC,IAAD,EAAeC,YAAf,EAAsD;IACnE,IAAIC,UAAJ;;IACA,IAAI;MACFA,UAAU,GAAGC,OAAO,GAACD,UAAR,CAAmBF,IAAnB,CAAb;IACD,CAFD,CAEE,OAAOI,GAAP,EAAY;MACZ,IAAIA,GAAG,CAACxC,IAAJ,KAAa,sBAAjB,EAAyC,MAAMwC,GAAN;MAEzC,OAAO,KAAP;IACD;;IAED,IAAI,OAAOH,YAAP,KAAwB,QAA5B,EAAsC,OAAO,IAAP;IAmBtC,IAAII,SAAA,CAAOC,KAAP,CAAaL,YAAb,CAAJ,EAAgCA,YAAY,GAAI,IAAGA,YAAa,EAAhC;IAEhC,OACE,CAACI,SAAA,CAAOE,UAAP,CAAmB,IAAGL,UAAW,EAAjC,EAAoCD,YAApC,CAAD,IACA,CAACI,SAAA,CAAOE,UAAP,CAAmB,SAAnB,EAA6BN,YAA7B,CAFH;EAID;;EAEDxB,SAAS,CAACuB,IAAD,EAA6B;IACpC,MAAMQ,MAAM,GAAG,KAAKtC,YAAL,CAAkB8B,IAAlB,CAAf;IACA,IAAIQ,MAAJ,EAAY,OAAOxD,SAAS,CAACwD,MAAD,CAAhB;IAEZ,MAAMC,SAAS,GAAG,KAAK3B,GAAL,CAAS,iBAAT,CAAlB;;IACA,IAAI2B,SAAJ,EAAe;MACb,MAAMC,GAAG,GAAGD,SAAS,CAACT,IAAD,CAArB;MACA,IAAIU,GAAJ,EAAS,OAAOA,GAAP;IACV;;IAGDP,OAAO,GAACQ,MAAR,CAAeX,IAAf,EAAqBvC,IAArB;IAEA,MAAMmD,GAAG,GAAI,KAAK1C,YAAL,CAAkB8B,IAAlB,IACX,KAAK7B,KAAL,CAAW0C,qBAAX,CAAiCb,IAAjC,CADF;IAGA,MAAMc,YAA6C,GAAG,EAAtD;;IACA,KAAK,MAAMC,GAAX,IAAkBZ,OAAO,GAACa,eAAR,CAAwBhB,IAAxB,CAAlB,EAAiD;MAC/Cc,YAAY,CAACC,GAAD,CAAZ,GAAoB,KAAKtC,SAAL,CAAesC,GAAf,CAApB;IACD;;IAED,MAAM;MAAEE,KAAF;MAASC;IAAT,IAAqBf,OAAO,GAACrB,GAAR,CACzBkB,IADyB,EAEzBe,GAAG,IAAID,YAAY,CAACC,GAAD,CAFM,EAGzBH,GAHyB,EAIzBO,MAAM,CAACC,IAAP,CAAY,KAAKjD,KAAL,CAAWkD,cAAX,EAAZ,CAJyB,CAA3B;IAOAH,OAAO,CAACI,OAAR,CAAgBtB,IAAI,IAAI;MACtB,IAAI,KAAK5C,IAAL,CAAUe,KAAV,CAAgBoD,UAAhB,CAA2BvB,IAA3B,EAAiC,IAAjC,CAAJ,EAA4D;QAC1D,KAAK5C,IAAL,CAAUe,KAAV,CAAgBqD,MAAhB,CAAuBxB,IAAvB;MACD;IACF,CAJD;IAMAiB,KAAK,CAACK,OAAN,CAAc/D,IAAI,IAAI;MAEpBA,IAAI,CAACkE,QAAL,GAAgB,IAAhB;IACD,CAHD;IAKA,KAAKrE,IAAL,CAAUsE,gBAAV,CAA2B,MAA3B,EAAmCT,KAAnC;IAGA,KAAK7D,IAAL,CAAU0B,GAAV,CAAc,MAAd,EAAsBwC,OAAtB,CAA8BlE,IAAI,IAAI;MACpC,IAAI6D,KAAK,CAACU,OAAN,CAAcvE,IAAI,CAACG,IAAnB,MAA6B,CAAC,CAAlC,EAAqC;MACrC,IAAIH,IAAI,CAACwE,qBAAL,EAAJ,EAAkC,KAAKzD,KAAL,CAAW0D,mBAAX,CAA+BzE,IAA/B;IACnC,CAHD;IAKA,OAAOwD,GAAP;EACD;;EAEDkB,iBAAiB,GAAG;IAClB,MAAM,IAAInC,KAAJ,CACJ,0EADI,CAAN;EAGD;;EAEDf,mBAAmB,CACjBrB,IADiB,EAEjBwE,GAFiB,EAGjBC,MAAoB,GAAGC,WAHN,EAIV;IACP,IAAI3E,GAAG,GAAGC,IAAI,KAAKA,IAAI,CAACD,GAAL,IAAYC,IAAI,CAAC2E,IAAtB,CAAd;;IAEA,IAAI,CAAC5E,GAAD,IAAQC,IAAZ,EAAkB;MAChB,MAAMF,KAA2C,GAAG;QAClDC,GAAG,EAAE;MAD6C,CAApD;MAGA,IAAA6E,mBAAA,EAAS5E,IAAT,EAAyBL,YAAzB,EAAuC,KAAKiB,KAA5C,EAAmDd,KAAnD;MACAC,GAAG,GAAGD,KAAK,CAACC,GAAZ;MAEA,IAAI8E,GAAG,GACL,mEADF;MAEA,IAAI9E,GAAJ,EAAS8E,GAAG,IAAI,+BAAP;MAETL,GAAG,IAAK,KAAIK,GAAI,GAAhB;IACD;;IAED,IAAI9E,GAAJ,EAAS;MACP,MAAM;QAAE+E,aAAa,GAAG;MAAlB,IAA2B,KAAKpE,IAAtC;MAEA8D,GAAG,IACD,OACA,IAAAO,6BAAA,EACE,KAAK1E,IADP,EAEE;QACE2E,KAAK,EAAE;UACLC,IAAI,EAAElF,GAAG,CAACiF,KAAJ,CAAUC,IADX;UAELC,MAAM,EAAEnF,GAAG,CAACiF,KAAJ,CAAUE,MAAV,GAAmB;QAFtB,CADT;QAKEC,GAAG,EACDpF,GAAG,CAACoF,GAAJ,IAAWpF,GAAG,CAACiF,KAAJ,CAAUC,IAAV,KAAmBlF,GAAG,CAACoF,GAAJ,CAAQF,IAAtC,GACI;UACEA,IAAI,EAAElF,GAAG,CAACoF,GAAJ,CAAQF,IADhB;UAEEC,MAAM,EAAEnF,GAAG,CAACoF,GAAJ,CAAQD,MAAR,GAAiB;QAF3B,CADJ,GAKIE;MAXR,CAFF,EAeE;QAAEN;MAAF,CAfF,CAFF;IAmBD;;IAED,OAAO,IAAIL,MAAJ,CAAWD,GAAX,CAAP;EACD;;AA1OuB"}