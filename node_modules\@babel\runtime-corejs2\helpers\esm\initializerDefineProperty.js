import _Object$defineProperty from "@babel/runtime-corejs2/core-js/object/define-property";
export default function _initializerDefineProperty(target, property, descriptor, context) {
  if (!descriptor) return;
  _Object$defineProperty(target, property, {
    enumerable: descriptor.enumerable,
    configurable: descriptor.configurable,
    writable: descriptor.writable,
    value: descriptor.initializer ? descriptor.initializer.call(context) : void 0
  });
}