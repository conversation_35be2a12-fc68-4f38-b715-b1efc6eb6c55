var _Object$setPrototypeOf = require("@babel/runtime-corejs2/core-js/object/set-prototype-of");
var _Object$getPrototypeOf = require("@babel/runtime-corejs2/core-js/object/get-prototype-of");
function _getPrototypeOf(o) {
  module.exports = _getPrototypeOf = _Object$setPrototypeOf ? _Object$getPrototypeOf.bind() : function _getPrototypeOf(o) {
    return o.__proto__ || _Object$getPrototypeOf(o);
  }, module.exports.__esModule = true, module.exports["default"] = module.exports;
  return _getPrototypeOf(o);
}
module.exports = _getPrototypeOf, module.exports.__esModule = true, module.exports["default"] = module.exports;