# motea Environment Variables Example
# Based on the open-source project Notea, originally created by qingwei-li<<EMAIL>>
# Modified and maintained by waycaan, 2025.

# ===========================================
# Quick Setup Guide
# ===========================================
# 1. Copy this file to .env.local
# 2. Choose a database provider and configure DATABASE_URL
# 3. Set authentication (PASSWORD or DISABLE_PASSWORD)
# 4. Customize optional settings as needed
#
# For detailed setup, use the configuration manager:
#   node scripts/config-manager.js
#   or
#   .\scripts\config-manager.ps1

# ===========================================
# Application Configuration
# ===========================================
NODE_ENV=production
PORT=3000

# ===========================================
# Authentication Configuration
# ===========================================
# Option 1: Set a password for the application
PASSWORD=your_secure_password_here

# Option 2: Disable password authentication (uncomment the line below and comment out PASSWORD above)
# DISABLE_PASSWORD=true

# ===========================================
# Database Configuration
# ===========================================
# Choose ONE of the following database configurations:

# Option 1: Neon PostgreSQL (recommended for Vercel/production)
# DATABASE_URL=postgres://username:<EMAIL>/dbname?sslmode=require
# DB_PROVIDER=neon

# Option 2: Supabase PostgreSQL
# DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres
# DB_PROVIDER=supabase

# Option 3: Self-hosted PostgreSQL
# DATABASE_URL=postgresql://username:password@localhost:5432/motea
# DB_PROVIDER=self-hosted

# Option 4: Local PostgreSQL (for Docker Compose)
DATABASE_URL=********************************************/motea
DB_PROVIDER=self-hosted

# Database table prefix (optional)
# STORE_PREFIX=motea_

# ===========================================
# Performance Configuration
# ===========================================
# Number of notes to preload (recommended: 10-20)
PRELOAD_NOTES_COUNT=10

# Logging level (error, warn, info, debug)
LOG_LEVEL=info

# ===========================================
# Session Configuration
# ===========================================
# Session secret for secure cookies (generate a random string)
SESSION_SECRET=your_session_secret_here

# Use secure cookies in production
COOKIE_SECURE=true

# ===========================================
# Optional Configuration
# ===========================================
# Base URL for the application (useful for reverse proxies)
# BASE_URL=https://your-domain.com

# Disable Next.js telemetry
NEXT_TELEMETRY_DISABLED=1

# ===========================================
# Provider-specific Examples
# ===========================================
# For detailed configuration examples, see:
#   config/env.neon.example      - Neon PostgreSQL setup
#   config/env.supabase.example  - Supabase PostgreSQL setup
#   config/env.self-hosted.example - Self-hosted PostgreSQL setup

# ===========================================
# Docker Configuration
# ===========================================
# When using Docker, these are automatically configured:
# HOSTNAME=0.0.0.0
# NODE_ENV=production
