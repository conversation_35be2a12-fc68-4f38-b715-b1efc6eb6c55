{"version": 3, "names": ["arrayExpression", "assignmentExpression", "binaryExpression", "blockStatement", "callExpression", "cloneNode", "conditionalExpression", "exportNamedDeclaration", "exportSpecifier", "expressionStatement", "functionExpression", "identifier", "memberExpression", "objectExpression", "program", "stringLiteral", "unaryExpression", "variableDeclaration", "variableDeclarator", "buildUmdWrapper", "replacements", "template", "statement", "buildGlobal", "allowlist", "namespace", "body", "container", "tree", "push", "buildHelpers", "buildModule", "refs", "unshift", "Object", "keys", "map", "name", "buildUmd", "FACTORY_PARAMETERS", "BROWSER_ARGUMENTS", "COMMON_ARGUMENTS", "AMD_ARGUMENTS", "FACTORY_BODY", "UMD_ROOT", "buildVar", "getHelperReference", "helpers", "list", "for<PERSON>ach", "indexOf", "ref", "ensure", "File", "nodes", "get", "outputType", "build", "global", "module", "umd", "var", "Error", "generator", "code"], "sources": ["../../src/tools/build-external-helpers.ts"], "sourcesContent": ["import * as helpers from \"@babel/helpers\";\nimport generator from \"@babel/generator\";\nimport template from \"@babel/template\";\nimport {\n  arrayExpression,\n  assignmentExpression,\n  binaryExpression,\n  blockStatement,\n  callExpression,\n  cloneNode,\n  conditionalExpression,\n  exportNamedDeclaration,\n  exportSpecifier,\n  expressionStatement,\n  functionExpression,\n  identifier,\n  memberExpression,\n  objectExpression,\n  program,\n  stringLiteral,\n  unaryExpression,\n  variableDeclaration,\n  variableDeclarator,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport File from \"../transformation/file/file\";\nimport type { PublicReplacements } from \"@babel/template/src/options\";\n\n// Wrapped to avoid wasting time parsing this when almost no-one uses\n// build-external-helpers.\nconst buildUmdWrapper = (replacements: PublicReplacements) =>\n  template.statement`\n    (function (root, factory) {\n      if (typeof define === \"function\" && define.amd) {\n        define(AMD_ARGUMENTS, factory);\n      } else if (typeof exports === \"object\") {\n        factory(COMMON_ARGUMENTS);\n      } else {\n        factory(BROWSER_ARGUMENTS);\n      }\n    })(UMD_ROOT, function (FACTORY_PARAMETERS) {\n      FACTORY_BODY\n    });\n  `(replacements);\n\nfunction buildGlobal(allowlist?: Array<string>) {\n  const namespace = identifier(\"babelHelpers\");\n\n  const body: t.Statement[] = [];\n  const container = functionExpression(\n    null,\n    [identifier(\"global\")],\n    blockStatement(body),\n  );\n  const tree = program([\n    expressionStatement(\n      callExpression(container, [\n        // typeof global === \"undefined\" ? self : global\n        conditionalExpression(\n          binaryExpression(\n            \"===\",\n            unaryExpression(\"typeof\", identifier(\"global\")),\n            stringLiteral(\"undefined\"),\n          ),\n          identifier(\"self\"),\n          identifier(\"global\"),\n        ),\n      ]),\n    ),\n  ]);\n\n  body.push(\n    variableDeclaration(\"var\", [\n      variableDeclarator(\n        namespace,\n        assignmentExpression(\n          \"=\",\n          memberExpression(identifier(\"global\"), namespace),\n          objectExpression([]),\n        ),\n      ),\n    ]),\n  );\n\n  buildHelpers(body, namespace, allowlist);\n\n  return tree;\n}\n\nfunction buildModule(allowlist?: Array<string>) {\n  const body: t.Statement[] = [];\n  const refs = buildHelpers(body, null, allowlist);\n\n  body.unshift(\n    exportNamedDeclaration(\n      null,\n      Object.keys(refs).map(name => {\n        return exportSpecifier(cloneNode(refs[name]), identifier(name));\n      }),\n    ),\n  );\n\n  return program(body, [], \"module\");\n}\n\nfunction buildUmd(allowlist?: Array<string>) {\n  const namespace = identifier(\"babelHelpers\");\n\n  const body: t.Statement[] = [];\n  body.push(\n    variableDeclaration(\"var\", [\n      variableDeclarator(namespace, identifier(\"global\")),\n    ]),\n  );\n\n  buildHelpers(body, namespace, allowlist);\n\n  return program([\n    buildUmdWrapper({\n      FACTORY_PARAMETERS: identifier(\"global\"),\n      BROWSER_ARGUMENTS: assignmentExpression(\n        \"=\",\n        memberExpression(identifier(\"root\"), namespace),\n        objectExpression([]),\n      ),\n      COMMON_ARGUMENTS: identifier(\"exports\"),\n      AMD_ARGUMENTS: arrayExpression([stringLiteral(\"exports\")]),\n      FACTORY_BODY: body,\n      UMD_ROOT: identifier(\"this\"),\n    }),\n  ]);\n}\n\nfunction buildVar(allowlist?: Array<string>) {\n  const namespace = identifier(\"babelHelpers\");\n\n  const body: t.Statement[] = [];\n  body.push(\n    variableDeclaration(\"var\", [\n      variableDeclarator(namespace, objectExpression([])),\n    ]),\n  );\n  const tree = program(body);\n  buildHelpers(body, namespace, allowlist);\n  body.push(expressionStatement(namespace));\n  return tree;\n}\n\nfunction buildHelpers(\n  body: t.Statement[],\n  namespace: t.Expression,\n  allowlist?: Array<string>,\n): Record<string, t.MemberExpression>;\nfunction buildHelpers(\n  body: t.Statement[],\n  namespace: null,\n  allowlist?: Array<string>,\n): Record<string, t.Identifier>;\n\nfunction buildHelpers(\n  body: t.Statement[],\n  namespace: t.Expression | null,\n  allowlist?: Array<string>,\n) {\n  const getHelperReference = (name: string) => {\n    return namespace\n      ? memberExpression(namespace, identifier(name))\n      : identifier(`_${name}`);\n  };\n\n  const refs: { [key: string]: t.Identifier | t.MemberExpression } = {};\n  helpers.list.forEach(function (name) {\n    if (allowlist && allowlist.indexOf(name) < 0) return;\n\n    const ref = (refs[name] = getHelperReference(name));\n\n    helpers.ensure(name, File);\n    const { nodes } = helpers.get(name, getHelperReference, ref);\n\n    body.push(...nodes);\n  });\n  return refs;\n}\nexport default function (\n  allowlist?: Array<string>,\n  outputType: \"global\" | \"module\" | \"umd\" | \"var\" = \"global\",\n) {\n  let tree: t.Program;\n\n  const build = {\n    global: buildGlobal,\n    module: buildModule,\n    umd: buildUmd,\n    var: buildVar,\n  }[outputType];\n\n  if (build) {\n    tree = build(allowlist);\n  } else {\n    throw new Error(`Unsupported output type ${outputType}`);\n  }\n\n  return generator(tree).code;\n}\n"], "mappings": ";;;;;;;AAAA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AACA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AACA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AACA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AAsBA;;;EArBEA,e;EACAC,oB;EACAC,gB;EACAC,c;EACAC,c;EACAC,S;EACAC,qB;EACAC,sB;EACAC,e;EACAC,mB;EACAC,kB;EACAC,U;EACAC,gB;EACAC,gB;EACAC,O;EACAC,a;EACAC,e;EACAC,mB;EACAC;;;AAQF,MAAMC,eAAe,GAAIC,YAAD,IACtBC,mBAAA,CAASC,SAAU;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAZE,CAYEF,YAZF,CADF;;AAeA,SAASG,WAAT,CAAqBC,SAArB,EAAgD;EAC9C,MAAMC,SAAS,GAAGd,UAAU,CAAC,cAAD,CAA5B;EAEA,MAAMe,IAAmB,GAAG,EAA5B;EACA,MAAMC,SAAS,GAAGjB,kBAAkB,CAClC,IADkC,EAElC,CAACC,UAAU,CAAC,QAAD,CAAX,CAFkC,EAGlCR,cAAc,CAACuB,IAAD,CAHoB,CAApC;EAKA,MAAME,IAAI,GAAGd,OAAO,CAAC,CACnBL,mBAAmB,CACjBL,cAAc,CAACuB,SAAD,EAAY,CAExBrB,qBAAqB,CACnBJ,gBAAgB,CACd,KADc,EAEdc,eAAe,CAAC,QAAD,EAAWL,UAAU,CAAC,QAAD,CAArB,CAFD,EAGdI,aAAa,CAAC,WAAD,CAHC,CADG,EAMnBJ,UAAU,CAAC,MAAD,CANS,EAOnBA,UAAU,CAAC,QAAD,CAPS,CAFG,CAAZ,CADG,CADA,CAAD,CAApB;EAiBAe,IAAI,CAACG,IAAL,CACEZ,mBAAmB,CAAC,KAAD,EAAQ,CACzBC,kBAAkB,CAChBO,SADgB,EAEhBxB,oBAAoB,CAClB,GADkB,EAElBW,gBAAgB,CAACD,UAAU,CAAC,QAAD,CAAX,EAAuBc,SAAvB,CAFE,EAGlBZ,gBAAgB,CAAC,EAAD,CAHE,CAFJ,CADO,CAAR,CADrB;EAaAiB,YAAY,CAACJ,IAAD,EAAOD,SAAP,EAAkBD,SAAlB,CAAZ;EAEA,OAAOI,IAAP;AACD;;AAED,SAASG,WAAT,CAAqBP,SAArB,EAAgD;EAC9C,MAAME,IAAmB,GAAG,EAA5B;EACA,MAAMM,IAAI,GAAGF,YAAY,CAACJ,IAAD,EAAO,IAAP,EAAaF,SAAb,CAAzB;EAEAE,IAAI,CAACO,OAAL,CACE1B,sBAAsB,CACpB,IADoB,EAEpB2B,MAAM,CAACC,IAAP,CAAYH,IAAZ,EAAkBI,GAAlB,CAAsBC,IAAI,IAAI;IAC5B,OAAO7B,eAAe,CAACH,SAAS,CAAC2B,IAAI,CAACK,IAAD,CAAL,CAAV,EAAwB1B,UAAU,CAAC0B,IAAD,CAAlC,CAAtB;EACD,CAFD,CAFoB,CADxB;EASA,OAAOvB,OAAO,CAACY,IAAD,EAAO,EAAP,EAAW,QAAX,CAAd;AACD;;AAED,SAASY,QAAT,CAAkBd,SAAlB,EAA6C;EAC3C,MAAMC,SAAS,GAAGd,UAAU,CAAC,cAAD,CAA5B;EAEA,MAAMe,IAAmB,GAAG,EAA5B;EACAA,IAAI,CAACG,IAAL,CACEZ,mBAAmB,CAAC,KAAD,EAAQ,CACzBC,kBAAkB,CAACO,SAAD,EAAYd,UAAU,CAAC,QAAD,CAAtB,CADO,CAAR,CADrB;EAMAmB,YAAY,CAACJ,IAAD,EAAOD,SAAP,EAAkBD,SAAlB,CAAZ;EAEA,OAAOV,OAAO,CAAC,CACbK,eAAe,CAAC;IACdoB,kBAAkB,EAAE5B,UAAU,CAAC,QAAD,CADhB;IAEd6B,iBAAiB,EAAEvC,oBAAoB,CACrC,GADqC,EAErCW,gBAAgB,CAACD,UAAU,CAAC,MAAD,CAAX,EAAqBc,SAArB,CAFqB,EAGrCZ,gBAAgB,CAAC,EAAD,CAHqB,CAFzB;IAOd4B,gBAAgB,EAAE9B,UAAU,CAAC,SAAD,CAPd;IAQd+B,aAAa,EAAE1C,eAAe,CAAC,CAACe,aAAa,CAAC,SAAD,CAAd,CAAD,CARhB;IASd4B,YAAY,EAAEjB,IATA;IAUdkB,QAAQ,EAAEjC,UAAU,CAAC,MAAD;EAVN,CAAD,CADF,CAAD,CAAd;AAcD;;AAED,SAASkC,QAAT,CAAkBrB,SAAlB,EAA6C;EAC3C,MAAMC,SAAS,GAAGd,UAAU,CAAC,cAAD,CAA5B;EAEA,MAAMe,IAAmB,GAAG,EAA5B;EACAA,IAAI,CAACG,IAAL,CACEZ,mBAAmB,CAAC,KAAD,EAAQ,CACzBC,kBAAkB,CAACO,SAAD,EAAYZ,gBAAgB,CAAC,EAAD,CAA5B,CADO,CAAR,CADrB;EAKA,MAAMe,IAAI,GAAGd,OAAO,CAACY,IAAD,CAApB;EACAI,YAAY,CAACJ,IAAD,EAAOD,SAAP,EAAkBD,SAAlB,CAAZ;EACAE,IAAI,CAACG,IAAL,CAAUpB,mBAAmB,CAACgB,SAAD,CAA7B;EACA,OAAOG,IAAP;AACD;;AAaD,SAASE,YAAT,CACEJ,IADF,EAEED,SAFF,EAGED,SAHF,EAIE;EACA,MAAMsB,kBAAkB,GAAIT,IAAD,IAAkB;IAC3C,OAAOZ,SAAS,GACZb,gBAAgB,CAACa,SAAD,EAAYd,UAAU,CAAC0B,IAAD,CAAtB,CADJ,GAEZ1B,UAAU,CAAE,IAAG0B,IAAK,EAAV,CAFd;EAGD,CAJD;;EAMA,MAAML,IAA0D,GAAG,EAAnE;EACAe,OAAO,GAACC,IAAR,CAAaC,OAAb,CAAqB,UAAUZ,IAAV,EAAgB;IACnC,IAAIb,SAAS,IAAIA,SAAS,CAAC0B,OAAV,CAAkBb,IAAlB,IAA0B,CAA3C,EAA8C;IAE9C,MAAMc,GAAG,GAAInB,IAAI,CAACK,IAAD,CAAJ,GAAaS,kBAAkB,CAACT,IAAD,CAA5C;IAEAU,OAAO,GAACK,MAAR,CAAef,IAAf,EAAqBgB,aAArB;IACA,MAAM;MAAEC;IAAF,IAAYP,OAAO,GAACQ,GAAR,CAAYlB,IAAZ,EAAkBS,kBAAlB,EAAsCK,GAAtC,CAAlB;IAEAzB,IAAI,CAACG,IAAL,CAAU,GAAGyB,KAAb;EACD,CATD;EAUA,OAAOtB,IAAP;AACD;;AACc,kBACbR,SADa,EAEbgC,UAA+C,GAAG,QAFrC,EAGb;EACA,IAAI5B,IAAJ;EAEA,MAAM6B,KAAK,GAAG;IACZC,MAAM,EAAEnC,WADI;IAEZoC,MAAM,EAAE5B,WAFI;IAGZ6B,GAAG,EAAEtB,QAHO;IAIZuB,GAAG,EAAEhB;EAJO,EAKZW,UALY,CAAd;;EAOA,IAAIC,KAAJ,EAAW;IACT7B,IAAI,GAAG6B,KAAK,CAACjC,SAAD,CAAZ;EACD,CAFD,MAEO;IACL,MAAM,IAAIsC,KAAJ,CAAW,2BAA0BN,UAAW,EAAhD,CAAN;EACD;;EAED,OAAO,IAAAO,oBAAA,EAAUnC,IAAV,EAAgBoC,IAAvB;AACD"}