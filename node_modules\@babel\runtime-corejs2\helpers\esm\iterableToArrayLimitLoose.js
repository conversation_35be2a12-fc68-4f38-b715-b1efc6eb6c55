import _Symbol from "@babel/runtime-corejs2/core-js/symbol";
import _Symbol$iterator from "@babel/runtime-corejs2/core-js/symbol/iterator";
export default function _iterableToArrayLimitLoose(arr, i) {
  var _i = arr && (typeof _Symbol !== "undefined" && arr[_Symbol$iterator] || arr["@@iterator"]);
  if (_i == null) return;
  var _arr = [];
  for (_i = _i.call(arr), _step; !(_step = _i.next()).done;) {
    _arr.push(_step.value);
    if (i && _arr.length === i) break;
  }
  return _arr;
}