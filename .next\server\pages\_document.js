/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_document";
exports.ids = ["pages/_document"];
exports.modules = {

/***/ "./node_modules/next/dist/pages/_document.js":
/*!***************************************************!*\
  !*** ./node_modules/next/dist/pages/_document.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.Html = Html;\nexports.Main = Main;\nexports[\"default\"] = void 0;\nvar _react = _interopRequireWildcard(__webpack_require__(/*! react */ \"react\"));\nvar _constants = __webpack_require__(/*! ../shared/lib/constants */ \"../shared/lib/constants\");\nvar _getPageFiles = __webpack_require__(/*! ../server/get-page-files */ \"../server/get-page-files\");\nvar _htmlescape = __webpack_require__(/*! ../server/htmlescape */ \"../server/htmlescape\");\nvar _isError = _interopRequireDefault(__webpack_require__(/*! ../lib/is-error */ \"./node_modules/next/dist/lib/is-error.js\"));\nvar _htmlContext = __webpack_require__(/*! ../shared/lib/html-context */ \"../shared/lib/html-context\");\nclass Document extends _react.default.Component {\n    /**\n   * `getInitialProps` hook returns the context object with the addition of `renderPage`.\n   * `renderPage` callback executes `React` rendering logic synchronously to support server-rendering wrappers\n   */ static getInitialProps(ctx) {\n        return ctx.defaultGetInitialProps(ctx);\n    }\n    render() {\n        return /*#__PURE__*/ _react.default.createElement(Html, null, /*#__PURE__*/ _react.default.createElement(Head, null), /*#__PURE__*/ _react.default.createElement(\"body\", null, /*#__PURE__*/ _react.default.createElement(Main, null), /*#__PURE__*/ _react.default.createElement(NextScript, null)));\n    }\n}\nexports[\"default\"] = Document;\nfunction _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\nfunction _getRequireWildcardCache() {\n    if (typeof WeakMap !== \"function\") return null;\n    var cache = new WeakMap();\n    _getRequireWildcardCache = function() {\n        return cache;\n    };\n    return cache;\n}\nfunction _interopRequireWildcard(obj) {\n    if (obj && obj.__esModule) {\n        return obj;\n    }\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n        return {\n            default: obj\n        };\n    }\n    var cache = _getRequireWildcardCache();\n    if (cache && cache.has(obj)) {\n        return cache.get(obj);\n    }\n    var newObj = {};\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n    for(var key in obj){\n        if (Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) {\n                Object.defineProperty(newObj, key, desc);\n            } else {\n                newObj[key] = obj[key];\n            }\n        }\n    }\n    newObj.default = obj;\n    if (cache) {\n        cache.set(obj, newObj);\n    }\n    return newObj;\n}\nfunction getDocumentFiles(buildManifest, pathname, inAmpMode) {\n    const sharedFiles = (0, _getPageFiles).getPageFiles(buildManifest, \"/_app\");\n    const pageFiles =  true && inAmpMode ? [] : (0, _getPageFiles).getPageFiles(buildManifest, pathname);\n    return {\n        sharedFiles,\n        pageFiles,\n        allFiles: [\n            ...new Set([\n                ...sharedFiles,\n                ...pageFiles\n            ])\n        ]\n    };\n}\nfunction getPolyfillScripts(context, props) {\n    // polyfills.js has to be rendered as nomodule without async\n    // It also has to be the first script to load\n    const { assetPrefix , buildManifest , devOnlyCacheBusterQueryString , disableOptimizedLoading , crossOrigin ,  } = context;\n    return buildManifest.polyfillFiles.filter((polyfill)=>polyfill.endsWith(\".js\") && !polyfill.endsWith(\".module.js\")).map((polyfill)=>/*#__PURE__*/ _react.default.createElement(\"script\", {\n            key: polyfill,\n            defer: !disableOptimizedLoading,\n            nonce: props.nonce,\n            crossOrigin: props.crossOrigin || crossOrigin,\n            noModule: true,\n            src: `${assetPrefix}/_next/${polyfill}${devOnlyCacheBusterQueryString}`\n        }));\n}\nfunction hasComponentProps(child) {\n    return !!child && !!child.props;\n}\nfunction AmpStyles({ styles  }) {\n    if (!styles) return null;\n    // try to parse styles from fragment for backwards compat\n    const curStyles = Array.isArray(styles) ? styles : [];\n    if (styles.props && // @ts-ignore Property 'props' does not exist on type ReactElement\n    Array.isArray(styles.props.children)) {\n        const hasStyles = (el)=>{\n            var ref, ref1;\n            return el == null ? void 0 : (ref = el.props) == null ? void 0 : (ref1 = ref.dangerouslySetInnerHTML) == null ? void 0 : ref1.__html;\n        };\n        // @ts-ignore Property 'props' does not exist on type ReactElement\n        styles.props.children.forEach((child)=>{\n            if (Array.isArray(child)) {\n                child.forEach((el)=>hasStyles(el) && curStyles.push(el));\n            } else if (hasStyles(child)) {\n                curStyles.push(child);\n            }\n        });\n    }\n    /* Add custom styles before AMP styles to prevent accidental overrides */ return /*#__PURE__*/ _react.default.createElement(\"style\", {\n        \"amp-custom\": \"\",\n        dangerouslySetInnerHTML: {\n            __html: curStyles.map((style)=>style.props.dangerouslySetInnerHTML.__html).join(\"\").replace(/\\/\\*# sourceMappingURL=.*\\*\\//g, \"\").replace(/\\/\\*@ sourceURL=.*?\\*\\//g, \"\")\n        }\n    });\n}\nfunction getDynamicChunks(context, props, files) {\n    const { dynamicImports , assetPrefix , isDevelopment , devOnlyCacheBusterQueryString , disableOptimizedLoading , crossOrigin ,  } = context;\n    return dynamicImports.map((file)=>{\n        if (!file.endsWith(\".js\") || files.allFiles.includes(file)) return null;\n        return /*#__PURE__*/ _react.default.createElement(\"script\", {\n            async: !isDevelopment && disableOptimizedLoading,\n            defer: !disableOptimizedLoading,\n            key: file,\n            src: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n            nonce: props.nonce,\n            crossOrigin: props.crossOrigin || crossOrigin\n        });\n    });\n}\nfunction getScripts(context, props, files) {\n    var ref;\n    const { assetPrefix , buildManifest , isDevelopment , devOnlyCacheBusterQueryString , disableOptimizedLoading , crossOrigin ,  } = context;\n    const normalScripts = files.allFiles.filter((file)=>file.endsWith(\".js\"));\n    const lowPriorityScripts = (ref = buildManifest.lowPriorityFiles) == null ? void 0 : ref.filter((file)=>file.endsWith(\".js\"));\n    return [\n        ...normalScripts,\n        ...lowPriorityScripts\n    ].map((file)=>{\n        return /*#__PURE__*/ _react.default.createElement(\"script\", {\n            key: file,\n            src: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n            nonce: props.nonce,\n            async: !isDevelopment && disableOptimizedLoading,\n            defer: !disableOptimizedLoading,\n            crossOrigin: props.crossOrigin || crossOrigin\n        });\n    });\n}\nfunction getPreNextWorkerScripts(context, props) {\n    const { assetPrefix , scriptLoader , crossOrigin , nextScriptWorkers  } = context;\n    // disable `nextScriptWorkers` in edge runtime\n    if (!nextScriptWorkers || \"nodejs\" === \"edge\") return null;\n    try {\n        let { partytownSnippet  } = require(\"@builder.io/partytown/integration\");\n        const children = Array.isArray(props.children) ? props.children : [\n            props.children\n        ];\n        // Check to see if the user has defined their own Partytown configuration\n        const userDefinedConfig = children.find((child)=>{\n            var ref, ref2;\n            return hasComponentProps(child) && (child == null ? void 0 : (ref = child.props) == null ? void 0 : (ref2 = ref.dangerouslySetInnerHTML) == null ? void 0 : ref2.__html.length) && \"data-partytown-config\" in child.props;\n        });\n        return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !userDefinedConfig && /*#__PURE__*/ _react.default.createElement(\"script\", {\n            \"data-partytown-config\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: `\n            partytown = {\n              lib: \"${assetPrefix}/_next/static/~partytown/\"\n            };\n          `\n            }\n        }), /*#__PURE__*/ _react.default.createElement(\"script\", {\n            \"data-partytown\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: partytownSnippet()\n            }\n        }), (scriptLoader.worker || []).map((file, index)=>{\n            const { strategy , src , children: scriptChildren , dangerouslySetInnerHTML , ...scriptProps } = file;\n            let srcProps = {};\n            if (src) {\n                // Use external src if provided\n                srcProps.src = src;\n            } else if (dangerouslySetInnerHTML && dangerouslySetInnerHTML.__html) {\n                // Embed inline script if provided with dangerouslySetInnerHTML\n                srcProps.dangerouslySetInnerHTML = {\n                    __html: dangerouslySetInnerHTML.__html\n                };\n            } else if (scriptChildren) {\n                // Embed inline script if provided with children\n                srcProps.dangerouslySetInnerHTML = {\n                    __html: typeof scriptChildren === \"string\" ? scriptChildren : Array.isArray(scriptChildren) ? scriptChildren.join(\"\") : \"\"\n                };\n            } else {\n                throw new Error(\"Invalid usage of next/script. Did you forget to include a src attribute or an inline script? https://nextjs.org/docs/messages/invalid-script\");\n            }\n            return /*#__PURE__*/ _react.default.createElement(\"script\", Object.assign({}, srcProps, scriptProps, {\n                type: \"text/partytown\",\n                key: src || index,\n                nonce: props.nonce,\n                \"data-nscript\": \"worker\",\n                crossOrigin: props.crossOrigin || crossOrigin\n            }));\n        }));\n    } catch (err) {\n        if ((0, _isError).default(err) && err.code !== \"MODULE_NOT_FOUND\") {\n            console.warn(`Warning: ${err.message}`);\n        }\n        return null;\n    }\n}\nfunction getPreNextScripts(context, props) {\n    const { scriptLoader , disableOptimizedLoading , crossOrigin  } = context;\n    const webWorkerScripts = getPreNextWorkerScripts(context, props);\n    const beforeInteractiveScripts = (scriptLoader.beforeInteractive || []).filter((script)=>script.src).map((file, index)=>{\n        const { strategy , ...scriptProps } = file;\n        var _defer;\n        return /*#__PURE__*/ _react.default.createElement(\"script\", Object.assign({}, scriptProps, {\n            key: scriptProps.src || index,\n            defer: (_defer = scriptProps.defer) != null ? _defer : !disableOptimizedLoading,\n            nonce: props.nonce,\n            \"data-nscript\": \"beforeInteractive\",\n            crossOrigin: props.crossOrigin || crossOrigin\n        }));\n    });\n    return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, webWorkerScripts, beforeInteractiveScripts);\n}\nfunction getHeadHTMLProps(props) {\n    const { crossOrigin , nonce , ...restProps } = props;\n    // This assignment is necessary for additional type checking to avoid unsupported attributes in <head>\n    const headProps = restProps;\n    return headProps;\n}\nfunction getAmpPath(ampPath, asPath) {\n    return ampPath || `${asPath}${asPath.includes(\"?\") ? \"&\" : \"?\"}amp=1`;\n}\nclass Head extends _react.default.Component {\n    static contextType = _htmlContext.HtmlContext;\n    getCssLinks(files) {\n        const { assetPrefix , devOnlyCacheBusterQueryString , dynamicImports , crossOrigin , optimizeCss , optimizeFonts ,  } = this.context;\n        const cssFiles = files.allFiles.filter((f)=>f.endsWith(\".css\"));\n        const sharedFiles = new Set(files.sharedFiles);\n        // Unmanaged files are CSS files that will be handled directly by the\n        // webpack runtime (`mini-css-extract-plugin`).\n        let unmangedFiles = new Set([]);\n        let dynamicCssFiles = Array.from(new Set(dynamicImports.filter((file)=>file.endsWith(\".css\"))));\n        if (dynamicCssFiles.length) {\n            const existing = new Set(cssFiles);\n            dynamicCssFiles = dynamicCssFiles.filter((f)=>!(existing.has(f) || sharedFiles.has(f)));\n            unmangedFiles = new Set(dynamicCssFiles);\n            cssFiles.push(...dynamicCssFiles);\n        }\n        let cssLinkElements = [];\n        cssFiles.forEach((file)=>{\n            const isSharedFile = sharedFiles.has(file);\n            if (!optimizeCss) {\n                cssLinkElements.push(/*#__PURE__*/ _react.default.createElement(\"link\", {\n                    key: `${file}-preload`,\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                    as: \"style\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                }));\n            }\n            const isUnmanagedFile = unmangedFiles.has(file);\n            cssLinkElements.push(/*#__PURE__*/ _react.default.createElement(\"link\", {\n                key: file,\n                nonce: this.props.nonce,\n                rel: \"stylesheet\",\n                href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                crossOrigin: this.props.crossOrigin || crossOrigin,\n                \"data-n-g\": isUnmanagedFile ? undefined : isSharedFile ? \"\" : undefined,\n                \"data-n-p\": isUnmanagedFile ? undefined : isSharedFile ? undefined : \"\"\n            }));\n        });\n        if (false) {}\n        return cssLinkElements.length === 0 ? null : cssLinkElements;\n    }\n    getPreloadDynamicChunks() {\n        const { dynamicImports , assetPrefix , devOnlyCacheBusterQueryString , crossOrigin ,  } = this.context;\n        return dynamicImports.map((file)=>{\n            if (!file.endsWith(\".js\")) {\n                return null;\n            }\n            return /*#__PURE__*/ _react.default.createElement(\"link\", {\n                rel: \"preload\",\n                key: file,\n                href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                as: \"script\",\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin\n            });\n        }) // Filter out nulled scripts\n        .filter(Boolean);\n    }\n    getPreloadMainLinks(files) {\n        const { assetPrefix , devOnlyCacheBusterQueryString , scriptLoader , crossOrigin ,  } = this.context;\n        const preloadFiles = files.allFiles.filter((file)=>{\n            return file.endsWith(\".js\");\n        });\n        return [\n            ...(scriptLoader.beforeInteractive || []).map((file)=>/*#__PURE__*/ _react.default.createElement(\"link\", {\n                    key: file.src,\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: file.src,\n                    as: \"script\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                })),\n            ...preloadFiles.map((file)=>/*#__PURE__*/ _react.default.createElement(\"link\", {\n                    key: file,\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                    as: \"script\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                })), \n        ];\n    }\n    getBeforeInteractiveInlineScripts() {\n        const { scriptLoader  } = this.context;\n        const { nonce , crossOrigin  } = this.props;\n        return (scriptLoader.beforeInteractive || []).filter((script)=>!script.src && (script.dangerouslySetInnerHTML || script.children)).map((file, index)=>{\n            const { strategy , children , dangerouslySetInnerHTML , src , ...scriptProps } = file;\n            let html = \"\";\n            if (dangerouslySetInnerHTML && dangerouslySetInnerHTML.__html) {\n                html = dangerouslySetInnerHTML.__html;\n            } else if (children) {\n                html = typeof children === \"string\" ? children : Array.isArray(children) ? children.join(\"\") : \"\";\n            }\n            return /*#__PURE__*/ _react.default.createElement(\"script\", Object.assign({}, scriptProps, {\n                dangerouslySetInnerHTML: {\n                    __html: html\n                },\n                key: scriptProps.id || index,\n                nonce: nonce,\n                \"data-nscript\": \"beforeInteractive\",\n                crossOrigin: crossOrigin || undefined\n            }));\n        });\n    }\n    getDynamicChunks(files) {\n        return getDynamicChunks(this.context, this.props, files);\n    }\n    getPreNextScripts() {\n        return getPreNextScripts(this.context, this.props);\n    }\n    getScripts(files) {\n        return getScripts(this.context, this.props, files);\n    }\n    getPolyfillScripts() {\n        return getPolyfillScripts(this.context, this.props);\n    }\n    makeStylesheetInert(node) {\n        return _react.default.Children.map(node, (c)=>{\n            var ref5, ref3;\n            if ((c == null ? void 0 : c.type) === \"link\" && (c == null ? void 0 : (ref5 = c.props) == null ? void 0 : ref5.href) && _constants.OPTIMIZED_FONT_PROVIDERS.some(({ url  })=>{\n                var ref, ref4;\n                return c == null ? void 0 : (ref = c.props) == null ? void 0 : (ref4 = ref.href) == null ? void 0 : ref4.startsWith(url);\n            })) {\n                const newProps = {\n                    ...c.props || {},\n                    \"data-href\": c.props.href,\n                    href: undefined\n                };\n                return /*#__PURE__*/ _react.default.cloneElement(c, newProps);\n            } else if (c == null ? void 0 : (ref3 = c.props) == null ? void 0 : ref3.children) {\n                const newProps1 = {\n                    ...c.props || {},\n                    children: this.makeStylesheetInert(c.props.children)\n                };\n                return /*#__PURE__*/ _react.default.cloneElement(c, newProps1);\n            }\n            return c;\n        }).filter(Boolean);\n    }\n    render() {\n        const { styles , ampPath , inAmpMode , hybridAmp , canonicalBase , __NEXT_DATA__ , dangerousAsPath , headTags , unstable_runtimeJS , unstable_JsPreload , disableOptimizedLoading , optimizeCss , optimizeFonts ,  } = this.context;\n        const disableRuntimeJS = unstable_runtimeJS === false;\n        const disableJsPreload = unstable_JsPreload === false || !disableOptimizedLoading;\n        this.context.docComponentsRendered.Head = true;\n        let { head  } = this.context;\n        let cssPreloads = [];\n        let otherHeadElements = [];\n        if (head) {\n            head.forEach((c)=>{\n                if (c && c.type === \"link\" && c.props[\"rel\"] === \"preload\" && c.props[\"as\"] === \"style\") {\n                    cssPreloads.push(c);\n                } else {\n                    c && otherHeadElements.push(c);\n                }\n            });\n            head = cssPreloads.concat(otherHeadElements);\n        }\n        let children = _react.default.Children.toArray(this.props.children).filter(Boolean);\n        // show a warning if Head contains <title> (only in development)\n        if (true) {\n            children = _react.default.Children.map(children, (child)=>{\n                var ref;\n                const isReactHelmet = child == null ? void 0 : (ref = child.props) == null ? void 0 : ref[\"data-react-helmet\"];\n                if (!isReactHelmet) {\n                    var ref6;\n                    if ((child == null ? void 0 : child.type) === \"title\") {\n                        console.warn(\"Warning: <title> should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-title\");\n                    } else if ((child == null ? void 0 : child.type) === \"meta\" && (child == null ? void 0 : (ref6 = child.props) == null ? void 0 : ref6.name) === \"viewport\") {\n                        console.warn(\"Warning: viewport meta tags should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-viewport-meta\");\n                    }\n                }\n                return child;\n            });\n            if (this.props.crossOrigin) console.warn(\"Warning: `Head` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated\");\n        }\n        if (false) {}\n        let hasAmphtmlRel = false;\n        let hasCanonicalRel = false;\n        // show warning and remove conflicting amp head tags\n        head = _react.default.Children.map(head || [], (child)=>{\n            if (!child) return child;\n            const { type , props  } = child;\n            if ( true && inAmpMode) {\n                let badProp = \"\";\n                if (type === \"meta\" && props.name === \"viewport\") {\n                    badProp = 'name=\"viewport\"';\n                } else if (type === \"link\" && props.rel === \"canonical\") {\n                    hasCanonicalRel = true;\n                } else if (type === \"script\") {\n                    // only block if\n                    // 1. it has a src and isn't pointing to ampproject's CDN\n                    // 2. it is using dangerouslySetInnerHTML without a type or\n                    // a type of text/javascript\n                    if (props.src && props.src.indexOf(\"ampproject\") < -1 || props.dangerouslySetInnerHTML && (!props.type || props.type === \"text/javascript\")) {\n                        badProp = \"<script\";\n                        Object.keys(props).forEach((prop)=>{\n                            badProp += ` ${prop}=\"${props[prop]}\"`;\n                        });\n                        badProp += \"/>\";\n                    }\n                }\n                if (badProp) {\n                    console.warn(`Found conflicting amp tag \"${child.type}\" with conflicting prop ${badProp} in ${__NEXT_DATA__.page}. https://nextjs.org/docs/messages/conflicting-amp-tag`);\n                    return null;\n                }\n            } else {\n                // non-amp mode\n                if (type === \"link\" && props.rel === \"amphtml\") {\n                    hasAmphtmlRel = true;\n                }\n            }\n            return child;\n        });\n        const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page,  true && inAmpMode);\n        var _nonce, _nonce1;\n        return /*#__PURE__*/ _react.default.createElement(\"head\", Object.assign({}, getHeadHTMLProps(this.props)), this.context.isDevelopment && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, /*#__PURE__*/ _react.default.createElement(\"style\", {\n            \"data-next-hide-fouc\": true,\n            \"data-ampdevmode\":  true && inAmpMode ? \"true\" : undefined,\n            dangerouslySetInnerHTML: {\n                __html: `body{display:none}`\n            }\n        }), /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            \"data-next-hide-fouc\": true,\n            \"data-ampdevmode\":  true && inAmpMode ? \"true\" : undefined\n        }, /*#__PURE__*/ _react.default.createElement(\"style\", {\n            dangerouslySetInnerHTML: {\n                __html: `body{display:block}`\n            }\n        }))), head, /*#__PURE__*/ _react.default.createElement(\"meta\", {\n            name: \"next-head-count\",\n            content: _react.default.Children.count(head || []).toString()\n        }), children, optimizeFonts && /*#__PURE__*/ _react.default.createElement(\"meta\", {\n            name: \"next-font-preconnect\"\n        }),  true && inAmpMode && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, /*#__PURE__*/ _react.default.createElement(\"meta\", {\n            name: \"viewport\",\n            content: \"width=device-width,minimum-scale=1,initial-scale=1\"\n        }), !hasCanonicalRel && /*#__PURE__*/ _react.default.createElement(\"link\", {\n            rel: \"canonical\",\n            href: canonicalBase + (__webpack_require__(/*! ../server/utils */ \"../server/utils\").cleanAmpPath)(dangerousAsPath)\n        }), /*#__PURE__*/ _react.default.createElement(\"link\", {\n            rel: \"preload\",\n            as: \"script\",\n            href: \"https://cdn.ampproject.org/v0.js\"\n        }), /*#__PURE__*/ _react.default.createElement(AmpStyles, {\n            styles: styles\n        }), /*#__PURE__*/ _react.default.createElement(\"style\", {\n            \"amp-boilerplate\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: `body{-webkit-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-moz-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-ms-animation:-amp-start 8s steps(1,end) 0s 1 normal both;animation:-amp-start 8s steps(1,end) 0s 1 normal both}@-webkit-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-moz-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-ms-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-o-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}`\n            }\n        }), /*#__PURE__*/ _react.default.createElement(\"noscript\", null, /*#__PURE__*/ _react.default.createElement(\"style\", {\n            \"amp-boilerplate\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: `body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}`\n            }\n        })), /*#__PURE__*/ _react.default.createElement(\"script\", {\n            async: true,\n            src: \"https://cdn.ampproject.org/v0.js\"\n        })), !( true && inAmpMode) && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !hasAmphtmlRel && hybridAmp && /*#__PURE__*/ _react.default.createElement(\"link\", {\n            rel: \"amphtml\",\n            href: canonicalBase + getAmpPath(ampPath, dangerousAsPath)\n        }), this.getBeforeInteractiveInlineScripts(), !optimizeCss && this.getCssLinks(files), !optimizeCss && /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            \"data-n-css\": (_nonce = this.props.nonce) != null ? _nonce : \"\"\n        }), !disableRuntimeJS && !disableJsPreload && this.getPreloadDynamicChunks(), !disableRuntimeJS && !disableJsPreload && this.getPreloadMainLinks(files), !disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(), !disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(), !disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files), !disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files), optimizeCss && this.getCssLinks(files), optimizeCss && /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            \"data-n-css\": (_nonce1 = this.props.nonce) != null ? _nonce1 : \"\"\n        }), this.context.isDevelopment && // this element is used to mount development styles so the\n        // ordering matches production\n        // (by default, style-loader injects at the bottom of <head />)\n        /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            id: \"__next_css__DO_NOT_USE__\"\n        }), styles || null), /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, {}, ...headTags || []));\n    }\n}\nexports.Head = Head;\nfunction handleDocumentScriptLoaderItems(scriptLoader, __NEXT_DATA__, props) {\n    var ref10, ref7, ref8, ref9;\n    if (!props.children) return;\n    const scriptLoaderItems = [];\n    const children = Array.isArray(props.children) ? props.children : [\n        props.children\n    ];\n    const headChildren = (ref10 = children.find((child)=>child.type === Head)) == null ? void 0 : (ref7 = ref10.props) == null ? void 0 : ref7.children;\n    const bodyChildren = (ref8 = children.find((child)=>child.type === \"body\")) == null ? void 0 : (ref9 = ref8.props) == null ? void 0 : ref9.children;\n    // Scripts with beforeInteractive can be placed inside Head or <body> so children of both needs to be traversed\n    const combinedChildren = [\n        ...Array.isArray(headChildren) ? headChildren : [\n            headChildren\n        ],\n        ...Array.isArray(bodyChildren) ? bodyChildren : [\n            bodyChildren\n        ], \n    ];\n    _react.default.Children.forEach(combinedChildren, (child)=>{\n        var ref;\n        if (!child) return;\n        // When using the `next/script` component, register it in script loader.\n        if ((ref = child.type) == null ? void 0 : ref.__nextScript) {\n            if (child.props.strategy === \"beforeInteractive\") {\n                scriptLoader.beforeInteractive = (scriptLoader.beforeInteractive || []).concat([\n                    {\n                        ...child.props\n                    }, \n                ]);\n                return;\n            } else if ([\n                \"lazyOnload\",\n                \"afterInteractive\",\n                \"worker\"\n            ].includes(child.props.strategy)) {\n                scriptLoaderItems.push(child.props);\n                return;\n            }\n        }\n    });\n    __NEXT_DATA__.scriptLoader = scriptLoaderItems;\n}\nclass NextScript extends _react.default.Component {\n    static contextType = _htmlContext.HtmlContext;\n    getDynamicChunks(files) {\n        return getDynamicChunks(this.context, this.props, files);\n    }\n    getPreNextScripts() {\n        return getPreNextScripts(this.context, this.props);\n    }\n    getScripts(files) {\n        return getScripts(this.context, this.props, files);\n    }\n    getPolyfillScripts() {\n        return getPolyfillScripts(this.context, this.props);\n    }\n    static getInlineScriptSource(context) {\n        const { __NEXT_DATA__ , largePageDataBytes  } = context;\n        try {\n            const data = JSON.stringify(__NEXT_DATA__);\n            const bytes =  false ? 0 : Buffer.from(data).byteLength;\n            const prettyBytes = (__webpack_require__(/*! ../lib/pretty-bytes */ \"./node_modules/next/dist/lib/pretty-bytes.js\")[\"default\"]);\n            if (largePageDataBytes && bytes > largePageDataBytes) {\n                console.warn(`Warning: data for page \"${__NEXT_DATA__.page}\"${__NEXT_DATA__.page === context.dangerousAsPath ? \"\" : ` (path \"${context.dangerousAsPath}\")`} is ${prettyBytes(bytes)} which exceeds the threshold of ${prettyBytes(largePageDataBytes)}, this amount of data can reduce performance.\\nSee more info here: https://nextjs.org/docs/messages/large-page-data`);\n            }\n            return (0, _htmlescape).htmlEscapeJsonString(data);\n        } catch (err) {\n            if ((0, _isError).default(err) && err.message.indexOf(\"circular structure\") !== -1) {\n                throw new Error(`Circular structure in \"getInitialProps\" result of page \"${__NEXT_DATA__.page}\". https://nextjs.org/docs/messages/circular-structure`);\n            }\n            throw err;\n        }\n    }\n    render() {\n        const { assetPrefix , inAmpMode , buildManifest , unstable_runtimeJS , docComponentsRendered , devOnlyCacheBusterQueryString , disableOptimizedLoading , crossOrigin ,  } = this.context;\n        const disableRuntimeJS = unstable_runtimeJS === false;\n        docComponentsRendered.NextScript = true;\n        if ( true && inAmpMode) {\n            if (false) {}\n            const ampDevFiles = [\n                ...buildManifest.devFiles,\n                ...buildManifest.polyfillFiles,\n                ...buildManifest.ampDevFiles, \n            ];\n            return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, disableRuntimeJS ? null : /*#__PURE__*/ _react.default.createElement(\"script\", {\n                id: \"__NEXT_DATA__\",\n                type: \"application/json\",\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin,\n                dangerouslySetInnerHTML: {\n                    __html: NextScript.getInlineScriptSource(this.context)\n                },\n                \"data-ampdevmode\": true\n            }), ampDevFiles.map((file)=>/*#__PURE__*/ _react.default.createElement(\"script\", {\n                    key: file,\n                    src: `${assetPrefix}/_next/${file}${devOnlyCacheBusterQueryString}`,\n                    nonce: this.props.nonce,\n                    crossOrigin: this.props.crossOrigin || crossOrigin,\n                    \"data-ampdevmode\": true\n                })));\n        }\n        if (true) {\n            if (this.props.crossOrigin) console.warn(\"Warning: `NextScript` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated\");\n        }\n        const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page,  true && inAmpMode);\n        return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !disableRuntimeJS && buildManifest.devFiles ? buildManifest.devFiles.map((file)=>/*#__PURE__*/ _react.default.createElement(\"script\", {\n                key: file,\n                src: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin\n            })) : null, disableRuntimeJS ? null : /*#__PURE__*/ _react.default.createElement(\"script\", {\n            id: \"__NEXT_DATA__\",\n            type: \"application/json\",\n            nonce: this.props.nonce,\n            crossOrigin: this.props.crossOrigin || crossOrigin,\n            dangerouslySetInnerHTML: {\n                __html: NextScript.getInlineScriptSource(this.context)\n            }\n        }), disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(), disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(), disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files), disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files));\n    }\n}\nexports.NextScript = NextScript;\nfunction Html(props) {\n    const { inAmpMode , docComponentsRendered , locale , scriptLoader , __NEXT_DATA__ ,  } = (0, _react).useContext(_htmlContext.HtmlContext);\n    docComponentsRendered.Html = true;\n    handleDocumentScriptLoaderItems(scriptLoader, __NEXT_DATA__, props);\n    return /*#__PURE__*/ _react.default.createElement(\"html\", Object.assign({}, props, {\n        lang: props.lang || locale || undefined,\n        amp:  true && inAmpMode ? \"\" : undefined,\n        \"data-ampdevmode\":  true && inAmpMode && \"development\" !== \"production\" ? \"\" : undefined\n    }));\n}\nfunction Main() {\n    const { docComponentsRendered  } = (0, _react).useContext(_htmlContext.HtmlContext);\n    docComponentsRendered.Main = true;\n    // @ts-ignore\n    return /*#__PURE__*/ _react.default.createElement(\"next-js-internal-body-render-target\", null);\n}\n// Add a special property to the built-in `Document` component so later we can\n// identify if a user customized `Document` is used or not.\nconst InternalFunctionDocument = function InternalFunctionDocument() {\n    return /*#__PURE__*/ _react.default.createElement(Html, null, /*#__PURE__*/ _react.default.createElement(Head, null), /*#__PURE__*/ _react.default.createElement(\"body\", null, /*#__PURE__*/ _react.default.createElement(Main, null), /*#__PURE__*/ _react.default.createElement(NextScript, null)));\n};\nDocument[_constants.NEXT_BUILTIN_DOCUMENT] = InternalFunctionDocument; //# sourceMappingURL=_document.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/pages/_document.js\n");

/***/ }),

/***/ "./pages/_document.tsx":
/*!*****************************!*\
  !*** ./pages/_document.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _material_ui_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @material-ui/core */ \"@material-ui/core\");\n/* harmony import */ var _material_ui_core__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_material_ui_core__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/document */ \"./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nclass MyDocument extends (next_document__WEBPACK_IMPORTED_MODULE_2___default()) {\n    render() {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Html, {\n            className: \"nightwind\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Head, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"application-name\",\n                            content: \"Notea\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\_document.tsx\",\n                            lineNumber: 10,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"apple-mobile-web-app-capable\",\n                            content: \"yes\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\_document.tsx\",\n                            lineNumber: 11,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"apple-mobile-web-app-status-bar-style\",\n                            content: \"default\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\_document.tsx\",\n                            lineNumber: 12,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"apple-mobile-web-app-title\",\n                            content: \"Notea\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\_document.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: \"Self hosted note taking app stored on S3.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\_document.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"format-detection\",\n                            content: \"telephone=no\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\_document.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"mobile-web-app-capable\",\n                            content: \"yes\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\_document.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"msapplication-TileColor\",\n                            content: \"#2B5797\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\_document.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"msapplication-tap-highlight\",\n                            content: \"no\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\_document.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"theme-color\",\n                            content: \"#ffffff\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\_document.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"apple-touch-icon\",\n                            sizes: \"192x192\",\n                            href: \"/static/icons/icon-192x192.png\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\_document.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            type: \"image/png\",\n                            sizes: \"128x128\",\n                            href: \"/static/icons/icon-128x128.png\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\_document.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"manifest\",\n                            href: \"/static/manifest.json\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\_document.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\_document.tsx\",\n                    lineNumber: 9,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                    className: \"bg-gray-50 text-gray-800 overflow-x-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Main, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\_document.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.NextScript, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\_document.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\_document.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\_document.tsx\",\n            lineNumber: 8,\n            columnNumber: 13\n        }, this);\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyDocument);\n// `getInitialProps` belongs to `_document` (instead of `_app`),\n// it's compatible with server-side generation (SSG).\nMyDocument.getInitialProps = async (ctx)=>{\n    // Resolution order\n    //\n    // On the server:\n    // 1. app.getInitialProps\n    // 2. page.getInitialProps\n    // 3. document.getInitialProps\n    // 4. app.render\n    // 5. page.render\n    // 6. document.render\n    //\n    // On the server with error:\n    // 1. document.getInitialProps\n    // 2. app.render\n    // 3. page.render\n    // 4. document.render\n    //\n    // On the client\n    // 1. app.getInitialProps\n    // 2. page.getInitialProps\n    // 3. app.render\n    // 4. page.render\n    // Render app and page and get the context of the page with collected side effects.\n    const sheets = new _material_ui_core__WEBPACK_IMPORTED_MODULE_1__.ServerStyleSheets();\n    const originalRenderPage = ctx.renderPage;\n    ctx.renderPage = ()=>originalRenderPage({\n            enhanceApp: (App)=>(props)=>sheets.collect(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(App, {\n                        ...props\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\_document.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 60\n                    }, undefined))\n        });\n    const initialProps = await next_document__WEBPACK_IMPORTED_MODULE_2___default().getInitialProps(ctx);\n    return {\n        ...initialProps,\n        // Styles fragment is rendered after the app and page rendering finish.\n        styles: [\n            ...react__WEBPACK_IMPORTED_MODULE_3__.Children.toArray(initialProps.styles),\n            sheets.getStyleElement(), \n        ]\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_document.tsx\n");

/***/ }),

/***/ "./node_modules/next/dist/lib/is-error.js":
/*!************************************************!*\
  !*** ./node_modules/next/dist/lib/is-error.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = isError;\nexports.getProperError = getProperError;\nvar _isPlainObject = __webpack_require__(/*! ../shared/lib/is-plain-object */ \"../shared/lib/is-plain-object\");\nfunction isError(err) {\n    return typeof err === \"object\" && err !== null && \"name\" in err && \"message\" in err;\n}\nfunction getProperError(err) {\n    if (isError(err)) {\n        return err;\n    }\n    if (true) {\n        // provide better error for case where `throw undefined`\n        // is called in development\n        if (typeof err === \"undefined\") {\n            return new Error(\"An undefined error was thrown, \" + \"see here for more info: https://nextjs.org/docs/messages/threw-undefined\");\n        }\n        if (err === null) {\n            return new Error(\"A null error was thrown, \" + \"see here for more info: https://nextjs.org/docs/messages/threw-undefined\");\n        }\n    }\n    return new Error((0, _isPlainObject).isPlainObject(err) ? JSON.stringify(err) : err + \"\");\n}\n\n//# sourceMappingURL=is-error.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2xpYi9pcy1lcnJvci5qcy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QztBQUM3QztBQUNBLENBQUMsRUFBQztBQUNGLGtCQUFlO0FBQ2Ysc0JBQXNCO0FBQ3RCLHFCQUFxQixtQkFBTyxDQUFDLG9FQUErQjtBQUM1RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsSUFBc0M7QUFDOUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL25vdGVhLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9saWIvaXMtZXJyb3IuanM/MTc4ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwge1xuICAgIHZhbHVlOiB0cnVlXG59KTtcbmV4cG9ydHMuZGVmYXVsdCA9IGlzRXJyb3I7XG5leHBvcnRzLmdldFByb3BlckVycm9yID0gZ2V0UHJvcGVyRXJyb3I7XG52YXIgX2lzUGxhaW5PYmplY3QgPSByZXF1aXJlKFwiLi4vc2hhcmVkL2xpYi9pcy1wbGFpbi1vYmplY3RcIik7XG5mdW5jdGlvbiBpc0Vycm9yKGVycikge1xuICAgIHJldHVybiB0eXBlb2YgZXJyID09PSBcIm9iamVjdFwiICYmIGVyciAhPT0gbnVsbCAmJiBcIm5hbWVcIiBpbiBlcnIgJiYgXCJtZXNzYWdlXCIgaW4gZXJyO1xufVxuZnVuY3Rpb24gZ2V0UHJvcGVyRXJyb3IoZXJyKSB7XG4gICAgaWYgKGlzRXJyb3IoZXJyKSkge1xuICAgICAgICByZXR1cm4gZXJyO1xuICAgIH1cbiAgICBpZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09IFwiZGV2ZWxvcG1lbnRcIikge1xuICAgICAgICAvLyBwcm92aWRlIGJldHRlciBlcnJvciBmb3IgY2FzZSB3aGVyZSBgdGhyb3cgdW5kZWZpbmVkYFxuICAgICAgICAvLyBpcyBjYWxsZWQgaW4gZGV2ZWxvcG1lbnRcbiAgICAgICAgaWYgKHR5cGVvZiBlcnIgPT09IFwidW5kZWZpbmVkXCIpIHtcbiAgICAgICAgICAgIHJldHVybiBuZXcgRXJyb3IoXCJBbiB1bmRlZmluZWQgZXJyb3Igd2FzIHRocm93biwgXCIgKyBcInNlZSBoZXJlIGZvciBtb3JlIGluZm86IGh0dHBzOi8vbmV4dGpzLm9yZy9kb2NzL21lc3NhZ2VzL3RocmV3LXVuZGVmaW5lZFwiKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoZXJyID09PSBudWxsKSB7XG4gICAgICAgICAgICByZXR1cm4gbmV3IEVycm9yKFwiQSBudWxsIGVycm9yIHdhcyB0aHJvd24sIFwiICsgXCJzZWUgaGVyZSBmb3IgbW9yZSBpbmZvOiBodHRwczovL25leHRqcy5vcmcvZG9jcy9tZXNzYWdlcy90aHJldy11bmRlZmluZWRcIik7XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIG5ldyBFcnJvcigoMCwgX2lzUGxhaW5PYmplY3QpLmlzUGxhaW5PYmplY3QoZXJyKSA/IEpTT04uc3RyaW5naWZ5KGVycikgOiBlcnIgKyBcIlwiKTtcbn1cblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aXMtZXJyb3IuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/lib/is-error.js\n");

/***/ }),

/***/ "./node_modules/next/dist/lib/pretty-bytes.js":
/*!****************************************************!*\
  !*** ./node_modules/next/dist/lib/pretty-bytes.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = prettyBytes;\nfunction prettyBytes(number, options) {\n    if (!Number.isFinite(number)) {\n        throw new TypeError(`Expected a finite number, got ${typeof number}: ${number}`);\n    }\n    options = Object.assign({}, options);\n    if (options.signed && number === 0) {\n        return \" 0 B\";\n    }\n    const isNegative = number < 0;\n    const prefix = isNegative ? \"-\" : options.signed ? \"+\" : \"\";\n    if (isNegative) {\n        number = -number;\n    }\n    if (number < 1) {\n        const numberString = toLocaleString(number, options.locale);\n        return prefix + numberString + \" B\";\n    }\n    const exponent = Math.min(Math.floor(Math.log10(number) / 3), UNITS.length - 1);\n    number = Number((number / Math.pow(1000, exponent)).toPrecision(3));\n    const numberString = toLocaleString(number, options.locale);\n    const unit = UNITS[exponent];\n    return prefix + numberString + \" \" + unit;\n}\n/*\nMIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/ const UNITS = [\n    \"B\",\n    \"kB\",\n    \"MB\",\n    \"GB\",\n    \"TB\",\n    \"PB\",\n    \"EB\",\n    \"ZB\",\n    \"YB\"\n];\n/*\nFormats the given number using `Number#toLocaleString`.\n- If locale is a string, the value is expected to be a locale-key (for example: `de`).\n- If locale is true, the system default locale is used for translation.\n- If no value for locale is specified, the number is returned unmodified.\n*/ const toLocaleString = (number, locale)=>{\n    let result = number;\n    if (typeof locale === \"string\") {\n        result = number.toLocaleString(locale);\n    } else if (locale === true) {\n        result = number.toLocaleString();\n    }\n    return result;\n};\n\n//# sourceMappingURL=pretty-bytes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///./node_modules/next/dist/lib/pretty-bytes.js\n");

/***/ }),

/***/ "./node_modules/next/document.js":
/*!***************************************!*\
  !*** ./node_modules/next/document.js ***!
  \***************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = __webpack_require__(/*! ./dist/pages/_document */ \"./node_modules/next/dist/pages/_document.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kb2N1bWVudC5qcy5qcyIsIm1hcHBpbmdzIjoiQUFBQSxpSEFBa0QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ub3RlYS8uL25vZGVfbW9kdWxlcy9uZXh0L2RvY3VtZW50LmpzPzlhMTQiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3QvcGFnZXMvX2RvY3VtZW50JylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/document.js\n");

/***/ }),

/***/ "@material-ui/core":
/*!************************************!*\
  !*** external "@material-ui/core" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@material-ui/core");

/***/ }),

/***/ "../server/get-page-files":
/*!*****************************************************!*\
  !*** external "next/dist/server/get-page-files.js" ***!
  \*****************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/get-page-files.js");

/***/ }),

/***/ "../server/htmlescape":
/*!*************************************************!*\
  !*** external "next/dist/server/htmlescape.js" ***!
  \*************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/htmlescape.js");

/***/ }),

/***/ "../server/utils":
/*!********************************************!*\
  !*** external "next/dist/server/utils.js" ***!
  \********************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/utils.js");

/***/ }),

/***/ "../shared/lib/constants":
/*!****************************************************!*\
  !*** external "next/dist/shared/lib/constants.js" ***!
  \****************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/constants.js");

/***/ }),

/***/ "../shared/lib/html-context":
/*!*******************************************************!*\
  !*** external "next/dist/shared/lib/html-context.js" ***!
  \*******************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/html-context.js");

/***/ }),

/***/ "../shared/lib/is-plain-object":
/*!**********************************************************!*\
  !*** external "next/dist/shared/lib/is-plain-object.js" ***!
  \**********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/is-plain-object.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./pages/_document.tsx"));
module.exports = __webpack_exports__;

})();