{"version": 3, "names": ["transformFile", "filename", "opts", "callback", "Error", "transformFileSync", "transformFileAsync", "Promise", "reject"], "sources": ["../src/transform-file-browser.ts"], "sourcesContent": ["// duplicated from transform-file so we do not have to import anything here\ntype TransformFile = {\n  (filename: string, callback: (error: Error, file: null) => void): void;\n  (\n    filename: string,\n    opts: any,\n    callback: (error: Error, file: null) => void,\n  ): void;\n};\n\nexport const transformFile: TransformFile = function transformFile(\n  filename,\n  opts,\n  callback?: (error: Error, file: null) => void,\n) {\n  if (typeof opts === \"function\") {\n    callback = opts;\n  }\n\n  callback(new Error(\"Transforming files is not supported in browsers\"), null);\n};\n\nexport function transformFileSync(): never {\n  throw new Error(\"Transforming files is not supported in browsers\");\n}\n\nexport function transformFileAsync() {\n  return Promise.reject(\n    new Error(\"Transforming files is not supported in browsers\"),\n  );\n}\n"], "mappings": ";;;;;;;;;AAUO,MAAMA,aAA4B,GAAG,SAASA,aAAT,CAC1CC,QAD0C,EAE1CC,IAF0C,EAG1CC,QAH0C,EAI1C;EACA,IAAI,OAAOD,IAAP,KAAgB,UAApB,EAAgC;IAC9BC,QAAQ,GAAGD,IAAX;EACD;;EAEDC,QAAQ,CAAC,IAAIC,KAAJ,CAAU,iDAAV,CAAD,EAA+D,IAA/D,CAAR;AACD,CAVM;;;;AAYA,SAASC,iBAAT,GAAoC;EACzC,MAAM,IAAID,KAAJ,CAAU,iDAAV,CAAN;AACD;;AAEM,SAASE,kBAAT,GAA8B;EACnC,OAAOC,OAAO,CAACC,MAAR,CACL,IAAIJ,KAAJ,CAAU,iDAAV,CADK,CAAP;AAGD"}