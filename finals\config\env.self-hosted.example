# motea Environment Configuration - Self-hosted PostgreSQL
# Based on the open-source project Notea, originally created by qingwei-li<<EMAIL>>
# Modified and maintained by waycaan, 2025.

# ===========================================
# Application Configuration
# ===========================================
NODE_ENV=production
PORT=3000

# ===========================================
# Database Configuration - Self-hosted PostgreSQL
# ===========================================
# Use this configuration for your own PostgreSQL server
# Suitable for local development, VPS, or dedicated servers

# Primary database connection (REQUIRED)
# Choose one of the following based on your setup:

# Local development (Docker Compose)
DATABASE_URL=********************************************/motea

# Local development (direct connection)
# DATABASE_URL=postgresql://postgres:password@localhost:5432/motea

# Remote server without SSL
# DATABASE_URL=postgresql://username:<EMAIL>:5432/motea

# Remote server with SSL
# DATABASE_URL=postgresql://username:<EMAIL>:5432/motea?sslmode=require

# Explicit provider setting (OPTIONAL - auto-detected)
DB_PROVIDER=self-hosted

# Database table prefix (OPTIONAL)
# STORE_PREFIX=motea_

# ===========================================
# Self-hosted Configuration Tips
# ===========================================
# 1. Ensure PostgreSQL version 12 or higher
# 2. Create a dedicated database and user for motea
# 3. Configure appropriate connection limits
# 4. Set up regular backups
# 5. Consider connection pooling for production

# ===========================================
# Authentication Configuration
# ===========================================
# Option 1: Set a password for the application
PASSWORD=your_secure_password_here

# Option 2: Disable password authentication (uncomment below)
# DISABLE_PASSWORD=true

# ===========================================
# Performance Configuration
# ===========================================
# Number of notes to preload (recommended: 5-15 for self-hosted)
PRELOAD_NOTES_COUNT=10

# Logging level
LOG_LEVEL=info

# ===========================================
# Session Configuration
# ===========================================
# Session secret for secure cookies
SESSION_SECRET=your_session_secret_here

# Use secure cookies in production (set to false for local development)
COOKIE_SECURE=false

# ===========================================
# Optional Configuration
# ===========================================
# Base URL for the application
# BASE_URL=https://your-domain.com

# Disable Next.js telemetry
NEXT_TELEMETRY_DISABLED=1

# ===========================================
# PostgreSQL Setup Instructions
# ===========================================
# 1. Install PostgreSQL 12+ on your server
# 2. Create a database for motea:
#    CREATE DATABASE motea;
# 3. Create a user for motea:
#    CREATE USER motea_user WITH PASSWORD 'secure_password';
# 4. Grant permissions:
#    GRANT ALL PRIVILEGES ON DATABASE motea TO motea_user;
# 5. Update the DATABASE_URL above with your credentials

# ===========================================
# Docker Compose Setup
# ===========================================
# If using the provided docker-compose.yml:
# 1. The PostgreSQL service is automatically configured
# 2. Use: ********************************************/motea
# 3. Data is persisted in a Docker volume
# 4. Access via localhost:5432 from host machine

# ===========================================
# Connection String Examples
# ===========================================
# Local development:
# postgresql://postgres:password@localhost:5432/motea

# Docker Compose:
# ********************************************/motea

# Remote server:
# *********************************************************

# With SSL:
# postgresql://motea_user:<EMAIL>:5432/motea?sslmode=require

# With connection pooling:
# postgresql://motea_user:<EMAIL>:5432/motea?pool_max_conns=10

# ===========================================
# Security Considerations
# ===========================================
# 1. Use strong passwords for database users
# 2. Limit database access to specific IP addresses
# 3. Enable SSL for remote connections
# 4. Regular security updates for PostgreSQL
# 5. Monitor database access logs
# 6. Set up proper firewall rules

# ===========================================
# Backup and Maintenance
# ===========================================
# 1. Set up automated backups using pg_dump
# 2. Monitor disk space and database size
# 3. Regular VACUUM and ANALYZE operations
# 4. Monitor connection counts and performance
# 5. Keep PostgreSQL updated to latest stable version

# ===========================================
# Performance Tuning
# ===========================================
# For production deployments, consider tuning:
# - shared_buffers (25% of RAM)
# - effective_cache_size (75% of RAM)
# - work_mem (based on concurrent connections)
# - max_connections (based on expected load)
# - checkpoint_completion_target (0.9)
