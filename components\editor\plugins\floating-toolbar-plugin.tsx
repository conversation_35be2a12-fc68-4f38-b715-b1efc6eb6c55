/**
 * Floating Toolbar Plugin for Lexical
 * Shows formatting options when text is selected
 */

import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { $getSelection, $isRangeSelection, FORMAT_TEXT_COMMAND, TextFormatType, INDENT_CONTENT_COMMAND, OUTDENT_CONTENT_COMMAND } from 'lexical';
import { $isLinkNode, TOGGLE_LINK_COMMAND } from '@lexical/link';

import { INSERT_ORDERED_LIST_COMMAND, INSERT_UNORDERED_LIST_COMMAND, $isListItemNode, $isListNode, $createListNode, ListItemNode } from '@lexical/list';
import { INSERT_CHECK_LIST_COMMAND } from '@lexical/list';
import { TOGGLE_HIGHLIGHT_COMMAND } from './highlight-plugin';

import { useCallback, useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { useTheme } from 'next-themes';

// Heroicons
import {
    LinkIcon,
    CodeIcon,
    ArrowRightIcon,
    ArrowLeftIcon
} from '@heroicons/react/outline';



export default function FloatingToolbarPlugin(): JSX.Element | null {
    const [editor] = useLexicalComposerContext();
    const [isVisible, setIsVisible] = useState(false);
    const [position, setPosition] = useState({ top: 0, left: 0 });
    const [isBold, setIsBold] = useState(false);
    const [isUnderline, setIsUnderline] = useState(false);
    const [isStrikethrough, setIsStrikethrough] = useState(false);
    const [isCode, setIsCode] = useState(false);
    const [isLink, setIsLink] = useState(false);
    const [isHighlight, setIsHighlight] = useState(false);
    const { theme } = useTheme();

    const updateToolbar = useCallback(() => {
        const selection = $getSelection();
        
        if ($isRangeSelection(selection)) {
            const anchorNode = selection.anchor.getNode();
            let element =
                anchorNode.getKey() === 'root'
                    ? anchorNode
                    : anchorNode.getTopLevelElementOrThrow();
            const elementKey = element.getKey();
            const elementDOM = editor.getElementByKey(elementKey);

            // 显示条件：只有当有选中文本时才显示
            const hasSelection = selection.getTextContent() !== '';

            if (elementDOM !== null && hasSelection) {
                const nativeSelection = window.getSelection();
                const rootElement = editor.getRootElement();

                if (
                    nativeSelection !== null &&
                    rootElement !== null &&
                    rootElement.contains(nativeSelection.anchorNode)
                ) {
                    const rangeRect = nativeSelection.getRangeAt(0).getBoundingClientRect();

                    setPosition({
                        top: rangeRect.top - 60,
                        left: rangeRect.left + rangeRect.width / 2 - 150,
                    });
                    setIsVisible(true);

                    // Update button states
                    setIsBold(selection.hasFormat('bold'));
                    setIsUnderline(selection.hasFormat('underline'));
                    setIsStrikethrough(selection.hasFormat('strikethrough'));
                    setIsCode(selection.hasFormat('code'));

                    // Check if selection contains a link
                    const node = selection.anchor.getNode();
                    const parent = node.getParent();
                    setIsLink($isLinkNode(parent) || $isLinkNode(node));

                    // Check for highlight using Lexical's built-in format
                    setIsHighlight(selection.hasFormat('highlight'));
                } else {
                    setIsVisible(false);
                }
            } else {
                setIsVisible(false);
            }
        } else {
            setIsVisible(false);
        }
    }, [editor]);

    useEffect(() => {
        return editor.registerUpdateListener(({ editorState }: any) => {
            editorState.read(() => {
                updateToolbar();
            });
        });
    }, [editor, updateToolbar]);

    const handleFormat = (format: TextFormatType) => {
        editor.dispatchCommand(FORMAT_TEXT_COMMAND, format);
    };

    const handleLink = () => {
        if (isLink) {
            editor.dispatchCommand(TOGGLE_LINK_COMMAND, null);
        } else {
            const url = prompt('Enter URL:');
            if (url) {
                editor.dispatchCommand(TOGGLE_LINK_COMMAND, url);
            }
        }
    };

    if (!isVisible) {
        return null;
    }

    const toolbarBg = theme === 'dark'
        ? 'border-gray-600'
        : 'border-gray-200';
    
    const buttonText = theme === 'dark' ? 'text-white' : 'text-gray-700';
    const buttonHover = theme === 'dark'
        ? 'hover:text-white'
        : 'hover:text-gray-900';
    
    const buttonActive = theme === 'dark'
        ? 'text-white'
        : 'text-gray-900';

    const toolbarButtons = [
        {
            title: 'Bold',
            icon: <span className="font-bold text-sm">B</span>,
            isActive: isBold,
            action: () => handleFormat('bold'),
        },
        {
            title: 'Strikethrough',
            icon: <span className="line-through text-sm">S</span>,
            isActive: isStrikethrough,
            action: () => handleFormat('strikethrough'),
        },
        {
            title: 'Underline',
            icon: <span className="underline text-sm">U</span>,
            isActive: isUnderline,
            action: () => handleFormat('underline'),
        },
        {
            title: 'Highlight',
            icon: theme === 'dark'
                ? <span className="text-xs px-1 rounded text-white" style={{backgroundColor: '#3185eb'}}>H</span>
                : <span className="text-xs px-1 rounded" style={{backgroundColor: '#eab834'}}>H</span>,
            isActive: isHighlight,
            action: () => editor.dispatchCommand(TOGGLE_HIGHLIGHT_COMMAND, undefined),
        },
        {
            title: 'Code',
            icon: <CodeIcon className="w-4 h-4" />,
            isActive: isCode,
            action: () => handleFormat('code'),
        },
        {
            title: isLink ? 'Remove Link' : 'Add Link',
            icon: <LinkIcon className="w-4 h-4" />,
            isActive: isLink,
            action: handleLink,
        },
        // 分隔符
        { type: 'separator' },
        {
            title: 'Checklist',
            icon: <span className="text-sm">☑</span>,
            isActive: false,
            action: () => editor.dispatchCommand(INSERT_CHECK_LIST_COMMAND, undefined),
        },
        {
            title: 'Bullet List',
            icon: <span className="text-sm">•</span>,
            isActive: false,
            action: () => editor.dispatchCommand(INSERT_UNORDERED_LIST_COMMAND, undefined),
        },
        {
            title: 'Numbered List',
            icon: <span className="text-sm">1.</span>,
            isActive: false,
            action: () => editor.dispatchCommand(INSERT_ORDERED_LIST_COMMAND, undefined),
        },
        // 分隔符
        { type: 'separator' },
        {
            title: 'Indent',
            icon: <ArrowRightIcon className="w-4 h-4" />,
            isActive: false,
            action: () => {
                editor.update(() => {
                    const selection = $getSelection();
                    if (!$isRangeSelection(selection)) {
                        return;
                    }

                    const nodes = selection.getNodes();
                    const listItemsToIndent: ListItemNode[] = [];

                    // 收集所有需要缩进的列表项
                    nodes.forEach(node => {
                        let listItemNode = null;

                        if ($isListItemNode(node)) {
                            listItemNode = node;
                        } else if ($isListItemNode(node.getParent())) {
                            listItemNode = node.getParent();
                        }

                        if (listItemNode && $isListItemNode(listItemNode) && !listItemsToIndent.includes(listItemNode)) {
                            listItemsToIndent.push(listItemNode);
                        }
                    });

                    if (listItemsToIndent.length > 0) {
                        // 按文档顺序排序列表项
                        listItemsToIndent.sort((a, b) => {
                            const aIndex = a.getIndexWithinParent();
                            const bIndex = b.getIndexWithinParent();
                            return aIndex - bIndex;
                        });

                        // 处理每个列表项的缩进
                        listItemsToIndent.forEach(listItemNode => {
                            // 找到前一个兄弟节点作为父系
                            const previousSibling = listItemNode.getPreviousSibling();

                            if (previousSibling && $isListItemNode(previousSibling)) {
                                // 检查前一个兄弟是否已经有子列表
                                let nestedList = null;
                                const children = previousSibling.getChildren();

                                // 查找是否已经有子列表
                                for (const child of children) {
                                    if ($isListNode(child)) {
                                        nestedList = child;
                                        break;
                                    }
                                }

                                // 如果没有子列表，创建一个新的无序子列表
                                if (!nestedList) {
                                    nestedList = $createListNode('bullet');
                                    previousSibling.append(nestedList);
                                }

                                // 将当前列表项移动到子列表中
                                listItemNode.remove();
                                nestedList.append(listItemNode);
                            }
                        });
                    } else {
                        // 如果没有列表项，使用通用缩进
                        editor.dispatchCommand(INDENT_CONTENT_COMMAND, undefined);
                    }
                });
            },
        },
        {
            title: 'Outdent',
            icon: <ArrowLeftIcon className="w-4 h-4" />,
            isActive: false,
            action: () => {
                // 暂时使用简单的反缩进逻辑，确保列表符号正常显示
                editor.dispatchCommand(OUTDENT_CONTENT_COMMAND, undefined);
            },
        },

    ];

    return createPortal(
        <div
            className={`fixed z-50 ${toolbarBg} border rounded-lg p-1.5 flex space-x-0.5 shadow-lg`}
            style={{
                top: position.top,
                left: position.left,
                transform: 'translateX(-50%)',
                backgroundColor: theme === 'dark' ? '#3f3f46' : '#e4e4e7',
            }}
        >
            {toolbarButtons.map((button, index) => {
                if (button.type === 'separator') {
                    return (
                        <div
                            key={index}
                            className={`w-px h-6 ${theme === 'dark' ? 'bg-gray-600' : 'bg-gray-300'} mx-1`}
                        />
                    );
                }

                return (
                    <button
                        key={index}
                        onClick={button.action}
                        title={button.title}
                        className={`
                            px-2.5 py-1.5 rounded transition-colors duration-150 min-w-[30px] h-7 flex items-center justify-center text-sm font-medium
                            ${button.isActive
                                ? buttonActive
                                : `${buttonText} ${buttonHover}`
                            }
                        `}
                        style={{
                            backgroundColor: button.isActive
                                ? (theme === 'dark' ? '#3185eb' : '#eab834')
                                : 'transparent'
                        }}
                        onMouseEnter={(e) => {
                            if (!button.isActive) {
                                e.currentTarget.style.backgroundColor = theme === 'dark' ? '#3185eb' : '#eab834';
                                if (theme === 'dark') {
                                    e.currentTarget.style.color = 'white';
                                }
                            }
                        }}
                        onMouseLeave={(e) => {
                            if (!button.isActive) {
                                e.currentTarget.style.backgroundColor = 'transparent';
                                e.currentTarget.style.color = '';
                            }
                        }}
                    >
                        {button.icon}
                    </button>
                );
            })}
        </div>,
        document.body
    );
}
