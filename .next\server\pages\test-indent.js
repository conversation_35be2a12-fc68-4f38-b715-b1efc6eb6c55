"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/test-indent";
exports.ids = ["pages/test-indent"];
exports.modules = {

/***/ "./components/editor/lexical-editor.tsx":
/*!**********************************************!*\
  !*** ./components/editor/lexical-editor.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"styled-jsx/style\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lexical */ \"lexical\");\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lexical__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lexical_react_LexicalComposer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @lexical/react/LexicalComposer */ \"@lexical/react/LexicalComposer\");\n/* harmony import */ var _lexical_react_LexicalComposer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_lexical_react_LexicalComposer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"@lexical/react/LexicalComposerContext\");\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _lexical_react_LexicalRichTextPlugin__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @lexical/react/LexicalRichTextPlugin */ \"@lexical/react/LexicalRichTextPlugin\");\n/* harmony import */ var _lexical_react_LexicalRichTextPlugin__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_lexical_react_LexicalRichTextPlugin__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _lexical_react_LexicalContentEditable__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @lexical/react/LexicalContentEditable */ \"@lexical/react/LexicalContentEditable\");\n/* harmony import */ var _lexical_react_LexicalContentEditable__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_lexical_react_LexicalContentEditable__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _lexical_react_LexicalHistoryPlugin__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @lexical/react/LexicalHistoryPlugin */ \"@lexical/react/LexicalHistoryPlugin\");\n/* harmony import */ var _lexical_react_LexicalHistoryPlugin__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_lexical_react_LexicalHistoryPlugin__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _lexical_react_LexicalAutoFocusPlugin__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @lexical/react/LexicalAutoFocusPlugin */ \"@lexical/react/LexicalAutoFocusPlugin\");\n/* harmony import */ var _lexical_react_LexicalAutoFocusPlugin__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_lexical_react_LexicalAutoFocusPlugin__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _lexical_react_LexicalOnChangePlugin__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @lexical/react/LexicalOnChangePlugin */ \"@lexical/react/LexicalOnChangePlugin\");\n/* harmony import */ var _lexical_react_LexicalOnChangePlugin__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(_lexical_react_LexicalOnChangePlugin__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var _lexical_react_LexicalLinkPlugin__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @lexical/react/LexicalLinkPlugin */ \"@lexical/react/LexicalLinkPlugin\");\n/* harmony import */ var _lexical_react_LexicalLinkPlugin__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(_lexical_react_LexicalLinkPlugin__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _lexical_react_LexicalListPlugin__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @lexical/react/LexicalListPlugin */ \"@lexical/react/LexicalListPlugin\");\n/* harmony import */ var _lexical_react_LexicalListPlugin__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(_lexical_react_LexicalListPlugin__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var _lexical_react_LexicalMarkdownShortcutPlugin__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @lexical/react/LexicalMarkdownShortcutPlugin */ \"@lexical/react/LexicalMarkdownShortcutPlugin\");\n/* harmony import */ var _lexical_react_LexicalMarkdownShortcutPlugin__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(_lexical_react_LexicalMarkdownShortcutPlugin__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var _lexical_markdown__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @lexical/markdown */ \"@lexical/markdown\");\n/* harmony import */ var _lexical_markdown__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(_lexical_markdown__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var _lexical_react_LexicalErrorBoundary__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @lexical/react/LexicalErrorBoundary */ \"@lexical/react/LexicalErrorBoundary\");\n/* harmony import */ var _lexical_react_LexicalErrorBoundary__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(_lexical_react_LexicalErrorBoundary__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var _lexical_rich_text__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @lexical/rich-text */ \"@lexical/rich-text\");\n/* harmony import */ var _lexical_rich_text__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(_lexical_rich_text__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var _lexical_list__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @lexical/list */ \"@lexical/list\");\n/* harmony import */ var _lexical_list__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(_lexical_list__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var _lexical_code__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @lexical/code */ \"@lexical/code\");\n/* harmony import */ var _lexical_code__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(_lexical_code__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var _lexical_link__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @lexical/link */ \"@lexical/link\");\n/* harmony import */ var _lexical_link__WEBPACK_IMPORTED_MODULE_19___default = /*#__PURE__*/__webpack_require__.n(_lexical_link__WEBPACK_IMPORTED_MODULE_19__);\n/* harmony import */ var react_div_100vh__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! react-div-100vh */ \"react-div-100vh\");\n/* harmony import */ var react_div_100vh__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(react_div_100vh__WEBPACK_IMPORTED_MODULE_20__);\n/* harmony import */ var libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! libs/web/hooks/use-mounted */ \"./libs/web/hooks/use-mounted.ts\");\n/* harmony import */ var libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! libs/web/hooks/use-i18n */ \"./libs/web/hooks/use-i18n.tsx\");\n/* harmony import */ var _plugins_slash_commands_plugin__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./plugins/slash-commands-plugin */ \"./components/editor/plugins/slash-commands-plugin.tsx\");\n/* harmony import */ var _plugins_floating_toolbar_plugin__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./plugins/floating-toolbar-plugin */ \"./components/editor/plugins/floating-toolbar-plugin.tsx\");\n/* harmony import */ var _lexical_react_LexicalCheckListPlugin__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @lexical/react/LexicalCheckListPlugin */ \"@lexical/react/LexicalCheckListPlugin\");\n/* harmony import */ var _lexical_react_LexicalCheckListPlugin__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(_lexical_react_LexicalCheckListPlugin__WEBPACK_IMPORTED_MODULE_25__);\n/* harmony import */ var _plugins_highlight_plugin__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./plugins/highlight-plugin */ \"./components/editor/plugins/highlight-plugin.tsx\");\n/* harmony import */ var _lexical_react_LexicalTabIndentationPlugin__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @lexical/react/LexicalTabIndentationPlugin */ \"@lexical/react/LexicalTabIndentationPlugin\");\n/* harmony import */ var _lexical_react_LexicalTabIndentationPlugin__WEBPACK_IMPORTED_MODULE_27___default = /*#__PURE__*/__webpack_require__.n(_lexical_react_LexicalTabIndentationPlugin__WEBPACK_IMPORTED_MODULE_27__);\n/* harmony import */ var _plugins_image_plugin__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! ./plugins/image-plugin */ \"./components/editor/plugins/image-plugin.tsx\");\n/* harmony import */ var _plugins_ime_plugin__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! ./plugins/ime-plugin */ \"./components/editor/plugins/ime-plugin.tsx\");\n/* harmony import */ var _nodes_image_node__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! ./nodes/image-node */ \"./components/editor/nodes/image-node.tsx\");\n/* harmony import */ var _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @lexical/react/LexicalHorizontalRuleNode */ \"@lexical/react/LexicalHorizontalRuleNode\");\n/* harmony import */ var _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_31___default = /*#__PURE__*/__webpack_require__.n(_lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_31__);\n/* harmony import */ var _lexical_react_LexicalHorizontalRulePlugin__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @lexical/react/LexicalHorizontalRulePlugin */ \"@lexical/react/LexicalHorizontalRulePlugin\");\n/* harmony import */ var _lexical_react_LexicalHorizontalRulePlugin__WEBPACK_IMPORTED_MODULE_32___default = /*#__PURE__*/__webpack_require__.n(_lexical_react_LexicalHorizontalRulePlugin__WEBPACK_IMPORTED_MODULE_32__);\n/**\n * Lexical Editor Component\n * Migrated from TipTap to Lexical for better performance and modern architecture\n */ \n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Import custom plugins and nodes\n\n\n\n\n\n\n\n\n\n\nconst theme = {\n    ltr: \"ltr\",\n    rtl: \"rtl\",\n    placeholder: \"editor-placeholder\",\n    paragraph: \"editor-paragraph\",\n    quote: \"editor-quote\",\n    heading: {\n        h1: \"editor-heading-h1\",\n        h2: \"editor-heading-h2\",\n        h3: \"editor-heading-h3\",\n        h4: \"editor-heading-h4\",\n        h5: \"editor-heading-h5\",\n        h6: \"editor-heading-h6\"\n    },\n    list: {\n        nested: {\n            listitem: \"editor-nested-listitem\"\n        },\n        ol: \"editor-list-ol\",\n        ul: \"editor-list-ul\",\n        listitem: \"editor-listitem\",\n        checklist: \"PlaygroundEditorTheme__checklist\",\n        listitemChecked: \"PlaygroundEditorTheme__listItemChecked\",\n        listitemUnchecked: \"PlaygroundEditorTheme__listItemUnchecked\"\n    },\n    // Lexical 0.32.1 原生缩进支持\n    indent: \"lexical-indent\",\n    image: \"editor-image\",\n    link: \"editor-link\",\n    text: {\n        bold: \"editor-text-bold\",\n        italic: \"editor-text-italic\",\n        overflowed: \"editor-text-overflowed\",\n        hashtag: \"editor-text-hashtag\",\n        underline: \"editor-text-underline\",\n        strikethrough: \"editor-text-strikethrough\",\n        underlineStrikethrough: \"editor-text-underlineStrikethrough\",\n        code: \"editor-text-code\",\n        highlight: \"editor-text-highlight\"\n    },\n    code: \"editor-code\",\n    codeHighlight: {\n        atrule: \"editor-tokenAttr\",\n        attr: \"editor-tokenAttr\",\n        boolean: \"editor-tokenProperty\",\n        builtin: \"editor-tokenSelector\",\n        cdata: \"editor-tokenComment\",\n        char: \"editor-tokenSelector\",\n        class: \"editor-tokenFunction\",\n        \"class-name\": \"editor-tokenFunction\",\n        comment: \"editor-tokenComment\",\n        constant: \"editor-tokenProperty\",\n        deleted: \"editor-tokenProperty\",\n        doctype: \"editor-tokenComment\",\n        entity: \"editor-tokenOperator\",\n        function: \"editor-tokenFunction\",\n        important: \"editor-tokenVariable\",\n        inserted: \"editor-tokenSelector\",\n        keyword: \"editor-tokenAttr\",\n        namespace: \"editor-tokenVariable\",\n        number: \"editor-tokenProperty\",\n        operator: \"editor-tokenOperator\",\n        prolog: \"editor-tokenComment\",\n        property: \"editor-tokenProperty\",\n        punctuation: \"editor-tokenPunctuation\",\n        regex: \"editor-tokenVariable\",\n        selector: \"editor-tokenSelector\",\n        string: \"editor-tokenSelector\",\n        symbol: \"editor-tokenProperty\",\n        tag: \"editor-tokenProperty\",\n        url: \"editor-tokenOperator\",\n        variable: \"editor-tokenVariable\"\n    }\n};\nfunction Placeholder() {\n    const { t  } = (0,libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_22__[\"default\"])();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"editor-placeholder\",\n        children: t(\"Start writing...\")\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n        lineNumber: 138,\n        columnNumber: 12\n    }, this);\n}\nconst LexicalEditor = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(({ readOnly =false , value =\"\" , onChange , onClickLink , onHoverLink , className =\"\" ,  }, ref)=>{\n    const height = (0,react_div_100vh__WEBPACK_IMPORTED_MODULE_20__.use100vh)();\n    const mounted = (0,libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_21__[\"default\"])();\n    const initialConfig = {\n        namespace: \"LexicalEditor\",\n        theme,\n        onError (error) {\n            console.error(\"Lexical Error:\", error);\n        },\n        nodes: [\n            _lexical_rich_text__WEBPACK_IMPORTED_MODULE_16__.HeadingNode,\n            _lexical_list__WEBPACK_IMPORTED_MODULE_17__.ListNode,\n            _lexical_list__WEBPACK_IMPORTED_MODULE_17__.ListItemNode,\n            _lexical_rich_text__WEBPACK_IMPORTED_MODULE_16__.QuoteNode,\n            _lexical_code__WEBPACK_IMPORTED_MODULE_18__.CodeNode,\n            _lexical_code__WEBPACK_IMPORTED_MODULE_18__.CodeHighlightNode,\n            _lexical_link__WEBPACK_IMPORTED_MODULE_19__.AutoLinkNode,\n            _lexical_link__WEBPACK_IMPORTED_MODULE_19__.LinkNode,\n            // Lexical原生的ListItemNode已经支持checkbox功能\n            // 使用Lexical内置的highlight格式，不需要自定义HighlightNode\n            _nodes_image_node__WEBPACK_IMPORTED_MODULE_30__.ImageNode,\n            _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_31__.HorizontalRuleNode, \n        ],\n        editable: !readOnly,\n        // 设置初始内容\n        editorState: value ? undefined : null\n    };\n    // 创建自定义transformers，包含图片支持\n    const IMAGE_TRANSFORMER = {\n        dependencies: [\n            _nodes_image_node__WEBPACK_IMPORTED_MODULE_30__.ImageNode\n        ],\n        export: (node)=>{\n            if (!(0,_nodes_image_node__WEBPACK_IMPORTED_MODULE_30__.$isImageNode)(node)) {\n                return null;\n            }\n            return `![${node.getAltText()}](${node.getSrc()})`;\n        },\n        regExp: /!\\[([^\\]]*)\\]\\(([^)]+)\\)/,\n        replace: (parentNode, children, match)=>{\n            const [, altText, src] = match;\n            const imageNode = (0,_nodes_image_node__WEBPACK_IMPORTED_MODULE_30__.$createImageNode)({\n                altText,\n                src,\n                maxWidth: 800\n            });\n            children.forEach((child)=>child.remove());\n            parentNode.append(imageNode);\n        },\n        type: \"element\"\n    };\n    // 创建自定义的下划线转换器，使用 <u>text</u> 语法\n    const UNDERLINE_TRANSFORMER = {\n        format: [\n            \"underline\"\n        ],\n        tag: \"<u>\",\n        type: \"text-format\"\n    };\n    // 暂时移除段落缩进转换器，因为它与 Lexical 的内部状态管理冲突\n    // 段落缩进功能在编辑器中正常工作，但 markdown 序列化不支持\n    // 如果需要保存缩进，建议使用 HTML 格式或 JSON 格式\n    // 创建水平分割线转换器\n    const HR_TRANSFORMER = {\n        dependencies: [\n            _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_31__.HorizontalRuleNode\n        ],\n        export: (node)=>{\n            return (0,_lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_31__.$isHorizontalRuleNode)(node) ? \"---\" : null;\n        },\n        regExp: /^(---|\\*\\*\\*|___)\\s?$/,\n        replace: (parentNode, children, match, isImport)=>{\n            const line = (0,_lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_31__.$createHorizontalRuleNode)();\n            if (isImport || parentNode.getNextSibling() != null) {\n                parentNode.replace(line);\n            } else {\n                parentNode.insertBefore(line);\n            }\n            line.selectNext();\n        },\n        type: \"element\"\n    };\n    // 重新排序transformers，确保CHECK_LIST优先级高于UNORDERED_LIST\n    const customTransformers = [\n        // 首先是CHECK_LIST，确保checkbox优先匹配\n        _lexical_markdown__WEBPACK_IMPORTED_MODULE_14__.CHECK_LIST,\n        // 然后是其他TRANSFORMERS（但要排除重复的CHECK_LIST）\n        ..._lexical_markdown__WEBPACK_IMPORTED_MODULE_14__.TRANSFORMERS.filter((t)=>t !== _lexical_markdown__WEBPACK_IMPORTED_MODULE_14__.CHECK_LIST),\n        // 最后是自定义的转换器\n        HR_TRANSFORMER,\n        UNDERLINE_TRANSFORMER,\n        IMAGE_TRANSFORMER\n    ];\n    // 移除了自动完成插件 - 现在通过正确的transformer优先级来解决问题\n    const handleChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((editorState, _editor, tags)=>{\n        if (onChange) {\n            // 模仿TipTap的简单检查：忽略历史合并和内容同步触发的更新\n            if (tags.has(\"history-merge\") || tags.has(\"content-sync\")) {\n                return;\n            }\n            editorState.read(()=>{\n                try {\n                    // 使用Lexical的官方transformers进行markdown转换\n                    const markdownContent = (0,_lexical_markdown__WEBPACK_IMPORTED_MODULE_14__.$convertToMarkdownString)(customTransformers);\n                    // 调试：检查是否包含checkbox语法\n                    if (markdownContent.includes(\"[ ]\") || markdownContent.includes(\"[x]\")) {\n                        console.log(\"\\uD83D\\uDD0D Checkbox detected in markdown\");\n                    }\n                    // 不做任何额外处理，保持Lexical原生的markdown输出\n                    // Lexical的transformers已经正确处理了列表、checkbox等格式\n                    // 简单的内容变化检查\n                    if (markdownContent !== value) {\n                        console.log(\"\\uD83D\\uDD0D Content changed, calling onChange\");\n                        onChange(()=>markdownContent);\n                    }\n                } catch (error) {\n                    console.error(\"\\uD83D\\uDD0D Error in markdown conversion:\", error);\n                // 如果转换出错，保持原有内容不变\n                }\n            });\n        }\n    }, [\n        onChange,\n        value\n    ]);\n    // 列表退出处理插件 - 处理Enter+Enter退出列表的逻辑\n    const ListExitPlugin = ()=>{\n        const [editor] = (0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_5__.useLexicalComposerContext)();\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n            return editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_3__.KEY_ENTER_COMMAND, (event)=>{\n                const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_3__.$getSelection)();\n                if (!(0,lexical__WEBPACK_IMPORTED_MODULE_3__.$isRangeSelection)(selection)) {\n                    return false;\n                }\n                const anchorNode = selection.anchor.getNode();\n                // 检查是否在空的列表项中\n                if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_17__.$isListItemNode)(anchorNode)) {\n                    const textContent = anchorNode.getTextContent().trim();\n                    if (textContent === \"\") {\n                        const listNode = anchorNode.getParent();\n                        if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_17__.$isListNode)(listNode)) {\n                            // 如果是空的列表项，退出列表\n                            event?.preventDefault();\n                            // 创建新段落并在列表后插入\n                            const paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_3__.$createParagraphNode)();\n                            listNode.insertAfter(paragraph);\n                            // 删除空的列表项\n                            anchorNode.remove();\n                            // 选中新段落\n                            paragraph.select();\n                            return true;\n                        }\n                    }\n                }\n                return false;\n            }, lexical__WEBPACK_IMPORTED_MODULE_3__.COMMAND_PRIORITY_HIGH);\n        }, [\n            editor\n        ]);\n        return null;\n    };\n    // 内容同步组件 - 模仿TipTap的方式\n    const ContentSyncPlugin = ()=>{\n        const [editor] = (0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_5__.useLexicalComposerContext)();\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n            if (editor && value !== undefined && mounted) {\n                editor.getEditorState().read(()=>{\n                    const root = (0,lexical__WEBPACK_IMPORTED_MODULE_3__.$getRoot)();\n                    const currentContent = root.getTextContent();\n                    // 只有当内容真的不同时才更新\n                    if (value !== currentContent) {\n                        editor.update(()=>{\n                            // 使用Lexical官方的markdown解析器来正确渲染markdown内容\n                            if (value.trim()) {\n                                // 不要清理双换行符！保持原始markdown格式\n                                // 双换行符在markdown中有重要意义（段落分隔、列表退出等）\n                                // 使用自定义的transformers解析内容\n                                (0,_lexical_markdown__WEBPACK_IMPORTED_MODULE_14__.$convertFromMarkdownString)(value, customTransformers);\n                            } else {\n                                // 空内容时清空并创建一个空段落\n                                const root = (0,lexical__WEBPACK_IMPORTED_MODULE_3__.$getRoot)();\n                                root.clear();\n                                const paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_3__.$createParagraphNode)();\n                                root.append(paragraph);\n                            }\n                        }, {\n                            tag: \"content-sync\"\n                        });\n                    }\n                });\n            }\n        }, [\n            editor,\n            value,\n            mounted\n        ]);\n        return null;\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useImperativeHandle)(ref, ()=>({\n            focusAtEnd: ()=>{\n            // TODO: Implement focus at end\n            },\n            focusAtStart: ()=>{\n            // TODO: Implement focus at start\n            }\n        }));\n    if (!mounted) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n            [\n                \"ea89f497a3ec74e8\",\n                [\n                    height ? height + \"px\" : \"100vh\"\n                ]\n            ]\n        ]) + \" \" + `lexical-editor ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalComposer__WEBPACK_IMPORTED_MODULE_4__.LexicalComposer, {\n                initialConfig: initialConfig,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                        [\n                            \"ea89f497a3ec74e8\",\n                            [\n                                height ? height + \"px\" : \"100vh\"\n                            ]\n                        ]\n                    ]) + \" \" + \"editor-container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalRichTextPlugin__WEBPACK_IMPORTED_MODULE_6__.RichTextPlugin, {\n                            contentEditable: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalContentEditable__WEBPACK_IMPORTED_MODULE_7__.ContentEditable, {\n                                className: \"editor-input focus:outline-none w-full\",\n                                spellCheck: false\n                            }, void 0, false, void 0, void 0),\n                            placeholder: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Placeholder, {}, void 0, false, void 0, void 0),\n                            ErrorBoundary: _lexical_react_LexicalErrorBoundary__WEBPACK_IMPORTED_MODULE_15__.LexicalErrorBoundary\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 380,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalHistoryPlugin__WEBPACK_IMPORTED_MODULE_8__.HistoryPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 390,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalAutoFocusPlugin__WEBPACK_IMPORTED_MODULE_9__.AutoFocusPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 391,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalLinkPlugin__WEBPACK_IMPORTED_MODULE_11__.LinkPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalListPlugin__WEBPACK_IMPORTED_MODULE_12__.ListPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 393,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalMarkdownShortcutPlugin__WEBPACK_IMPORTED_MODULE_13__.MarkdownShortcutPlugin, {\n                            transformers: customTransformers\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 394,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_slash_commands_plugin__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 395,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_floating_toolbar_plugin__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalCheckListPlugin__WEBPACK_IMPORTED_MODULE_25__.CheckListPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 397,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_highlight_plugin__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 398,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalTabIndentationPlugin__WEBPACK_IMPORTED_MODULE_27__.TabIndentationPlugin, {\n                            maxIndent: 10\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 399,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_image_plugin__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 400,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalHorizontalRulePlugin__WEBPACK_IMPORTED_MODULE_32__.HorizontalRulePlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 401,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_ime_plugin__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                            enabled: true,\n                            debug: \"development\" === \"development\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 402,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ListExitPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"ea89f497a3ec74e8\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentSyncPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"ea89f497a3ec74e8\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalOnChangePlugin__WEBPACK_IMPORTED_MODULE_10__.OnChangePlugin, {\n                            onChange: handleChange,\n                            ignoreHistoryMergeTagChange: true,\n                            ignoreSelectionChange: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 408,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                    lineNumber: 379,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                lineNumber: 378,\n                columnNumber: 13\n            }, undefined),\n            (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"ea89f497a3ec74e8\",\n                dynamic: [\n                    height ? height + \"px\" : \"100vh\"\n                ],\n                children: `.lexical-editor{position:relative}.editor-container{position:relative}.editor-input{outline:none;padding:1rem 0;min-height:-webkit-calc(${height ? height + \"px\" : \"100vh\"} - 14rem);min-height:-moz-calc(${height ? height + \"px\" : \"100vh\"} - 14rem);min-height:calc(${height ? height + \"px\" : \"100vh\"} - 14rem);padding-bottom:10rem;width:100%;max-width:none;line-height:1.7;font-size:1rem;color:inherit;-webkit-spellcheck:false;-moz-spellcheck:false;-ms-spellcheck:false;spellcheck:false}.lexical-editor{--lexical-indent-base-value:32px}.lexical-indent{--lexical-indent-base-value:32px}.editor-input p[style*=\"margin-left\"],.editor-input h1[style*=\"margin-left\"],.editor-input h2[style*=\"margin-left\"],.editor-input h3[style*=\"margin-left\"],.editor-input h4[style*=\"margin-left\"],.editor-input h5[style*=\"margin-left\"],.editor-input h6[style*=\"margin-left\"],.editor-input li[style*=\"margin-left\"]{-webkit-transition:margin-left.2s ease;-moz-transition:margin-left.2s ease;-o-transition:margin-left.2s ease;transition:margin-left.2s ease}.editor-placeholder{color:#999;overflow:hidden;position:absolute;-o-text-overflow:ellipsis;text-overflow:ellipsis;top:1rem;left:0;font-size:1rem;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;display:inline-block;pointer-events:none}.editor-paragraph{margin:1rem 0;line-height:1.7}.editor-heading-h1{font-size:2.8em;font-weight:bold;margin:1.5rem 0 1rem 0;line-height:1.2}.editor-heading-h2{font-size:2.2em;font-weight:bold;margin:1.4rem 0 .8rem 0;line-height:1.3}.editor-heading-h3{font-size:1.8em;font-weight:bold;margin:1.3rem 0 .6rem 0;line-height:1.4}.editor-heading-h4{font-size:1.5em;font-weight:bold;margin:1.2rem 0 .5rem 0;line-height:1.4}.editor-heading-h5{font-size:1.3em;font-weight:bold;margin:1.1rem 0 .4rem 0;line-height:1.5}.editor-heading-h6{font-size:1.2em;font-weight:bold;margin:1rem 0 .3rem 0;line-height:1.5}.editor-quote{margin:1rem 0;padding-left:1rem;border-left:4px solid#ccc;font-style:italic;color:#666}.editor-list-ol,.editor-list-ul{margin:1rem 0;padding-left:2rem}.editor-listitem{margin:.5rem 0}.editor-link{color:#3b82f6;text-decoration:underline;cursor:pointer}.editor-link:hover{color:#1d4ed8}.editor-text-bold{font-weight:bold}.editor-text-italic{font-style:italic}.editor-text-underline{text-decoration:underline}.editor-text-strikethrough{text-decoration:line-through}.editor-text-code{background-color:#e4e4e7;color:black;padding:.2rem .4rem;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;font-family:monospace;font-size:.9em}.editor-code{background-color:#e4e4e7;color:black;border:1px solid#e9ecef;-webkit-border-radius:.375rem;-moz-border-radius:.375rem;border-radius:.375rem;padding:1rem;margin:1rem 0;font-family:\"Courier New\",Courier,monospace;font-size:.9em;line-height:1.4;overflow-x:auto}[data-theme=\"dark\"] .editor-text-code{background-color:#3f3f46;color:white}[data-theme=\"dark\"] .editor-code{background-color:#3f3f46;color:white;border-color:#4b5563}.editor-container ul{list-style-type:disc;padding-left:1.5rem;margin:.5rem 0}.editor-container ol{list-style-type:decimal;padding-left:1.5rem;margin:.5rem 0}.editor-container li{margin:.25rem 0;line-height:1.6;padding-left:.25rem}.editor-container ul ul{list-style:none;margin:.25rem 0;padding-left:1.5rem;counter-reset:nested-list}.editor-container ul ul li{counter-increment:nested-list;position:relative;margin-left:0}.editor-container ul ul li::before{content:counter(nested-list,upper-alpha)\". \";position:absolute;left:-1.5rem;font-weight:normal;color:inherit}.editor-container ul ul ul{list-style:none;margin:.25rem 0;padding-left:1.5rem;counter-reset:nested-list-3}.editor-container ul ul ul li{counter-increment:nested-list-3}.editor-container ul ul ul li::before{content:counter(nested-list-3,lower-roman)\". \";left:-1.5rem}.editor-container ol ol{list-style:none;margin:.25rem 0;padding-left:1.5rem;counter-reset:nested-list}.editor-container ol ol li{counter-increment:nested-list;position:relative;margin-left:0}.editor-container ol ol li::before{content:counter(nested-list,upper-alpha)\". \";position:absolute;left:-1.5rem;font-weight:normal;color:inherit}.editor-container ol ol ol{list-style:none;margin:.25rem 0;padding-left:1.5rem;counter-reset:nested-list-3}.editor-container ol ol ol li{counter-increment:nested-list-3}.editor-container ol ol ol li::before{content:counter(nested-list-3,lower-roman)\". \";left:-1.5rem}.task-list{list-style:none;padding-left:0;margin:1rem 0}.task-item{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:start;-webkit-align-items:flex-start;-moz-box-align:start;-ms-flex-align:start;align-items:flex-start;margin:.5rem 0;list-style:none}.task-checkbox{margin-right:.5rem;margin-top:.125rem;cursor:pointer;width:1rem;height:1rem;border:1px solid#d1d5db;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;background:white;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;-moz-appearance:none;-ms-appearance:none;appearance:none;-webkit-appearance:none}.task-checkbox:checked{background-color:#3b82f6;border-color:#3b82f6;color:white}.task-checkbox:checked::after{content:\"✓\";font-size:.75rem;font-weight:bold;color:white}.task-content{-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1;line-height:1.7}.task-item[data-checked=\"true\"] .task-content{text-decoration:line-through;opacity:.6}.lexical-highlight,mark,.editor-text-highlight{background-color:#eab834!important;color:black!important;padding:.1rem .2rem;-webkit-border-radius:.125rem;-moz-border-radius:.125rem;border-radius:.125rem}.dark .lexical-highlight,.dark mark,.dark .editor-text-highlight,[data-theme=\"dark\"] .lexical-highlight,[data-theme=\"dark\"] mark,[data-theme=\"dark\"] .editor-text-highlight,html.dark .lexical-highlight,html.dark mark,html.dark .editor-text-highlight{background-color:#3185eb!important;color:white!important}.editor-image img{max-width:100%;height:auto;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;margin:1rem 0}@media(prefers-color-scheme:dark){.editor-placeholder{color:#6b7280}.editor-quote{border-left-color:#4b5563;color:#9ca3af}.editor-text-code{background-color:#374151;color:#f9fafb}.editor-code{background-color:#1f2937;border-color:#374151;color:#f9fafb}}.lexical-editor ul:not([data-lexical-list-type=\"check\"]) li::marker{color:#6b7280}.lexical-editor ol li::marker{color:#6b7280}.lexical-editor .PlaygroundEditorTheme__listItemChecked,.lexical-editor .PlaygroundEditorTheme__listItemUnchecked{position:relative;margin-left:.5em;margin-right:.5em;padding-left:1.5em;padding-right:1.5em;list-style-type:none;outline:none;display:block;min-height:1.5em}.lexical-editor .PlaygroundEditorTheme__listItemChecked>*,.lexical-editor .PlaygroundEditorTheme__listItemUnchecked>*{margin-left:.01em}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before,.lexical-editor .PlaygroundEditorTheme__listItemChecked:before{content:\"\";width:.9em;height:.9em;top:50%;left:0;cursor:pointer;display:block;-webkit-background-size:cover;-moz-background-size:cover;-o-background-size:cover;background-size:cover;position:absolute;-webkit-transform:translateY(-50%);-moz-transform:translateY(-50%);-ms-transform:translateY(-50%);-o-transform:translateY(-50%);transform:translateY(-50%)}.lexical-editor .PlaygroundEditorTheme__listItemChecked{text-decoration:line-through}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:focus:before,.lexical-editor .PlaygroundEditorTheme__listItemChecked:focus:before{-webkit-box-shadow:0 0 0 2px#a6cdfe;-moz-box-shadow:0 0 0 2px#a6cdfe;box-shadow:0 0 0 2px#a6cdfe;-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before{border:1px solid#999;-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px}.lexical-editor .PlaygroundEditorTheme__listItemChecked:before{border:1px solid rgb(61,135,245);-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px;background-color:#3d87f5;background-repeat:no-repeat}.lexical-editor .PlaygroundEditorTheme__listItemChecked:after{content:\"\";cursor:pointer;border-color:#fff;border-style:solid;position:absolute;display:block;top:45%;width:.2em;left:.35em;height:.4em;-webkit-transform:translateY(-50%)rotate(45deg);-moz-transform:translateY(-50%)rotate(45deg);-ms-transform:translateY(-50%)rotate(45deg);-o-transform:translateY(-50%)rotate(45deg);transform:translateY(-50%)rotate(45deg);border-width:0 .1em .1em 0}`\n            }, void 0, false, void 0, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n        lineNumber: 377,\n        columnNumber: 9\n    }, undefined);\n});\nLexicalEditor.displayName = \"LexicalEditor\";\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LexicalEditor);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/editor/lexical-editor.tsx\n");

/***/ }),

/***/ "./components/editor/nodes/image-node.tsx":
/*!************************************************!*\
  !*** ./components/editor/nodes/image-node.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"$createImageNode\": () => (/* binding */ $createImageNode),\n/* harmony export */   \"$isImageNode\": () => (/* binding */ $isImageNode),\n/* harmony export */   \"ImageNode\": () => (/* binding */ ImageNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lexical */ \"lexical\");\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lexical__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/**\n * Image Node for Lexical\n * Supports Markdown syntax ![alt](url) and direct image insertion\n */ \n\n\nfunction ImageComponent({ src , altText , width , height , maxWidth  }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n        src: src,\n        alt: altText,\n        style: {\n            height,\n            maxWidth: maxWidth || \"100%\",\n            width\n        },\n        className: \"max-w-full h-auto rounded-lg my-4\",\n        draggable: \"false\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\nodes\\\\image-node.tsx\",\n        lineNumber: 54,\n        columnNumber: 9\n    }, this);\n}\nclass ImageNode extends lexical__WEBPACK_IMPORTED_MODULE_1__.DecoratorNode {\n    static getType() {\n        return \"image\";\n    }\n    static clone(node) {\n        return new ImageNode(node.__src, node.__altText, node.__maxWidth, node.__width, node.__height, node.__key);\n    }\n    static importJSON(serializedNode) {\n        const { altText , height , width , maxWidth , src  } = serializedNode;\n        const node = $createImageNode({\n            altText,\n            height,\n            maxWidth,\n            src,\n            width\n        });\n        return node;\n    }\n    exportJSON() {\n        return {\n            altText: this.getAltText(),\n            height: this.__height,\n            maxWidth: this.__maxWidth,\n            src: this.getSrc(),\n            type: \"image\",\n            version: 1,\n            width: this.__width\n        };\n    }\n    constructor(src, altText, maxWidth, width, height, key){\n        super(key);\n        this.__src = src;\n        this.__altText = altText;\n        this.__maxWidth = maxWidth;\n        this.__width = width;\n        this.__height = height;\n    }\n    exportDOM() {\n        const element = document.createElement(\"img\");\n        element.setAttribute(\"src\", this.__src);\n        element.setAttribute(\"alt\", this.__altText);\n        if (this.__width) {\n            element.setAttribute(\"width\", this.__width.toString());\n        }\n        if (this.__height) {\n            element.setAttribute(\"height\", this.__height.toString());\n        }\n        element.className = \"max-w-full h-auto rounded-lg my-4\";\n        return {\n            element\n        };\n    }\n    static importDOM() {\n        return {\n            img: (node)=>({\n                    conversion: convertImageElement,\n                    priority: 0\n                })\n        };\n    }\n    createDOM(config) {\n        const span = document.createElement(\"span\");\n        const theme = config.theme;\n        const className = theme.image;\n        if (className !== undefined) {\n            span.className = className;\n        }\n        return span;\n    }\n    updateDOM() {\n        return false;\n    }\n    getSrc() {\n        return this.__src;\n    }\n    getAltText() {\n        return this.__altText;\n    }\n    setAltText(altText) {\n        const writable = this.getWritable();\n        writable.__altText = altText;\n    }\n    setWidthAndHeight(width, height) {\n        const writable = this.getWritable();\n        writable.__width = width;\n        writable.__height = height;\n    }\n    decorate() {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n            fallback: null,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageComponent, {\n                src: this.__src,\n                altText: this.__altText,\n                width: this.__width,\n                height: this.__height,\n                maxWidth: this.__maxWidth\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\nodes\\\\image-node.tsx\",\n                lineNumber: 189,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\nodes\\\\image-node.tsx\",\n            lineNumber: 188,\n            columnNumber: 13\n        }, this);\n    }\n}\nfunction convertImageElement(domNode) {\n    if (domNode instanceof HTMLImageElement) {\n        const { alt: altText , src  } = domNode;\n        const node = $createImageNode({\n            altText,\n            src\n        });\n        return {\n            node\n        };\n    }\n    return null;\n}\nfunction $createImageNode({ altText , height , maxWidth =500 , src , width , key  }) {\n    return (0,lexical__WEBPACK_IMPORTED_MODULE_1__.$applyNodeReplacement)(new ImageNode(src, altText, maxWidth, width, height, key));\n}\nfunction $isImageNode(node) {\n    return node instanceof ImageNode;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/editor/nodes/image-node.tsx\n");

/***/ }),

/***/ "./components/editor/plugins/floating-toolbar-plugin.tsx":
/*!***************************************************************!*\
  !*** ./components/editor/plugins/floating-toolbar-plugin.tsx ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FloatingToolbarPlugin)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"@lexical/react/LexicalComposerContext\");\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lexical */ \"lexical\");\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lexical__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lexical_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @lexical/link */ \"@lexical/link\");\n/* harmony import */ var _lexical_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_lexical_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lexical_list__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @lexical/list */ \"@lexical/list\");\n/* harmony import */ var _lexical_list__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_lexical_list__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _highlight_plugin__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./highlight-plugin */ \"./components/editor/plugins/highlight-plugin.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-dom */ \"react-dom\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next-themes */ \"next-themes\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_themes__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var _heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @heroicons/react/outline */ \"@heroicons/react/outline\");\n/* harmony import */ var _heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__);\n/**\n * Floating Toolbar Plugin for Lexical\n * Shows formatting options when text is selected\n */ \n\n\n\n\n\n\n\n\n\n// Heroicons\n\nfunction FloatingToolbarPlugin() {\n    const [editor] = (0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_1__.useLexicalComposerContext)();\n    const { 0: isVisible , 1: setIsVisible  } = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false);\n    const { 0: position , 1: setPosition  } = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)({\n        top: 0,\n        left: 0\n    });\n    const { 0: isBold , 1: setIsBold  } = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false);\n    const { 0: isUnderline , 1: setIsUnderline  } = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false);\n    const { 0: isStrikethrough , 1: setIsStrikethrough  } = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false);\n    const { 0: isCode , 1: setIsCode  } = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false);\n    const { 0: isLink , 1: setIsLink  } = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false);\n    const { 0: isHighlight , 1: setIsHighlight  } = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(false);\n    const { theme  } = (0,next_themes__WEBPACK_IMPORTED_MODULE_8__.useTheme)();\n    const updateToolbar = (0,react__WEBPACK_IMPORTED_MODULE_6__.useCallback)(()=>{\n        const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_2__.$getSelection)();\n        if ((0,lexical__WEBPACK_IMPORTED_MODULE_2__.$isRangeSelection)(selection)) {\n            const anchorNode = selection.anchor.getNode();\n            let element = anchorNode.getKey() === \"root\" ? anchorNode : anchorNode.getTopLevelElementOrThrow();\n            const elementKey = element.getKey();\n            const elementDOM = editor.getElementByKey(elementKey);\n            // 显示条件：有选中文本 或者 光标在编辑器中（用于缩进功能）\n            const hasSelection = selection.getTextContent() !== \"\";\n            const hasFocus = selection.focus !== null;\n            if (elementDOM !== null && (hasSelection || hasFocus)) {\n                const nativeSelection = window.getSelection();\n                const rootElement = editor.getRootElement();\n                if (nativeSelection !== null && rootElement !== null && rootElement.contains(nativeSelection.anchorNode)) {\n                    const rangeRect = nativeSelection.getRangeAt(0).getBoundingClientRect();\n                    setPosition({\n                        top: rangeRect.top - 60,\n                        left: rangeRect.left + rangeRect.width / 2 - 150\n                    });\n                    setIsVisible(true);\n                    // Update button states\n                    setIsBold(selection.hasFormat(\"bold\"));\n                    setIsUnderline(selection.hasFormat(\"underline\"));\n                    setIsStrikethrough(selection.hasFormat(\"strikethrough\"));\n                    setIsCode(selection.hasFormat(\"code\"));\n                    // Check if selection contains a link\n                    const node = selection.anchor.getNode();\n                    const parent = node.getParent();\n                    setIsLink((0,_lexical_link__WEBPACK_IMPORTED_MODULE_3__.$isLinkNode)(parent) || (0,_lexical_link__WEBPACK_IMPORTED_MODULE_3__.$isLinkNode)(node));\n                    // Check for highlight using Lexical's built-in format\n                    setIsHighlight(selection.hasFormat(\"highlight\"));\n                } else {\n                    setIsVisible(false);\n                }\n            } else {\n                setIsVisible(false);\n            }\n        } else {\n            setIsVisible(false);\n        }\n    }, [\n        editor\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_6__.useEffect)(()=>{\n        return editor.registerUpdateListener(({ editorState  })=>{\n            editorState.read(()=>{\n                updateToolbar();\n            });\n        });\n    }, [\n        editor,\n        updateToolbar\n    ]);\n    const handleFormat = (format)=>{\n        editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_2__.FORMAT_TEXT_COMMAND, format);\n    };\n    const handleLink = ()=>{\n        if (isLink) {\n            editor.dispatchCommand(_lexical_link__WEBPACK_IMPORTED_MODULE_3__.TOGGLE_LINK_COMMAND, null);\n        } else {\n            const url = prompt(\"Enter URL:\");\n            if (url) {\n                editor.dispatchCommand(_lexical_link__WEBPACK_IMPORTED_MODULE_3__.TOGGLE_LINK_COMMAND, url);\n            }\n        }\n    };\n    if (!isVisible) {\n        return null;\n    }\n    const toolbarBg = theme === \"dark\" ? \"border-gray-600\" : \"border-gray-200\";\n    const buttonText = theme === \"dark\" ? \"text-white\" : \"text-gray-700\";\n    const buttonHover = theme === \"dark\" ? \"hover:text-white\" : \"hover:text-gray-900\";\n    const buttonActive = theme === \"dark\" ? \"text-white\" : \"text-gray-900\";\n    const toolbarButtons = [\n        {\n            title: \"Bold\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"font-bold text-sm\",\n                children: \"B\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 140,\n                columnNumber: 19\n            }, this),\n            isActive: isBold,\n            action: ()=>handleFormat(\"bold\")\n        },\n        {\n            title: \"Strikethrough\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"line-through text-sm\",\n                children: \"S\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 146,\n                columnNumber: 19\n            }, this),\n            isActive: isStrikethrough,\n            action: ()=>handleFormat(\"strikethrough\")\n        },\n        {\n            title: \"Underline\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"underline text-sm\",\n                children: \"U\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 152,\n                columnNumber: 19\n            }, this),\n            isActive: isUnderline,\n            action: ()=>handleFormat(\"underline\")\n        },\n        {\n            title: \"Highlight\",\n            icon: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-xs px-1 rounded text-white\",\n                style: {\n                    backgroundColor: \"#3185eb\"\n                },\n                children: \"H\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 159,\n                columnNumber: 19\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-xs px-1 rounded\",\n                style: {\n                    backgroundColor: \"#eab834\"\n                },\n                children: \"H\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 160,\n                columnNumber: 19\n            }, this),\n            isActive: isHighlight,\n            action: ()=>editor.dispatchCommand(_highlight_plugin__WEBPACK_IMPORTED_MODULE_5__.TOGGLE_HIGHLIGHT_COMMAND, undefined)\n        },\n        {\n            title: \"Code\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__.CodeIcon, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 166,\n                columnNumber: 19\n            }, this),\n            isActive: isCode,\n            action: ()=>handleFormat(\"code\")\n        },\n        {\n            title: isLink ? \"Remove Link\" : \"Add Link\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__.LinkIcon, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 172,\n                columnNumber: 19\n            }, this),\n            isActive: isLink,\n            action: handleLink\n        },\n        // 分隔符\n        {\n            type: \"separator\"\n        },\n        {\n            title: \"Checklist\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm\",\n                children: \"☑\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 180,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: ()=>editor.dispatchCommand(_lexical_list__WEBPACK_IMPORTED_MODULE_4__.INSERT_CHECK_LIST_COMMAND, undefined)\n        },\n        {\n            title: \"Bullet List\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm\",\n                children: \"•\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 186,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: ()=>editor.dispatchCommand(_lexical_list__WEBPACK_IMPORTED_MODULE_4__.INSERT_UNORDERED_LIST_COMMAND, undefined)\n        },\n        {\n            title: \"Numbered List\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm\",\n                children: \"1.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 192,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: ()=>editor.dispatchCommand(_lexical_list__WEBPACK_IMPORTED_MODULE_4__.INSERT_ORDERED_LIST_COMMAND, undefined)\n        },\n        // 分隔符\n        {\n            type: \"separator\"\n        },\n        {\n            title: \"Indent\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__.ArrowRightIcon, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 200,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: ()=>{\n                // 使用Lexical的内置缩进命令，避免直接操作节点\n                editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_2__.INDENT_CONTENT_COMMAND, undefined);\n            }\n        },\n        {\n            title: \"Outdent\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__.ArrowLeftIcon, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 209,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: ()=>{\n                // 使用Lexical的内置缩进命令，避免直接操作节点\n                editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_2__.OUTDENT_CONTENT_COMMAND, undefined);\n            }\n        }, \n    ];\n    return /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_7__.createPortal)(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `fixed z-50 ${toolbarBg} border rounded-lg p-1.5 flex space-x-0.5 shadow-lg`,\n        style: {\n            top: position.top,\n            left: position.left,\n            transform: \"translateX(-50%)\",\n            backgroundColor: theme === \"dark\" ? \"#3f3f46\" : \"#e4e4e7\"\n        },\n        children: toolbarButtons.map((button, index)=>{\n            if (button.type === \"separator\") {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `w-px h-6 ${theme === \"dark\" ? \"bg-gray-600\" : \"bg-gray-300\"} mx-1`\n                }, index, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                    lineNumber: 232,\n                    columnNumber: 25\n                }, this);\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: button.action,\n                title: button.title,\n                className: `\n                            px-2.5 py-1.5 rounded transition-colors duration-150 min-w-[30px] h-7 flex items-center justify-center text-sm font-medium\n                            ${button.isActive ? buttonActive : `${buttonText} ${buttonHover}`}\n                        `,\n                style: {\n                    backgroundColor: button.isActive ? theme === \"dark\" ? \"#3185eb\" : \"#eab834\" : \"transparent\"\n                },\n                onMouseEnter: (e)=>{\n                    if (!button.isActive) {\n                        e.currentTarget.style.backgroundColor = theme === \"dark\" ? \"#3185eb\" : \"#eab834\";\n                        if (theme === \"dark\") {\n                            e.currentTarget.style.color = \"white\";\n                        }\n                    }\n                },\n                onMouseLeave: (e)=>{\n                    if (!button.isActive) {\n                        e.currentTarget.style.backgroundColor = \"transparent\";\n                        e.currentTarget.style.color = \"\";\n                    }\n                },\n                children: button.icon\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 240,\n                columnNumber: 21\n            }, this);\n        })\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n        lineNumber: 220,\n        columnNumber: 9\n    }, this), document.body);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/editor/plugins/floating-toolbar-plugin.tsx\n");

/***/ }),

/***/ "./components/editor/plugins/highlight-plugin.tsx":
/*!********************************************************!*\
  !*** ./components/editor/plugins/highlight-plugin.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"TOGGLE_HIGHLIGHT_COMMAND\": () => (/* binding */ TOGGLE_HIGHLIGHT_COMMAND),\n/* harmony export */   \"default\": () => (/* binding */ HighlightPlugin)\n/* harmony export */ });\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"@lexical/react/LexicalComposerContext\");\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lexical */ \"lexical\");\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lexical__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/**\n * Highlight Plugin for Lexical\n * Provides text highlighting functionality\n */ \n\n\nconst TOGGLE_HIGHLIGHT_COMMAND = (0,lexical__WEBPACK_IMPORTED_MODULE_1__.createCommand)(\"TOGGLE_HIGHLIGHT_COMMAND\");\nfunction HighlightPlugin() {\n    const [editor] = (0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_0__.useLexicalComposerContext)();\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const removeToggleHighlightCommand = editor.registerCommand(TOGGLE_HIGHLIGHT_COMMAND, (color = \"#ffeb3b\")=>{\n            const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_1__.$getSelection)();\n            if ((0,lexical__WEBPACK_IMPORTED_MODULE_1__.$isRangeSelection)(selection)) {\n                // 使用Lexical内置的highlight格式，这样就能与HIGHLIGHT转换器兼容\n                if (selection.hasFormat(\"highlight\")) {\n                    // 移除高亮\n                    selection.formatText(\"highlight\");\n                } else {\n                    // 添加高亮\n                    selection.formatText(\"highlight\");\n                }\n            }\n            return true;\n        }, lexical__WEBPACK_IMPORTED_MODULE_1__.COMMAND_PRIORITY_LOW);\n        return ()=>{\n            removeToggleHighlightCommand();\n        };\n    }, [\n        editor\n    ]);\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/editor/plugins/highlight-plugin.tsx\n");

/***/ }),

/***/ "./components/editor/plugins/image-plugin.tsx":
/*!****************************************************!*\
  !*** ./components/editor/plugins/image-plugin.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"INSERT_IMAGE_COMMAND\": () => (/* binding */ INSERT_IMAGE_COMMAND),\n/* harmony export */   \"default\": () => (/* binding */ ImagePlugin)\n/* harmony export */ });\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"@lexical/react/LexicalComposerContext\");\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lexical */ \"lexical\");\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lexical__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _nodes_image_node__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../nodes/image-node */ \"./components/editor/nodes/image-node.tsx\");\n/**\n * Image Plugin for Lexical\n * Handles image insertion and Markdown syntax parsing\n */ \n\n\n\n\nconst INSERT_IMAGE_COMMAND = (0,lexical__WEBPACK_IMPORTED_MODULE_1__.createCommand)(\"INSERT_IMAGE_COMMAND\");\nfunction ImagePlugin() {\n    const [editor] = (0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_0__.useLexicalComposerContext)();\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const removeInsertImageCommand = editor.registerCommand(INSERT_IMAGE_COMMAND, (payload)=>{\n            const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_1__.$getSelection)();\n            if ((0,lexical__WEBPACK_IMPORTED_MODULE_1__.$isRangeSelection)(selection)) {\n                const imageNode = (0,_nodes_image_node__WEBPACK_IMPORTED_MODULE_3__.$createImageNode)(payload);\n                (0,lexical__WEBPACK_IMPORTED_MODULE_1__.$insertNodes)([\n                    imageNode\n                ]);\n            }\n            return true;\n        }, lexical__WEBPACK_IMPORTED_MODULE_1__.COMMAND_PRIORITY_LOW);\n        // 暂时禁用自动图片转换，避免问题\n        // TODO: 重新实现更稳定的图片Markdown转换\n        return ()=>{\n            removeInsertImageCommand();\n        };\n    }, [\n        editor\n    ]);\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/editor/plugins/image-plugin.tsx\n");

/***/ }),

/***/ "./components/editor/plugins/ime-plugin.tsx":
/*!**************************************************!*\
  !*** ./components/editor/plugins/ime-plugin.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ IMEPlugin)\n/* harmony export */ });\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"@lexical/react/LexicalComposerContext\");\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/**\n * IME Plugin for Lexical\n * Provides better Chinese input method support\n */ \n\nfunction IMEPlugin({ enabled =true , debug =false  } = {}) {\n    const [editor] = (0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_0__.useLexicalComposerContext)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!enabled) return;\n        const rootElement = editor.getRootElement();\n        if (!rootElement) return;\n        let isComposing = false;\n        let compositionData = \"\";\n        const handleCompositionStart = (event)=>{\n            isComposing = true;\n            compositionData = \"\";\n            if (debug) {\n                console.log(\"IME: Composition started\", event);\n            }\n        };\n        const handleCompositionUpdate = (event)=>{\n            if (isComposing) {\n                compositionData = event.data || \"\";\n                if (debug) {\n                    console.log(\"IME: Composition update\", event.data);\n                }\n            }\n        };\n        const handleCompositionEnd = (event)=>{\n            isComposing = false;\n            compositionData = \"\";\n            if (debug) {\n                console.log(\"IME: Composition ended\", event.data);\n            }\n        };\n        const handleBeforeInput = (event)=>{\n            // Let composition events handle IME input\n            if (isComposing) {\n                if (debug) {\n                    console.log(\"IME: Blocking beforeinput during composition\", event);\n                }\n                return;\n            }\n        };\n        const handleInput = (event)=>{\n            // Additional input handling if needed\n            if (debug && isComposing) {\n                console.log(\"IME: Input during composition\", event);\n            }\n        };\n        // Add event listeners\n        rootElement.addEventListener(\"compositionstart\", handleCompositionStart);\n        rootElement.addEventListener(\"compositionupdate\", handleCompositionUpdate);\n        rootElement.addEventListener(\"compositionend\", handleCompositionEnd);\n        rootElement.addEventListener(\"beforeinput\", handleBeforeInput);\n        rootElement.addEventListener(\"input\", handleInput);\n        // Cleanup\n        return ()=>{\n            rootElement.removeEventListener(\"compositionstart\", handleCompositionStart);\n            rootElement.removeEventListener(\"compositionupdate\", handleCompositionUpdate);\n            rootElement.removeEventListener(\"compositionend\", handleCompositionEnd);\n            rootElement.removeEventListener(\"beforeinput\", handleBeforeInput);\n            rootElement.removeEventListener(\"input\", handleInput);\n        };\n    }, [\n        editor,\n        enabled,\n        debug\n    ]);\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/editor/plugins/ime-plugin.tsx\n");

/***/ }),

/***/ "./components/editor/plugins/slash-commands-plugin.tsx":
/*!*************************************************************!*\
  !*** ./components/editor/plugins/slash-commands-plugin.tsx ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SlashCommandsPlugin)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"styled-jsx/style\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"@lexical/react/LexicalComposerContext\");\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _lexical_react_LexicalTypeaheadMenuPlugin__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @lexical/react/LexicalTypeaheadMenuPlugin */ \"@lexical/react/LexicalTypeaheadMenuPlugin\");\n/* harmony import */ var _lexical_react_LexicalTypeaheadMenuPlugin__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_lexical_react_LexicalTypeaheadMenuPlugin__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lexical_rich_text__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @lexical/rich-text */ \"@lexical/rich-text\");\n/* harmony import */ var _lexical_rich_text__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_lexical_rich_text__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lexical */ \"lexical\");\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lexical__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _lexical_selection__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @lexical/selection */ \"@lexical/selection\");\n/* harmony import */ var _lexical_selection__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(_lexical_selection__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _lexical_list__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @lexical/list */ \"@lexical/list\");\n/* harmony import */ var _lexical_list__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(_lexical_list__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @lexical/react/LexicalHorizontalRuleNode */ \"@lexical/react/LexicalHorizontalRuleNode\");\n/* harmony import */ var _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-dom */ \"react-dom\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next-themes */ \"next-themes\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_themes__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _heroicons_react_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @heroicons/react/outline */ \"@heroicons/react/outline\");\n/* harmony import */ var _heroicons_react_outline__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_12__);\n/**\n * Slash Commands Plugin for Lexical\n * Provides quick insertion of various content types using \"/\" trigger\n */ \n\n\n\n\n\n\n\n\n\n\n\n// Heroicons\n\nclass SlashCommandOption extends _lexical_react_LexicalTypeaheadMenuPlugin__WEBPACK_IMPORTED_MODULE_3__.MenuOption {\n    constructor(title, options){\n        super(title);\n        this.title = title;\n        this.keywords = options.keywords || [];\n        this.icon = options.icon;\n        this.keyboardShortcut = options.keyboardShortcut;\n        this.onSelect = options.onSelect.bind(this);\n    }\n}\nfunction SlashCommandMenuItem({ index , isSelected , onClick , onMouseEnter , option  }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n        tabIndex: -1,\n        className: `slash-command-item ${isSelected ? \"selected\" : \"\"}`,\n        ref: option.setRefElement,\n        role: \"option\",\n        \"aria-selected\": isSelected,\n        id: \"typeahead-item-\" + index,\n        onMouseEnter: onMouseEnter,\n        onClick: onClick,\n        children: [\n            option.icon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"slash-command-icon\",\n                children: option.icon\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\slash-commands-plugin.tsx\",\n                lineNumber: 84,\n                columnNumber: 29\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"slash-command-text\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"slash-command-title\",\n                        children: option.title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\slash-commands-plugin.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 17\n                    }, this),\n                    option.keyboardShortcut && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"slash-command-shortcut\",\n                        children: option.keyboardShortcut\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\slash-commands-plugin.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 21\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\slash-commands-plugin.tsx\",\n                lineNumber: 85,\n                columnNumber: 13\n            }, this)\n        ]\n    }, option.key, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\slash-commands-plugin.tsx\",\n        lineNumber: 73,\n        columnNumber: 9\n    }, this);\n}\nfunction SlashCommandsPlugin() {\n    const [editor] = (0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_2__.useLexicalComposerContext)();\n    const { 0: queryString , 1: setQueryString  } = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(null);\n    const { theme  } = (0,next_themes__WEBPACK_IMPORTED_MODULE_11__.useTheme)();\n    const checkForTriggerMatch = (0,_lexical_react_LexicalTypeaheadMenuPlugin__WEBPACK_IMPORTED_MODULE_3__.useBasicTypeaheadTriggerMatch)(\"/\", {\n        minLength: 0\n    });\n    const options = (0,react__WEBPACK_IMPORTED_MODULE_9__.useMemo)(()=>{\n        const baseOptions = [\n            new SlashCommandOption(\"Checklist\", {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_12__.ClipboardListIcon, {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\slash-commands-plugin.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 23\n                }, this),\n                keywords: [\n                    \"checklist\",\n                    \"todo\",\n                    \"task\",\n                    \"checkbox\"\n                ],\n                onSelect: ()=>{\n                    editor.dispatchCommand(_lexical_list__WEBPACK_IMPORTED_MODULE_7__.INSERT_CHECK_LIST_COMMAND, undefined);\n                }\n            }),\n            new SlashCommandOption(\"Bullet List\", {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_12__.ViewListIcon, {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\slash-commands-plugin.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 23\n                }, this),\n                keywords: [\n                    \"bullet\",\n                    \"list\",\n                    \"unordered\",\n                    \"ul\"\n                ],\n                onSelect: ()=>{\n                    editor.dispatchCommand(_lexical_list__WEBPACK_IMPORTED_MODULE_7__.INSERT_UNORDERED_LIST_COMMAND, undefined);\n                }\n            }),\n            new SlashCommandOption(\"Numbered List\", {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_12__.CollectionIcon, {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\slash-commands-plugin.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 23\n                }, this),\n                keywords: [\n                    \"numbered\",\n                    \"list\",\n                    \"ordered\",\n                    \"ol\"\n                ],\n                onSelect: ()=>{\n                    editor.dispatchCommand(_lexical_list__WEBPACK_IMPORTED_MODULE_7__.INSERT_ORDERED_LIST_COMMAND, undefined);\n                }\n            }),\n            new SlashCommandOption(\"Quote\", {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_12__.AnnotationIcon, {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\slash-commands-plugin.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 23\n                }, this),\n                keywords: [\n                    \"quote\",\n                    \"blockquote\",\n                    \"citation\"\n                ],\n                onSelect: ()=>{\n                    editor.update(()=>{\n                        const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_5__.$getSelection)();\n                        if ((0,lexical__WEBPACK_IMPORTED_MODULE_5__.$isRangeSelection)(selection)) {\n                            (0,_lexical_selection__WEBPACK_IMPORTED_MODULE_6__.$setBlocksType)(selection, ()=>(0,_lexical_rich_text__WEBPACK_IMPORTED_MODULE_4__.$createQuoteNode)());\n                        }\n                    });\n                }\n            }),\n            new SlashCommandOption(\"Divider\", {\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_12__.MinusIcon, {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\slash-commands-plugin.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 23\n                }, this),\n                keywords: [\n                    \"divider\",\n                    \"separator\",\n                    \"hr\",\n                    \"horizontal\",\n                    \"rule\"\n                ],\n                onSelect: ()=>{\n                    editor.dispatchCommand(_lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_8__.INSERT_HORIZONTAL_RULE_COMMAND, undefined);\n                }\n            }), \n        ];\n        if (!queryString) {\n            return baseOptions;\n        }\n        return baseOptions.filter((option)=>{\n            return new RegExp(queryString, \"gi\").exec(option.title) || option.keywords != null ? option.keywords.some((keyword)=>new RegExp(queryString, \"gi\").exec(keyword)) : false;\n        });\n    }, [\n        editor,\n        queryString\n    ]);\n    const onSelectOption = (0,react__WEBPACK_IMPORTED_MODULE_9__.useCallback)((selectedOption, nodeToRemove, closeMenu, matchingString)=>{\n        editor.update(()=>{\n            if (nodeToRemove) {\n                nodeToRemove.remove();\n            }\n            selectedOption.onSelect(matchingString);\n            closeMenu();\n        });\n    }, [\n        editor\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalTypeaheadMenuPlugin__WEBPACK_IMPORTED_MODULE_3__.LexicalTypeaheadMenuPlugin, {\n                onQueryChange: setQueryString,\n                onSelectOption: onSelectOption,\n                triggerFn: checkForTriggerMatch,\n                options: options,\n                menuRenderFn: (anchorElementRef, { selectedIndex , selectOptionAndCleanUp , setHighlightedIndex  })=>anchorElementRef.current && options.length ? /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_10__.createPortal)(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-c46a33cdbde614f\" + \" \" + `slash-commands-menu ${theme === \"dark\" ? \"dark\" : \"\"}`,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"jsx-c46a33cdbde614f\" + \" \" + \"slash-commands-list\",\n                            children: options.map((option, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SlashCommandMenuItem, {\n                                    index: i,\n                                    isSelected: selectedIndex === i,\n                                    onClick: ()=>{\n                                        setHighlightedIndex(i);\n                                        selectOptionAndCleanUp(option);\n                                    },\n                                    onMouseEnter: ()=>{\n                                        setHighlightedIndex(i);\n                                    },\n                                    option: option\n                                }, option.key, false, void 0, void 0))\n                        }, void 0, false, void 0, void 0)\n                    }, void 0, false, void 0, void 0), anchorElementRef.current) : null\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\slash-commands-plugin.tsx\",\n                lineNumber: 182,\n                columnNumber: 13\n            }, this),\n            (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"c46a33cdbde614f\",\n                children: \".slash-commands-menu{background:#f4f4f5;border:1px solid#e5e7eb;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;-webkit-box-shadow:0 20px 25px -5px rgba(0,0,0,.1),0 10px 10px -5px rgba(0,0,0,.04);-moz-box-shadow:0 20px 25px -5px rgba(0,0,0,.1),0 10px 10px -5px rgba(0,0,0,.04);box-shadow:0 20px 25px -5px rgba(0,0,0,.1),0 10px 10px -5px rgba(0,0,0,.04);max-height:300px;min-width:240px;overflow-y:auto;padding:.5rem 0;z-index:1000;position:absolute}.slash-commands-menu.dark{background:#27272a;border-color:#374151}.slash-commands-list{list-style:none;margin:0;padding:0}.slash-command-item{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;padding:.75rem 1rem;cursor:pointer;-webkit-transition:all.15s ease;-moz-transition:all.15s ease;-o-transition:all.15s ease;transition:all.15s ease;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;margin:0 .25rem}.slash-command-item:hover,.slash-command-item.selected{background-color:#eab834;color:black}.slash-commands-menu.dark .slash-command-item:hover,.slash-commands-menu.dark .slash-command-item.selected{background-color:#3185eb;color:white}.slash-command-item:hover .slash-command-icon,.slash-command-item.selected .slash-command-icon,.slash-command-item:hover .slash-command-title,.slash-command-item.selected .slash-command-title{color:black}.slash-commands-menu.dark .slash-command-item:hover .slash-command-icon,.slash-commands-menu.dark .slash-command-item.selected .slash-command-icon,.slash-commands-menu.dark .slash-command-item:hover .slash-command-title,.slash-commands-menu.dark .slash-command-item.selected .slash-command-title{color:white}.slash-command-icon{margin-right:.75rem;width:2rem;height:2rem;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;font-size:1rem;color:#6b7280;background-color:#e4e4e7;-webkit-border-radius:.375rem;-moz-border-radius:.375rem;border-radius:.375rem;font-weight:600}.slash-commands-menu.dark .slash-command-icon{background-color:#3f3f46;color:#9ca3af}.slash-command-text{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-webkit-flex-direction:column;-moz-box-orient:vertical;-moz-box-direction:normal;-ms-flex-direction:column;flex-direction:column;-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1}.slash-command-title{font-size:.875rem;font-weight:600;color:#111827;line-height:1.25}.slash-commands-menu.dark .slash-command-title{color:#f9fafb}.slash-command-shortcut{font-size:.75rem;color:#6b7280;margin-top:.125rem}.slash-commands-menu.dark .slash-command-shortcut{color:#9ca3af}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/editor/plugins/slash-commands-plugin.tsx\n");

/***/ }),

/***/ "./libs/shared/meta.ts":
/*!*****************************!*\
  !*** ./libs/shared/meta.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"EDITOR_SIZE\": () => (/* binding */ EDITOR_SIZE),\n/* harmony export */   \"NOTE_DELETED\": () => (/* binding */ NOTE_DELETED),\n/* harmony export */   \"NOTE_PINNED\": () => (/* binding */ NOTE_PINNED),\n/* harmony export */   \"NOTE_SHARED\": () => (/* binding */ NOTE_SHARED),\n/* harmony export */   \"NUMBER_KEYS\": () => (/* binding */ NUMBER_KEYS),\n/* harmony export */   \"PAGE_META_KEY\": () => (/* binding */ PAGE_META_KEY)\n/* harmony export */ });\nvar NOTE_DELETED;\n(function(NOTE_DELETED) {\n    NOTE_DELETED[NOTE_DELETED[\"NORMAL\"] = 0] = \"NORMAL\";\n    NOTE_DELETED[NOTE_DELETED[\"DELETED\"] = 1] = \"DELETED\";\n})(NOTE_DELETED || (NOTE_DELETED = {}));\nvar NOTE_SHARED;\n(function(NOTE_SHARED) {\n    NOTE_SHARED[NOTE_SHARED[\"PRIVATE\"] = 0] = \"PRIVATE\";\n    NOTE_SHARED[NOTE_SHARED[\"PUBLIC\"] = 1] = \"PUBLIC\";\n})(NOTE_SHARED || (NOTE_SHARED = {}));\nvar NOTE_PINNED;\n(function(NOTE_PINNED) {\n    NOTE_PINNED[NOTE_PINNED[\"UNPINNED\"] = 0] = \"UNPINNED\";\n    NOTE_PINNED[NOTE_PINNED[\"PINNED\"] = 1] = \"PINNED\";\n})(NOTE_PINNED || (NOTE_PINNED = {}));\nvar EDITOR_SIZE;\n(function(EDITOR_SIZE) {\n    EDITOR_SIZE[EDITOR_SIZE[\"SMALL\"] = 0] = \"SMALL\";\n    EDITOR_SIZE[EDITOR_SIZE[\"LARGE\"] = 1] = \"LARGE\";\n    EDITOR_SIZE[EDITOR_SIZE[\"FULL\"] = 2] = \"FULL\";\n})(EDITOR_SIZE || (EDITOR_SIZE = {}));\nconst PAGE_META_KEY = [\n    \"title\",\n    \"pid\",\n    \"id\",\n    \"shared\",\n    \"pic\",\n    \"date\",\n    \"deleted\",\n    \"pinned\",\n    \"editorsize\", \n];\nconst NUMBER_KEYS = [\n    \"deleted\",\n    \"shared\",\n    \"pinned\",\n    \"editorsize\", \n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./libs/shared/meta.ts\n");

/***/ }),

/***/ "./libs/shared/settings.ts":
/*!*********************************!*\
  !*** ./libs/shared/settings.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"DEFAULT_SETTINGS\": () => (/* binding */ DEFAULT_SETTINGS),\n/* harmony export */   \"formatSettings\": () => (/* binding */ formatSettings)\n/* harmony export */ });\n/* harmony import */ var locales__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! locales */ \"./locales/index.ts\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _meta__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./meta */ \"./libs/shared/meta.ts\");\n/* harmony import */ var _tree__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./tree */ \"./libs/shared/tree.ts\");\n\n\n\n\nconst DEFAULT_SETTINGS = Object.freeze({\n    split_sizes: [\n        30,\n        70\n    ],\n    daily_root_id: _tree__WEBPACK_IMPORTED_MODULE_3__.ROOT_ID,\n    sidebar_is_fold: false,\n    locale: locales__WEBPACK_IMPORTED_MODULE_0__.Locale.EN,\n    editorsize: _meta__WEBPACK_IMPORTED_MODULE_2__.EDITOR_SIZE.SMALL\n});\nfunction formatSettings(body = {}) {\n    const settings = {\n        ...DEFAULT_SETTINGS\n    };\n    if ((0,lodash__WEBPACK_IMPORTED_MODULE_1__.isString)(body.daily_root_id)) {\n        settings.daily_root_id = body.daily_root_id;\n    }\n    if ((0,lodash__WEBPACK_IMPORTED_MODULE_1__.isArray)(body.split_sizes) && (0,lodash__WEBPACK_IMPORTED_MODULE_1__.isNumber)(body.split_sizes[0]) && (0,lodash__WEBPACK_IMPORTED_MODULE_1__.isNumber)(body.split_sizes[1])) {\n        // Sometimes when debugging mode is turned on in the browser,\n        // the size will become abnormal\n        const [size1, size2] = body.split_sizes;\n        if (size1 > 100 || size1 < 0 || size2 > 100 || size2 < 0) {\n            settings.split_sizes = DEFAULT_SETTINGS.split_sizes;\n        } else {\n            settings.split_sizes = [\n                size1,\n                size2\n            ];\n        }\n    }\n    if ((0,lodash__WEBPACK_IMPORTED_MODULE_1__.isBoolean)(body.sidebar_is_fold)) {\n        settings.sidebar_is_fold = body.sidebar_is_fold;\n    }\n    if ((0,lodash__WEBPACK_IMPORTED_MODULE_1__.isString)(body.last_visit)) {\n        settings.last_visit = body.last_visit;\n    }\n    if ((0,lodash__WEBPACK_IMPORTED_MODULE_1__.values)(locales__WEBPACK_IMPORTED_MODULE_0__.Locale).includes(body.locale)) {\n        settings.locale = body.locale;\n    }\n    if ((0,lodash__WEBPACK_IMPORTED_MODULE_1__.isString)(body.injection)) {\n        settings.injection = body.injection;\n    }\n    if ((0,lodash__WEBPACK_IMPORTED_MODULE_1__.isNumber)(body.editorsize)) {\n        settings.editorsize = body.editorsize;\n    }\n    return settings;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./libs/shared/settings.ts\n");

/***/ }),

/***/ "./libs/shared/tree.ts":
/*!*****************************!*\
  !*** ./libs/shared/tree.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"DEFAULT_TREE\": () => (/* binding */ DEFAULT_TREE),\n/* harmony export */   \"ROOT_ID\": () => (/* binding */ ROOT_ID),\n/* harmony export */   \"cleanItemModel\": () => (/* binding */ cleanItemModel),\n/* harmony export */   \"cleanTreeModel\": () => (/* binding */ cleanTreeModel),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   \"makeHierarchy\": () => (/* binding */ makeHierarchy)\n/* harmony export */ });\n/* harmony import */ var _atlaskit_tree__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @atlaskit/tree */ \"@atlaskit/tree\");\n/* harmony import */ var _atlaskit_tree__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_atlaskit_tree__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst ROOT_ID = \"root\";\nconst DEFAULT_TREE = {\n    rootId: ROOT_ID,\n    items: {\n        root: {\n            id: ROOT_ID,\n            children: []\n        }\n    }\n};\nfunction addItem(tree, id, pid = ROOT_ID) {\n    const newTree = (0,lodash__WEBPACK_IMPORTED_MODULE_1__.cloneDeep)(tree);\n    newTree.items[id] = newTree.items[id] || {\n        id,\n        children: []\n    };\n    const parentItem = newTree.items[pid];\n    if (parentItem) {\n        if (!parentItem.children.includes(id)) {\n            parentItem.children = [\n                ...parentItem.children,\n                id\n            ];\n        }\n    } else {\n        throw new Error(`Parent ID '${pid}' does not refer to a valid item`);\n    }\n    return newTree;\n}\nfunction mutateItem(tree, id, data) {\n    if (data.data) {\n        data.data = {\n            ...tree.items[id]?.data,\n            ...data.data\n        };\n    }\n    return (0,_atlaskit_tree__WEBPACK_IMPORTED_MODULE_0__.mutateTree)(tree, id, data);\n}\nfunction removeItem(tree, id) {\n    (0,lodash__WEBPACK_IMPORTED_MODULE_1__.forEach)(tree.items, (item)=>{\n        if (item.children.includes(id)) {\n            (0,lodash__WEBPACK_IMPORTED_MODULE_1__.pull)(item.children, id);\n            return false;\n        }\n    });\n    return (0,lodash__WEBPACK_IMPORTED_MODULE_1__.cloneDeep)(tree);\n}\nfunction moveItem(tree, source, destination) {\n    if (!destination) {\n        return tree;\n    }\n    return (0,_atlaskit_tree__WEBPACK_IMPORTED_MODULE_0__.moveItemOnTree)(tree, source, destination);\n}\n/**\n * 从原父节点上移除，添加到新的父节点上\n */ function restoreItem(tree, id, pid = ROOT_ID) {\n    tree = removeItem(tree, id);\n    tree = addItem(tree, id, pid);\n    return tree;\n}\nfunction deleteItem(tree, id) {\n    tree = (0,lodash__WEBPACK_IMPORTED_MODULE_1__.cloneDeep)(tree);\n    delete tree.items[id];\n    return tree;\n}\nconst flattenTree = (tree, rootId = tree.rootId)=>{\n    if (!tree.items[rootId]) {\n        return [];\n    }\n    return (0,lodash__WEBPACK_IMPORTED_MODULE_1__.reduce)(tree.items[rootId].children, (accum, itemId)=>{\n        const item = tree.items[itemId];\n        const children = flattenTree({\n            rootId: item.id,\n            items: tree.items\n        });\n        return [\n            ...accum,\n            item,\n            ...children\n        ];\n    }, []);\n};\nfunction makeHierarchy(tree, rootId = tree.rootId) {\n    if (!tree.items[rootId]) {\n        return false;\n    }\n    const root = tree.items[rootId];\n    return {\n        ...root,\n        children: root.children.map((v)=>makeHierarchy(tree, v)).filter((v)=>!!v)\n    };\n}\nfunction cleanItemModel(model) {\n    if (!model.id) throw new Error(\"Missing id on tree model\");\n    const children = model.children ?? [];\n    return {\n        ...model,\n        id: model.id,\n        children,\n        hasChildren: children.length > 0,\n        data: model.data,\n        isExpanded: model.isExpanded ?? false\n    };\n}\nfunction cleanTreeModel(model) {\n    const items = {};\n    if (model.items) {\n        for(const itemId in model.items){\n            const item = model.items[itemId];\n            if (!item) {\n                continue;\n            }\n            const cleanedItem = cleanItemModel(item);\n            const children = [];\n            for (const child of cleanedItem.children){\n                if (child && model.items[child]) {\n                    children.push(child);\n                }\n            }\n            items[itemId] = {\n                ...cleanedItem,\n                children\n            };\n        }\n    }\n    return {\n        ...model,\n        rootId: model.rootId ?? ROOT_ID,\n        items: items\n    };\n}\nconst TreeActions = {\n    addItem,\n    mutateItem,\n    removeItem,\n    moveItem,\n    restoreItem,\n    deleteItem,\n    flattenTree,\n    makeHierarchy,\n    cleanTreeModel,\n    cleanItemModel\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TreeActions);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./libs/shared/tree.ts\n");

/***/ }),

/***/ "./libs/web/hooks/use-i18n.tsx":
/*!*************************************!*\
  !*** ./libs/web/hooks/use-i18n.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useI18n)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _utils_i18n_provider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/i18n-provider */ \"./libs/web/utils/i18n-provider.tsx\");\n\n\nfunction useI18n() {\n    const i18n = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_utils_i18n_provider__WEBPACK_IMPORTED_MODULE_1__.I18nContext);\n    return i18n;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9saWJzL3dlYi9ob29rcy91c2UtaTE4bi50c3guanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFtQztBQUNrQjtBQUV0QyxTQUFTRSxPQUFPLEdBQUc7SUFDOUIsTUFBTUMsSUFBSSxHQUFHSCxpREFBVSxDQUFDQyw2REFBVyxDQUFDO0lBQ3BDLE9BQU9FLElBQUksQ0FBQztBQUNoQixDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbm90ZWEvLi9saWJzL3dlYi9ob29rcy91c2UtaTE4bi50c3g/MjJhMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VDb250ZXh0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgSTE4bkNvbnRleHQgfSBmcm9tICcuLi91dGlscy9pMThuLXByb3ZpZGVyJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gdXNlSTE4bigpIHtcbiAgICBjb25zdCBpMThuID0gdXNlQ29udGV4dChJMThuQ29udGV4dCk7XG4gICAgcmV0dXJuIGkxOG47XG59XG4iXSwibmFtZXMiOlsidXNlQ29udGV4dCIsIkkxOG5Db250ZXh0IiwidXNlSTE4biIsImkxOG4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./libs/web/hooks/use-i18n.tsx\n");

/***/ }),

/***/ "./libs/web/hooks/use-mounted.ts":
/*!***************************************!*\
  !*** ./libs/web/hooks/use-mounted.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst useMounted = ()=>{\n    const { 0: mounted , 1: setMounted  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        setMounted(true);\n    }, []);\n    return mounted;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (useMounted);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9saWJzL3dlYi9ob29rcy91c2UtbW91bnRlZC50cy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNEM7QUFFNUMsTUFBTUUsVUFBVSxHQUFHLElBQU07SUFDckIsTUFBTSxLQUFDQyxPQUFPLE1BQUVDLFVBQVUsTUFBSUgsK0NBQVEsQ0FBQyxLQUFLLENBQUM7SUFDN0NELGdEQUFTLENBQUMsSUFBTTtRQUNaSSxVQUFVLENBQUMsSUFBSSxDQUFDLENBQUM7SUFDckIsQ0FBQyxFQUFFLEVBQUUsQ0FBQyxDQUFDO0lBRVAsT0FBT0QsT0FBTyxDQUFDO0FBQ25CLENBQUM7QUFFRCxpRUFBZUQsVUFBVSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbm90ZWEvLi9saWJzL3dlYi9ob29rcy91c2UtbW91bnRlZC50cz9jYjY4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5cbmNvbnN0IHVzZU1vdW50ZWQgPSAoKSA9PiB7XG4gICAgY29uc3QgW21vdW50ZWQsIHNldE1vdW50ZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xuICAgIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIHNldE1vdW50ZWQodHJ1ZSk7XG4gICAgfSwgW10pO1xuXG4gICAgcmV0dXJuIG1vdW50ZWQ7XG59O1xuXG5leHBvcnQgZGVmYXVsdCB1c2VNb3VudGVkO1xuIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsInVzZVN0YXRlIiwidXNlTW91bnRlZCIsIm1vdW50ZWQiLCJzZXRNb3VudGVkIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./libs/web/hooks/use-mounted.ts\n");

/***/ }),

/***/ "./libs/web/utils/i18n-provider.tsx":
/*!******************************************!*\
  !*** ./libs/web/utils/i18n-provider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"I18nContext\": () => (/* binding */ I18nContext),\n/* harmony export */   \"default\": () => (/* binding */ I18nProvider),\n/* harmony export */   \"defaultLanguage\": () => (/* binding */ defaultLanguage),\n/* harmony export */   \"languages\": () => (/* binding */ languages)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var rosetta__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rosetta */ \"rosetta\");\n/* harmony import */ var rosetta__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(rosetta__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var libs_shared_settings__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! libs/shared/settings */ \"./libs/shared/settings.ts\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var locales__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! locales */ \"./locales/index.ts\");\n/* harmony import */ var pupa__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! pupa */ \"pupa\");\n/* harmony import */ var pupa__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(pupa__WEBPACK_IMPORTED_MODULE_6__);\n\n\n\n\n\n\n\nconst i18n = rosetta__WEBPACK_IMPORTED_MODULE_2___default()();\nconst defaultLanguage = libs_shared_settings__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_SETTINGS.locale;\nconst languages = (0,lodash__WEBPACK_IMPORTED_MODULE_4__.values)(locales__WEBPACK_IMPORTED_MODULE_5__.Locale);\nconst I18nContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({});\n// default language\ni18n.locale(defaultLanguage);\nfunction I18nProvider({ children , locale , lngDict  }) {\n    const activeLocaleRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(locale || defaultLanguage);\n    const { 1: setTick  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const firstRender = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(true);\n    const i18nWrapper = {\n        activeLocale: activeLocaleRef.current,\n        t: (key, ...args)=>{\n            if (activeLocaleRef.current === defaultLanguage) {\n                return pupa__WEBPACK_IMPORTED_MODULE_6___default()(Array.isArray(key) ? key.join(\"\") : key, args[0] ?? {});\n            }\n            return i18n.t(Array.isArray(key) ? key : [\n                key\n            ], ...args);\n        },\n        locale: (l, dict)=>{\n            i18n.locale(l);\n            activeLocaleRef.current = l;\n            if (dict) {\n                i18n.set(l, dict);\n            }\n            // force rerender to update view\n            setTick((tick)=>tick + 1);\n        }\n    };\n    // for initial SSR render\n    if (locale && firstRender.current === true) {\n        firstRender.current = false;\n        i18nWrapper.locale(locale, lngDict);\n    }\n    // when locale is updated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (locale) {\n            i18nWrapper.locale(locale, lngDict);\n        }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        lngDict,\n        locale\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(I18nContext.Provider, {\n        value: i18nWrapper,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\libs\\\\web\\\\utils\\\\i18n-provider.tsx\",\n        lineNumber: 72,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./libs/web/utils/i18n-provider.tsx\n");

/***/ }),

/***/ "./locales/index.ts":
/*!**************************!*\
  !*** ./locales/index.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"Locale\": () => (/* binding */ Locale),\n/* harmony export */   \"configLocale\": () => (/* binding */ configLocale),\n/* harmony export */   \"muiLocale\": () => (/* binding */ muiLocale)\n/* harmony export */ });\n/* harmony import */ var _material_ui_core_locale__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @material-ui/core/locale */ \"@material-ui/core/locale\");\n/* harmony import */ var _material_ui_core_locale__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_material_ui_core_locale__WEBPACK_IMPORTED_MODULE_0__);\n\nvar Locale;\n(function(Locale) {\n    Locale[\"ZH_CN\"] = \"zh-CN\";\n    Locale[\"EN\"] = \"en\";\n    Locale[\"de_DE\"] = \"de-DE\";\n    Locale[\"ru_RU\"] = \"ru-RU\";\n    Locale[\"ar\"] = \"ar\";\n    Locale[\"it_IT\"] = \"it-IT\";\n    Locale[\"nl_NL\"] = \"nl-NL\";\n    Locale[\"fr_FR\"] = \"fr-FR\";\n    Locale[\"sv_SE\"] = \"sv-SE\";\n})(Locale || (Locale = {}));\nconst muiLocale = {\n    [Locale.ZH_CN]: _material_ui_core_locale__WEBPACK_IMPORTED_MODULE_0__.zhCN,\n    [Locale.EN]: _material_ui_core_locale__WEBPACK_IMPORTED_MODULE_0__.enUS,\n    [Locale.de_DE]: _material_ui_core_locale__WEBPACK_IMPORTED_MODULE_0__.deDE,\n    [Locale.ru_RU]: _material_ui_core_locale__WEBPACK_IMPORTED_MODULE_0__.ruRU,\n    // FIXME: upgrade material-ui and import arEG\n    [Locale.ar]: _material_ui_core_locale__WEBPACK_IMPORTED_MODULE_0__.enUS,\n    [Locale.it_IT]: _material_ui_core_locale__WEBPACK_IMPORTED_MODULE_0__.itIT,\n    [Locale.nl_NL]: _material_ui_core_locale__WEBPACK_IMPORTED_MODULE_0__.nlNL,\n    [Locale.fr_FR]: _material_ui_core_locale__WEBPACK_IMPORTED_MODULE_0__.frFR,\n    [Locale.sv_SE]: _material_ui_core_locale__WEBPACK_IMPORTED_MODULE_0__.svSE\n};\nconst configLocale = {\n    [Locale.EN]: \"English\",\n    [Locale.ZH_CN]: \"简体中文\",\n    [Locale.de_DE]: \"Deutsch\",\n    [Locale.ru_RU]: \"Русский\",\n    [Locale.ar]: \"العربية\",\n    [Locale.it_IT]: \"Italiano\",\n    [Locale.nl_NL]: \"Nederlands\",\n    [Locale.fr_FR]: \"fran\\xe7ais\",\n    [Locale.sv_SE]: \"Svenska\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./locales/index.ts\n");

/***/ }),

/***/ "./pages/test-indent.tsx":
/*!*******************************!*\
  !*** ./pages/test-indent.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TestIndentPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var components_editor_lexical_editor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! components/editor/lexical-editor */ \"./components/editor/lexical-editor.tsx\");\n/**\n * Test page for custom indent functionality\n * This page allows testing the new indent system\n */ \n\n\n// import { TEST_INDENT_MARKDOWN, validateIndentPreservation, debugIndentStructure } from 'components/editor/utils/indent-test';\nfunction TestIndentPage() {\n    const { 0: content , 1: setContent  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(`# 测试标题\n\n这是一个普通段落。\n\n这是另一个段落，可以用来测试缩进。\n\n- 列表项 1\n- 列表项 2\n- 列表项 3\n\n1. 编号列表 1\n2. 编号列表 2\n3. 编号列表 3`);\n    const { 0: testResults , 1: setTestResults  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const handleContentChange = (getValue)=>{\n        const newContent = getValue();\n        setContent(newContent);\n        // 简单的测试：检查是否包含缩进标记\n        const timestamp = new Date().toLocaleTimeString();\n        const hasIndentMarkers = newContent.includes(\"<!-- indent:\");\n        if (hasIndentMarkers) {\n            setTestResults((prev)=>[\n                    ...prev,\n                    `${timestamp}: ✅ 检测到缩进标记`\n                ]);\n        } else {\n            setTestResults((prev)=>[\n                    ...prev,\n                    `${timestamp}: ℹ️ 内容更新，无缩进标记`\n                ]);\n        }\n        // Debug output\n        console.log(\"=== Content Updated ===\");\n        console.log(newContent);\n    };\n    const resetContent = ()=>{\n        setContent(`# 测试标题\n\n这是一个普通段落。\n\n这是另一个段落，可以用来测试缩进。\n\n- 列表项 1\n- 列表项 2\n- 列表项 3\n\n1. 编号列表 1\n2. 编号列表 2\n3. 编号列表 3`);\n        setTestResults([]);\n    };\n    const clearResults = ()=>{\n        setTestResults([]);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            padding: \"20px\",\n            maxWidth: \"1200px\",\n            margin: \"0 auto\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                children: \"Lexical Editor Indent Test\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\test-indent.tsx\",\n                lineNumber: 68,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: resetContent,\n                        style: {\n                            marginRight: \"10px\",\n                            padding: \"8px 16px\",\n                            backgroundColor: \"#007bff\",\n                            color: \"white\",\n                            border: \"none\",\n                            borderRadius: \"4px\",\n                            cursor: \"pointer\"\n                        },\n                        children: \"Reset Test Content\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\test-indent.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: clearResults,\n                        style: {\n                            padding: \"8px 16px\",\n                            backgroundColor: \"#6c757d\",\n                            color: \"white\",\n                            border: \"none\",\n                            borderRadius: \"4px\",\n                            cursor: \"pointer\"\n                        },\n                        children: \"Clear Results\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\test-indent.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\test-indent.tsx\",\n                lineNumber: 70,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    display: \"flex\",\n                    gap: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                children: \"Editor\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\test-indent.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    border: \"1px solid #ccc\",\n                                    borderRadius: \"4px\",\n                                    minHeight: \"400px\",\n                                    backgroundColor: \"white\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_editor_lexical_editor__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    value: content,\n                                    onChange: handleContentChange,\n                                    readOnly: false\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\test-indent.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 25\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\test-indent.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\test-indent.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            flex: 1\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                children: \"Test Results\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\test-indent.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    border: \"1px solid #ccc\",\n                                    borderRadius: \"4px\",\n                                    padding: \"10px\",\n                                    minHeight: \"400px\",\n                                    backgroundColor: \"#f8f9fa\",\n                                    fontFamily: \"monospace\",\n                                    fontSize: \"12px\",\n                                    overflow: \"auto\"\n                                },\n                                children: testResults.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: \"#6c757d\"\n                                    },\n                                    children: \"No test results yet. Make changes to the editor to see results.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\test-indent.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 29\n                                }, this) : testResults.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            marginBottom: \"5px\"\n                                        },\n                                        children: result\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\test-indent.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 33\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\test-indent.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\test-indent.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\test-indent.tsx\",\n                lineNumber: 100,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginTop: \"20px\",\n                    padding: \"15px\",\n                    backgroundColor: \"#e9ecef\",\n                    borderRadius: \"4px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Test Instructions\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\test-indent.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    \"Use \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"kbd\", {\n                                        children: \"Tab\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\test-indent.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 29\n                                    }, this),\n                                    \" to indent content\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\test-indent.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    \"Use \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"kbd\", {\n                                        children: \"Shift+Tab\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\test-indent.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 29\n                                    }, this),\n                                    \" to outdent content\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\test-indent.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"Use the bubble menu indent/outdent buttons when text is selected\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\test-indent.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"Try indenting paragraphs, headings, and list items\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\test-indent.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"Save and reload to test persistence\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\test-indent.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"Check the test results panel for validation feedback\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\test-indent.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\test-indent.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                        children: \"Expected Behavior:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\test-indent.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"Indentation should be preserved when saving/loading\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\test-indent.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"Both paragraph and list indentation should work\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\test-indent.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"Visual indentation should match saved markdown\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\test-indent.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"Test results should show ✅ PASSED for proper indent preservation\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\test-indent.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\test-indent.tsx\",\n                        lineNumber: 157,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\test-indent.tsx\",\n                lineNumber: 145,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginTop: \"20px\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Raw Markdown Content\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\test-indent.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                        style: {\n                            backgroundColor: \"#f8f9fa\",\n                            padding: \"10px\",\n                            borderRadius: \"4px\",\n                            fontSize: \"12px\",\n                            overflow: \"auto\",\n                            maxHeight: \"300px\"\n                        },\n                        children: content\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\test-indent.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\test-indent.tsx\",\n                lineNumber: 166,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\pages\\\\test-indent.tsx\",\n        lineNumber: 67,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/test-indent.tsx\n");

/***/ }),

/***/ "@atlaskit/tree":
/*!*********************************!*\
  !*** external "@atlaskit/tree" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@atlaskit/tree");

/***/ }),

/***/ "@heroicons/react/outline":
/*!*******************************************!*\
  !*** external "@heroicons/react/outline" ***!
  \*******************************************/
/***/ ((module) => {

module.exports = require("@heroicons/react/outline");

/***/ }),

/***/ "@lexical/code":
/*!********************************!*\
  !*** external "@lexical/code" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("@lexical/code");

/***/ }),

/***/ "@lexical/link":
/*!********************************!*\
  !*** external "@lexical/link" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("@lexical/link");

/***/ }),

/***/ "@lexical/list":
/*!********************************!*\
  !*** external "@lexical/list" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("@lexical/list");

/***/ }),

/***/ "@lexical/markdown":
/*!************************************!*\
  !*** external "@lexical/markdown" ***!
  \************************************/
/***/ ((module) => {

module.exports = require("@lexical/markdown");

/***/ }),

/***/ "@lexical/react/LexicalAutoFocusPlugin":
/*!********************************************************!*\
  !*** external "@lexical/react/LexicalAutoFocusPlugin" ***!
  \********************************************************/
/***/ ((module) => {

module.exports = require("@lexical/react/LexicalAutoFocusPlugin");

/***/ }),

/***/ "@lexical/react/LexicalCheckListPlugin":
/*!********************************************************!*\
  !*** external "@lexical/react/LexicalCheckListPlugin" ***!
  \********************************************************/
/***/ ((module) => {

module.exports = require("@lexical/react/LexicalCheckListPlugin");

/***/ }),

/***/ "@lexical/react/LexicalComposer":
/*!*************************************************!*\
  !*** external "@lexical/react/LexicalComposer" ***!
  \*************************************************/
/***/ ((module) => {

module.exports = require("@lexical/react/LexicalComposer");

/***/ }),

/***/ "@lexical/react/LexicalComposerContext":
/*!********************************************************!*\
  !*** external "@lexical/react/LexicalComposerContext" ***!
  \********************************************************/
/***/ ((module) => {

module.exports = require("@lexical/react/LexicalComposerContext");

/***/ }),

/***/ "@lexical/react/LexicalContentEditable":
/*!********************************************************!*\
  !*** external "@lexical/react/LexicalContentEditable" ***!
  \********************************************************/
/***/ ((module) => {

module.exports = require("@lexical/react/LexicalContentEditable");

/***/ }),

/***/ "@lexical/react/LexicalErrorBoundary":
/*!******************************************************!*\
  !*** external "@lexical/react/LexicalErrorBoundary" ***!
  \******************************************************/
/***/ ((module) => {

module.exports = require("@lexical/react/LexicalErrorBoundary");

/***/ }),

/***/ "@lexical/react/LexicalHistoryPlugin":
/*!******************************************************!*\
  !*** external "@lexical/react/LexicalHistoryPlugin" ***!
  \******************************************************/
/***/ ((module) => {

module.exports = require("@lexical/react/LexicalHistoryPlugin");

/***/ }),

/***/ "@lexical/react/LexicalHorizontalRuleNode":
/*!***********************************************************!*\
  !*** external "@lexical/react/LexicalHorizontalRuleNode" ***!
  \***********************************************************/
/***/ ((module) => {

module.exports = require("@lexical/react/LexicalHorizontalRuleNode");

/***/ }),

/***/ "@lexical/react/LexicalHorizontalRulePlugin":
/*!*************************************************************!*\
  !*** external "@lexical/react/LexicalHorizontalRulePlugin" ***!
  \*************************************************************/
/***/ ((module) => {

module.exports = require("@lexical/react/LexicalHorizontalRulePlugin");

/***/ }),

/***/ "@lexical/react/LexicalLinkPlugin":
/*!***************************************************!*\
  !*** external "@lexical/react/LexicalLinkPlugin" ***!
  \***************************************************/
/***/ ((module) => {

module.exports = require("@lexical/react/LexicalLinkPlugin");

/***/ }),

/***/ "@lexical/react/LexicalListPlugin":
/*!***************************************************!*\
  !*** external "@lexical/react/LexicalListPlugin" ***!
  \***************************************************/
/***/ ((module) => {

module.exports = require("@lexical/react/LexicalListPlugin");

/***/ }),

/***/ "@lexical/react/LexicalMarkdownShortcutPlugin":
/*!***************************************************************!*\
  !*** external "@lexical/react/LexicalMarkdownShortcutPlugin" ***!
  \***************************************************************/
/***/ ((module) => {

module.exports = require("@lexical/react/LexicalMarkdownShortcutPlugin");

/***/ }),

/***/ "@lexical/react/LexicalOnChangePlugin":
/*!*******************************************************!*\
  !*** external "@lexical/react/LexicalOnChangePlugin" ***!
  \*******************************************************/
/***/ ((module) => {

module.exports = require("@lexical/react/LexicalOnChangePlugin");

/***/ }),

/***/ "@lexical/react/LexicalRichTextPlugin":
/*!*******************************************************!*\
  !*** external "@lexical/react/LexicalRichTextPlugin" ***!
  \*******************************************************/
/***/ ((module) => {

module.exports = require("@lexical/react/LexicalRichTextPlugin");

/***/ }),

/***/ "@lexical/react/LexicalTabIndentationPlugin":
/*!*************************************************************!*\
  !*** external "@lexical/react/LexicalTabIndentationPlugin" ***!
  \*************************************************************/
/***/ ((module) => {

module.exports = require("@lexical/react/LexicalTabIndentationPlugin");

/***/ }),

/***/ "@lexical/react/LexicalTypeaheadMenuPlugin":
/*!************************************************************!*\
  !*** external "@lexical/react/LexicalTypeaheadMenuPlugin" ***!
  \************************************************************/
/***/ ((module) => {

module.exports = require("@lexical/react/LexicalTypeaheadMenuPlugin");

/***/ }),

/***/ "@lexical/rich-text":
/*!*************************************!*\
  !*** external "@lexical/rich-text" ***!
  \*************************************/
/***/ ((module) => {

module.exports = require("@lexical/rich-text");

/***/ }),

/***/ "@lexical/selection":
/*!*************************************!*\
  !*** external "@lexical/selection" ***!
  \*************************************/
/***/ ((module) => {

module.exports = require("@lexical/selection");

/***/ }),

/***/ "@material-ui/core/locale":
/*!*******************************************!*\
  !*** external "@material-ui/core/locale" ***!
  \*******************************************/
/***/ ((module) => {

module.exports = require("@material-ui/core/locale");

/***/ }),

/***/ "lexical":
/*!**************************!*\
  !*** external "lexical" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("lexical");

/***/ }),

/***/ "lodash":
/*!*************************!*\
  !*** external "lodash" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("lodash");

/***/ }),

/***/ "next-themes":
/*!******************************!*\
  !*** external "next-themes" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("next-themes");

/***/ }),

/***/ "pupa":
/*!***********************!*\
  !*** external "pupa" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("pupa");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react-div-100vh":
/*!**********************************!*\
  !*** external "react-div-100vh" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("react-div-100vh");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "rosetta":
/*!**************************!*\
  !*** external "rosetta" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("rosetta");

/***/ }),

/***/ "styled-jsx/style":
/*!***********************************!*\
  !*** external "styled-jsx/style" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("styled-jsx/style");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./pages/test-indent.tsx"));
module.exports = __webpack_exports__;

})();