{"version": 3, "names": ["file", "traverseFast", "debug", "buildDebug", "LARGE_INPUT_SOURCEMAP_THRESHOLD", "normalizeFile", "pluginPasses", "options", "code", "ast", "type", "Error", "cloneInputAst", "cloneDeep", "parser", "inputMap", "inputSourceMap", "convertSourceMap", "fromObject", "lastComment", "extractComments", "INLINE_SOURCEMAP_REGEX", "fromComment", "err", "EXTERNAL_SOURCEMAP_REGEX", "filename", "match", "exec", "inputMapContent", "fs", "readFileSync", "path", "resolve", "dirname", "length", "fromJSON", "File", "extractCommentsFromList", "regex", "comments", "filter", "value", "test", "node", "leadingComments", "innerComments", "trailingComments"], "sources": ["../../src/transformation/normalize-file.ts"], "sourcesContent": ["import fs from \"fs\";\nimport path from \"path\";\nimport buildDebug from \"debug\";\nimport type { <PERSON><PERSON> } from \"gensync\";\nimport { file, traverseFast } from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport type { PluginPasses } from \"../config\";\nimport convertSourceMap from \"convert-source-map\";\nimport type { SourceMapConverter as Converter } from \"convert-source-map\";\nimport File from \"./file/file\";\nimport parser from \"../parser\";\nimport cloneDeep from \"./util/clone-deep\";\n\nconst debug = buildDebug(\"babel:transform:file\");\nconst LARGE_INPUT_SOURCEMAP_THRESHOLD = 3_000_000;\n\nexport type NormalizedFile = {\n  code: string;\n  ast: t.File;\n  inputMap: Converter | null;\n};\n\nexport default function* normalizeFile(\n  pluginPasses: PluginPasses,\n  options: { [key: string]: any },\n  code: string,\n  ast?: t.File | t.Program | null,\n): Handler<File> {\n  code = `${code || \"\"}`;\n\n  if (ast) {\n    if (ast.type === \"Program\") {\n      ast = file(ast, [], []);\n    } else if (ast.type !== \"File\") {\n      throw new Error(\"AST root must be a Program or File node\");\n    }\n\n    if (options.cloneInputAst) {\n      ast = cloneDeep(ast) as t.File;\n    }\n  } else {\n    // @ts-expect-error todo: use babel-types ast typings in Babel parser\n    ast = yield* parser(pluginPasses, options, code);\n  }\n\n  let inputMap = null;\n  if (options.inputSourceMap !== false) {\n    // If an explicit object is passed in, it overrides the processing of\n    // source maps that may be in the file itself.\n    if (typeof options.inputSourceMap === \"object\") {\n      inputMap = convertSourceMap.fromObject(options.inputSourceMap);\n    }\n\n    if (!inputMap) {\n      const lastComment = extractComments(INLINE_SOURCEMAP_REGEX, ast);\n      if (lastComment) {\n        try {\n          inputMap = convertSourceMap.fromComment(lastComment);\n        } catch (err) {\n          debug(\"discarding unknown inline input sourcemap\", err);\n        }\n      }\n    }\n\n    if (!inputMap) {\n      const lastComment = extractComments(EXTERNAL_SOURCEMAP_REGEX, ast);\n      if (typeof options.filename === \"string\" && lastComment) {\n        try {\n          // when `lastComment` is non-null, EXTERNAL_SOURCEMAP_REGEX must have matches\n          const match: [string, string] = EXTERNAL_SOURCEMAP_REGEX.exec(\n            lastComment,\n          ) as any;\n          const inputMapContent = fs.readFileSync(\n            path.resolve(path.dirname(options.filename), match[1]),\n          );\n          if (inputMapContent.length > LARGE_INPUT_SOURCEMAP_THRESHOLD) {\n            debug(\"skip merging input map > 1 MB\");\n          } else {\n            inputMap = convertSourceMap.fromJSON(\n              // todo:\n              inputMapContent as unknown as string,\n            );\n          }\n        } catch (err) {\n          debug(\"discarding unknown file input sourcemap\", err);\n        }\n      } else if (lastComment) {\n        debug(\"discarding un-loadable file input sourcemap\");\n      }\n    }\n  }\n\n  return new File(options, {\n    code,\n    ast: ast as t.File,\n    inputMap,\n  });\n}\n\n// These regexps are copied from the convert-source-map package,\n// but without // or /* at the beginning of the comment.\n\n// eslint-disable-next-line max-len\nconst INLINE_SOURCEMAP_REGEX =\n  /^[@#]\\s+sourceMappingURL=data:(?:application|text)\\/json;(?:charset[:=]\\S+?;)?base64,(?:.*)$/;\nconst EXTERNAL_SOURCEMAP_REGEX =\n  /^[@#][ \\t]+sourceMappingURL=([^\\s'\"`]+)[ \\t]*$/;\n\nfunction extractCommentsFromList(\n  regex: RegExp,\n  comments: t.Comment[],\n  lastComment: string | null,\n): [t.Comment[], string | null] {\n  if (comments) {\n    comments = comments.filter(({ value }) => {\n      if (regex.test(value)) {\n        lastComment = value;\n        return false;\n      }\n      return true;\n    });\n  }\n  return [comments, lastComment];\n}\n\nfunction extractComments(regex: RegExp, ast: t.Node) {\n  let lastComment: string = null;\n  traverseFast(ast, node => {\n    [node.leadingComments, lastComment] = extractCommentsFromList(\n      regex,\n      node.leadingComments,\n      lastComment,\n    );\n    [node.innerComments, lastComment] = extractCommentsFromList(\n      regex,\n      node.innerComments,\n      lastComment,\n    );\n    [node.trailingComments, lastComment] = extractCommentsFromList(\n      regex,\n      node.trailingComments,\n      lastComment,\n    );\n  });\n  return lastComment;\n}\n"], "mappings": ";;;;;;;AAAA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AACA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AACA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AAEA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AAGA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AAEA;;AACA;;AACA;;;EAPSA,I;EAAMC;;;AASf,MAAMC,KAAK,GAAGC,QAAA,CAAW,sBAAX,CAAd;;AACA,MAAMC,+BAA+B,GAAG,OAAxC;;AAQe,UAAUC,aAAV,CACbC,YADa,EAEbC,OAFa,EAGbC,IAHa,EAIbC,GAJa,EAKE;EACfD,IAAI,GAAI,GAAEA,IAAI,IAAI,EAAG,EAArB;;EAEA,IAAIC,GAAJ,EAAS;IACP,IAAIA,GAAG,CAACC,IAAJ,KAAa,SAAjB,EAA4B;MAC1BD,GAAG,GAAGT,IAAI,CAACS,GAAD,EAAM,EAAN,EAAU,EAAV,CAAV;IACD,CAFD,MAEO,IAAIA,GAAG,CAACC,IAAJ,KAAa,MAAjB,EAAyB;MAC9B,MAAM,IAAIC,KAAJ,CAAU,yCAAV,CAAN;IACD;;IAED,IAAIJ,OAAO,CAACK,aAAZ,EAA2B;MACzBH,GAAG,GAAG,IAAAI,kBAAA,EAAUJ,GAAV,CAAN;IACD;EACF,CAVD,MAUO;IAELA,GAAG,GAAG,OAAO,IAAAK,eAAA,EAAOR,YAAP,EAAqBC,OAArB,EAA8BC,IAA9B,CAAb;EACD;;EAED,IAAIO,QAAQ,GAAG,IAAf;;EACA,IAAIR,OAAO,CAACS,cAAR,KAA2B,KAA/B,EAAsC;IAGpC,IAAI,OAAOT,OAAO,CAACS,cAAf,KAAkC,QAAtC,EAAgD;MAC9CD,QAAQ,GAAGE,mBAAA,CAAiBC,UAAjB,CAA4BX,OAAO,CAACS,cAApC,CAAX;IACD;;IAED,IAAI,CAACD,QAAL,EAAe;MACb,MAAMI,WAAW,GAAGC,eAAe,CAACC,sBAAD,EAAyBZ,GAAzB,CAAnC;;MACA,IAAIU,WAAJ,EAAiB;QACf,IAAI;UACFJ,QAAQ,GAAGE,mBAAA,CAAiBK,WAAjB,CAA6BH,WAA7B,CAAX;QACD,CAFD,CAEE,OAAOI,GAAP,EAAY;UACZrB,KAAK,CAAC,2CAAD,EAA8CqB,GAA9C,CAAL;QACD;MACF;IACF;;IAED,IAAI,CAACR,QAAL,EAAe;MACb,MAAMI,WAAW,GAAGC,eAAe,CAACI,wBAAD,EAA2Bf,GAA3B,CAAnC;;MACA,IAAI,OAAOF,OAAO,CAACkB,QAAf,KAA4B,QAA5B,IAAwCN,WAA5C,EAAyD;QACvD,IAAI;UAEF,MAAMO,KAAuB,GAAGF,wBAAwB,CAACG,IAAzB,CAC9BR,WAD8B,CAAhC;;UAGA,MAAMS,eAAe,GAAGC,KAAA,CAAGC,YAAH,CACtBC,OAAA,CAAKC,OAAL,CAAaD,OAAA,CAAKE,OAAL,CAAa1B,OAAO,CAACkB,QAArB,CAAb,EAA6CC,KAAK,CAAC,CAAD,CAAlD,CADsB,CAAxB;;UAGA,IAAIE,eAAe,CAACM,MAAhB,GAAyB9B,+BAA7B,EAA8D;YAC5DF,KAAK,CAAC,+BAAD,CAAL;UACD,CAFD,MAEO;YACLa,QAAQ,GAAGE,mBAAA,CAAiBkB,QAAjB,CAETP,eAFS,CAAX;UAID;QACF,CAhBD,CAgBE,OAAOL,GAAP,EAAY;UACZrB,KAAK,CAAC,yCAAD,EAA4CqB,GAA5C,CAAL;QACD;MACF,CApBD,MAoBO,IAAIJ,WAAJ,EAAiB;QACtBjB,KAAK,CAAC,6CAAD,CAAL;MACD;IACF;EACF;;EAED,OAAO,IAAIkC,aAAJ,CAAS7B,OAAT,EAAkB;IACvBC,IADuB;IAEvBC,GAAG,EAAEA,GAFkB;IAGvBM;EAHuB,CAAlB,CAAP;AAKD;;AAMD,MAAMM,sBAAsB,GAC1B,8FADF;AAEA,MAAMG,wBAAwB,GAC5B,gDADF;;AAGA,SAASa,uBAAT,CACEC,KADF,EAEEC,QAFF,EAGEpB,WAHF,EAIgC;EAC9B,IAAIoB,QAAJ,EAAc;IACZA,QAAQ,GAAGA,QAAQ,CAACC,MAAT,CAAgB,CAAC;MAAEC;IAAF,CAAD,KAAe;MACxC,IAAIH,KAAK,CAACI,IAAN,CAAWD,KAAX,CAAJ,EAAuB;QACrBtB,WAAW,GAAGsB,KAAd;QACA,OAAO,KAAP;MACD;;MACD,OAAO,IAAP;IACD,CANU,CAAX;EAOD;;EACD,OAAO,CAACF,QAAD,EAAWpB,WAAX,CAAP;AACD;;AAED,SAASC,eAAT,CAAyBkB,KAAzB,EAAwC7B,GAAxC,EAAqD;EACnD,IAAIU,WAAmB,GAAG,IAA1B;EACAlB,YAAY,CAACQ,GAAD,EAAMkC,IAAI,IAAI;IACxB,CAACA,IAAI,CAACC,eAAN,EAAuBzB,WAAvB,IAAsCkB,uBAAuB,CAC3DC,KAD2D,EAE3DK,IAAI,CAACC,eAFsD,EAG3DzB,WAH2D,CAA7D;IAKA,CAACwB,IAAI,CAACE,aAAN,EAAqB1B,WAArB,IAAoCkB,uBAAuB,CACzDC,KADyD,EAEzDK,IAAI,CAACE,aAFoD,EAGzD1B,WAHyD,CAA3D;IAKA,CAACwB,IAAI,CAACG,gBAAN,EAAwB3B,WAAxB,IAAuCkB,uBAAuB,CAC5DC,KAD4D,EAE5DK,IAAI,CAACG,gBAFuD,EAG5D3B,WAH4D,CAA9D;EAKD,CAhBW,CAAZ;EAiBA,OAAOA,WAAP;AACD"}