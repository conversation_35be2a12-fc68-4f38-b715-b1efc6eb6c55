"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./components/editor/lexical-editor.tsx":
/*!**********************************************!*\
  !*** ./components/editor/lexical-editor.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @swc/helpers/src/_sliced_to_array.mjs */ \"./node_modules/@swc/helpers/src/_sliced_to_array.mjs\");\n/* harmony import */ var _swc_helpers_src_to_consumable_array_mjs__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @swc/helpers/src/_to_consumable_array.mjs */ \"./node_modules/@swc/helpers/src/_to_consumable_array.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! lexical */ \"./node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalComposer__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @lexical/react/LexicalComposer */ \"./node_modules/@lexical/react/LexicalComposer.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"./node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalRichTextPlugin__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @lexical/react/LexicalRichTextPlugin */ \"./node_modules/@lexical/react/LexicalRichTextPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalContentEditable__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @lexical/react/LexicalContentEditable */ \"./node_modules/@lexical/react/LexicalContentEditable.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalHistoryPlugin__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @lexical/react/LexicalHistoryPlugin */ \"./node_modules/@lexical/react/LexicalHistoryPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalAutoFocusPlugin__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @lexical/react/LexicalAutoFocusPlugin */ \"./node_modules/@lexical/react/LexicalAutoFocusPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalOnChangePlugin__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! @lexical/react/LexicalOnChangePlugin */ \"./node_modules/@lexical/react/LexicalOnChangePlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalLinkPlugin__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @lexical/react/LexicalLinkPlugin */ \"./node_modules/@lexical/react/LexicalLinkPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalListPlugin__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @lexical/react/LexicalListPlugin */ \"./node_modules/@lexical/react/LexicalListPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalMarkdownShortcutPlugin__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! @lexical/react/LexicalMarkdownShortcutPlugin */ \"./node_modules/@lexical/react/LexicalMarkdownShortcutPlugin.dev.mjs\");\n/* harmony import */ var _lexical_markdown__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @lexical/markdown */ \"./node_modules/@lexical/markdown/LexicalMarkdown.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalErrorBoundary__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @lexical/react/LexicalErrorBoundary */ \"./node_modules/@lexical/react/LexicalErrorBoundary.dev.mjs\");\n/* harmony import */ var _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @lexical/rich-text */ \"./node_modules/@lexical/rich-text/LexicalRichText.dev.mjs\");\n/* harmony import */ var _lexical_list__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @lexical/list */ \"./node_modules/@lexical/list/LexicalList.dev.mjs\");\n/* harmony import */ var _lexical_code__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @lexical/code */ \"./node_modules/@lexical/code/LexicalCode.dev.mjs\");\n/* harmony import */ var _lexical_link__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @lexical/link */ \"./node_modules/@lexical/link/LexicalLink.dev.mjs\");\n/* harmony import */ var react_div_100vh__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-div-100vh */ \"./node_modules/react-div-100vh/dist/esm/index.js\");\n/* harmony import */ var libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! libs/web/hooks/use-mounted */ \"./libs/web/hooks/use-mounted.ts\");\n/* harmony import */ var libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! libs/web/hooks/use-i18n */ \"./libs/web/hooks/use-i18n.tsx\");\n/* harmony import */ var _plugins_slash_commands_plugin__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./plugins/slash-commands-plugin */ \"./components/editor/plugins/slash-commands-plugin.tsx\");\n/* harmony import */ var _plugins_floating_toolbar_plugin__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./plugins/floating-toolbar-plugin */ \"./components/editor/plugins/floating-toolbar-plugin.tsx\");\n/* harmony import */ var _lexical_react_LexicalCheckListPlugin__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @lexical/react/LexicalCheckListPlugin */ \"./node_modules/@lexical/react/LexicalCheckListPlugin.dev.mjs\");\n/* harmony import */ var _plugins_highlight_plugin__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./plugins/highlight-plugin */ \"./components/editor/plugins/highlight-plugin.tsx\");\n/* harmony import */ var _plugins_image_plugin__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./plugins/image-plugin */ \"./components/editor/plugins/image-plugin.tsx\");\n/* harmony import */ var _plugins_ime_plugin__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./plugins/ime-plugin */ \"./components/editor/plugins/ime-plugin.tsx\");\n/* harmony import */ var _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./nodes/image-node */ \"./components/editor/nodes/image-node.tsx\");\n/* harmony import */ var _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @lexical/react/LexicalHorizontalRuleNode */ \"./node_modules/@lexical/react/LexicalHorizontalRuleNode.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalHorizontalRulePlugin__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! @lexical/react/LexicalHorizontalRulePlugin */ \"./node_modules/@lexical/react/LexicalHorizontalRulePlugin.dev.mjs\");\n/**\n * Lexical Editor Component\n * Migrated from TipTap to Lexical for better performance and modern architecture\n */ \n\nvar _this = undefined;\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Import custom plugins and nodes\n\n\n\n\n\n\n\n\n\nvar theme = {\n    ltr: \"ltr\",\n    rtl: \"rtl\",\n    placeholder: \"editor-placeholder\",\n    paragraph: \"editor-paragraph\",\n    quote: \"editor-quote\",\n    heading: {\n        h1: \"editor-heading-h1\",\n        h2: \"editor-heading-h2\",\n        h3: \"editor-heading-h3\",\n        h4: \"editor-heading-h4\",\n        h5: \"editor-heading-h5\",\n        h6: \"editor-heading-h6\"\n    },\n    list: {\n        nested: {\n            listitem: \"editor-nested-listitem\"\n        },\n        ol: \"editor-list-ol\",\n        ul: \"editor-list-ul\",\n        listitem: \"editor-listitem\",\n        // 按深度定义不同的列表样式 - 这是关键！\n        olDepth: [\n            \"editor-list-ol-1\",\n            \"editor-list-ol-2\",\n            \"editor-list-ol-3\",\n            \"editor-list-ol-4\",\n            \"editor-list-ol-5\" // I, II, III...\n        ],\n        ulDepth: [\n            \"editor-list-ul-1\",\n            \"editor-list-ul-2\",\n            \"editor-list-ul-3\",\n            \"editor-list-ul-4\",\n            \"editor-list-ul-5\" // ‣\n        ],\n        checklist: \"PlaygroundEditorTheme__checklist\",\n        listitemChecked: \"PlaygroundEditorTheme__listItemChecked\",\n        listitemUnchecked: \"PlaygroundEditorTheme__listItemUnchecked\"\n    },\n    // Lexical 0.32.1 原生缩进支持\n    indent: \"lexical-indent\",\n    image: \"editor-image\",\n    link: \"editor-link\",\n    text: {\n        bold: \"editor-text-bold\",\n        italic: \"editor-text-italic\",\n        overflowed: \"editor-text-overflowed\",\n        hashtag: \"editor-text-hashtag\",\n        underline: \"editor-text-underline\",\n        strikethrough: \"editor-text-strikethrough\",\n        underlineStrikethrough: \"editor-text-underlineStrikethrough\",\n        code: \"editor-text-code\",\n        highlight: \"editor-text-highlight\"\n    },\n    code: \"editor-code\",\n    codeHighlight: {\n        atrule: \"editor-tokenAttr\",\n        attr: \"editor-tokenAttr\",\n        boolean: \"editor-tokenProperty\",\n        builtin: \"editor-tokenSelector\",\n        cdata: \"editor-tokenComment\",\n        char: \"editor-tokenSelector\",\n        class: \"editor-tokenFunction\",\n        \"class-name\": \"editor-tokenFunction\",\n        comment: \"editor-tokenComment\",\n        constant: \"editor-tokenProperty\",\n        deleted: \"editor-tokenProperty\",\n        doctype: \"editor-tokenComment\",\n        entity: \"editor-tokenOperator\",\n        function: \"editor-tokenFunction\",\n        important: \"editor-tokenVariable\",\n        inserted: \"editor-tokenSelector\",\n        keyword: \"editor-tokenAttr\",\n        namespace: \"editor-tokenVariable\",\n        number: \"editor-tokenProperty\",\n        operator: \"editor-tokenOperator\",\n        prolog: \"editor-tokenComment\",\n        property: \"editor-tokenProperty\",\n        punctuation: \"editor-tokenPunctuation\",\n        regex: \"editor-tokenVariable\",\n        selector: \"editor-tokenSelector\",\n        string: \"editor-tokenSelector\",\n        symbol: \"editor-tokenProperty\",\n        tag: \"editor-tokenProperty\",\n        url: \"editor-tokenOperator\",\n        variable: \"editor-tokenVariable\"\n    }\n};\nfunction Placeholder() {\n    _s();\n    var t = (0,libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__[\"default\"])().t;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"editor-placeholder\",\n        children: t(\"Start writing...\")\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n        lineNumber: 153,\n        columnNumber: 12\n    }, this);\n}\n_s(Placeholder, \"82N5KF9nLzZ6+2WH7KIjzIXRkLw=\", false, function() {\n    return [\n        libs_web_hooks_use_i18n__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    ];\n});\n_c = Placeholder;\nvar LexicalEditor = /*#__PURE__*/ _s1((0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(_c1 = _s1(function(param, ref) {\n    var _readOnly = param.readOnly, readOnly = _readOnly === void 0 ? false : _readOnly, _value = param.value, value = _value === void 0 ? \"\" : _value, onChange = param.onChange, onClickLink = param.onClickLink, onHoverLink = param.onHoverLink, _className = param.className, className = _className === void 0 ? \"\" : _className;\n    _s1();\n    var _s = $RefreshSig$(), _s2 = $RefreshSig$();\n    var height = (0,react_div_100vh__WEBPACK_IMPORTED_MODULE_4__.use100vh)();\n    var mounted = (0,libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    var initialConfig = {\n        namespace: \"LexicalEditor\",\n        theme: theme,\n        onError: function onError(error) {\n            console.error(\"Lexical Error:\", error);\n        },\n        nodes: [\n            _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.HeadingNode,\n            _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListNode,\n            _lexical_list__WEBPACK_IMPORTED_MODULE_14__.ListItemNode,\n            _lexical_rich_text__WEBPACK_IMPORTED_MODULE_13__.QuoteNode,\n            _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeNode,\n            _lexical_code__WEBPACK_IMPORTED_MODULE_3__.CodeHighlightNode,\n            _lexical_link__WEBPACK_IMPORTED_MODULE_15__.AutoLinkNode,\n            _lexical_link__WEBPACK_IMPORTED_MODULE_15__.LinkNode,\n            // Lexical原生的ListItemNode已经支持checkbox功能\n            // 使用Lexical内置的highlight格式，不需要自定义HighlightNode\n            _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.ImageNode,\n            _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.HorizontalRuleNode, \n        ],\n        editable: !readOnly,\n        // 设置初始内容\n        editorState: value ? undefined : null\n    };\n    // 创建自定义transformers，包含图片支持\n    var IMAGE_TRANSFORMER = {\n        dependencies: [\n            _nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.ImageNode\n        ],\n        export: function(node) {\n            if (!(0,_nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.$isImageNode)(node)) {\n                return null;\n            }\n            return \"![\".concat(node.getAltText(), \"](\").concat(node.getSrc(), \")\");\n        },\n        regExp: /!\\[([^\\]]*)\\]\\(([^)]+)\\)/,\n        replace: function(parentNode, children, match) {\n            var _match = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_17__[\"default\"])(match, 3), altText = _match[1], src = _match[2];\n            var imageNode = (0,_nodes_image_node__WEBPACK_IMPORTED_MODULE_12__.$createImageNode)({\n                altText: altText,\n                src: src,\n                maxWidth: 800\n            });\n            children.forEach(function(child) {\n                return child.remove();\n            });\n            parentNode.append(imageNode);\n        },\n        type: \"element\"\n    };\n    // 创建自定义的下划线转换器，使用 <u>text</u> 语法\n    var UNDERLINE_TRANSFORMER = {\n        format: [\n            \"underline\"\n        ],\n        tag: \"<u>\",\n        type: \"text-format\"\n    };\n    // 移除段落缩进转换器 - 专注于列表缩进功能\n    // 段落缩进不是标准markdown语法，我们只处理列表缩进\n    // 创建水平分割线转换器\n    var HR_TRANSFORMER = {\n        dependencies: [\n            _lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.HorizontalRuleNode\n        ],\n        export: function(node) {\n            return (0,_lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.$isHorizontalRuleNode)(node) ? \"---\" : null;\n        },\n        regExp: /^(---|\\*\\*\\*|___)\\s?$/,\n        replace: function(parentNode, children, match, isImport) {\n            var line = (0,_lexical_react_LexicalHorizontalRuleNode__WEBPACK_IMPORTED_MODULE_16__.$createHorizontalRuleNode)();\n            if (isImport || parentNode.getNextSibling() != null) {\n                parentNode.replace(line);\n            } else {\n                parentNode.insertBefore(line);\n            }\n            line.selectNext();\n        },\n        type: \"element\"\n    };\n    // 🔧 JSON格式保存 - 完全使用Lexical原生格式\n    // 使用标准的Lexical transformers\n    var customTransformers = (0,_swc_helpers_src_to_consumable_array_mjs__WEBPACK_IMPORTED_MODULE_18__[\"default\"])(_lexical_markdown__WEBPACK_IMPORTED_MODULE_19__.TRANSFORMERS).concat([\n        // 添加我们的自定义转换器\n        HR_TRANSFORMER,\n        UNDERLINE_TRANSFORMER,\n        IMAGE_TRANSFORMER\n    ]);\n    // 移除了自动完成插件 - 现在通过正确的transformer优先级来解决问题\n    var handleChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(editorState, _editor, tags) {\n        if (onChange) {\n            // 模仿TipTap的简单检查：忽略历史合并和内容同步触发的更新\n            if (tags.has(\"history-merge\") || tags.has(\"content-sync\")) {\n                return;\n            }\n            editorState.read(function() {\n                try {\n                    // 🔧 添加调试：检查编辑器状态中的列表结构\n                    var root = (0,lexical__WEBPACK_IMPORTED_MODULE_20__.$getRoot)();\n                    var children = root.getChildren();\n                    console.log(\"\\uD83D\\uDD27 编辑器根节点子元素数量:\", children.length);\n                    children.forEach(function(child, index) {\n                        if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListNode)(child)) {\n                            console.log(\"\\uD83D\\uDD27 第\".concat(index, \"个子元素是列表:\"), {\n                                type: child.getListType(),\n                                childrenCount: child.getChildren().length\n                            });\n                            var listItems = child.getChildren();\n                            listItems.forEach(function(item, itemIndex) {\n                                if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListItemNode)(item)) {\n                                    var itemChildren = item.getChildren();\n                                    console.log(\"\\uD83D\\uDD27   列表项\".concat(itemIndex, \":\"), {\n                                        childrenCount: itemChildren.length,\n                                        textContent: item.getTextContent(),\n                                        hasNestedList: itemChildren.some(function(c) {\n                                            return (0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListNode)(c);\n                                        })\n                                    });\n                                }\n                            });\n                        }\n                    });\n                    // 🔧 新方案：使用Lexical原生JSON格式保存，完美保持嵌套关系\n                    var editorStateJSON = JSON.stringify(editorState.toJSON());\n                    console.log(\"\\uD83D\\uDD27 Lexical原生JSON格式:\");\n                    console.log(editorStateJSON);\n                    // 简单的内容变化检查（比较JSON字符串）\n                    if (editorStateJSON !== value) {\n                        console.log(\"\\uD83D\\uDD27 编辑器状态改变，保存JSON格式\");\n                        onChange(function() {\n                            return editorStateJSON;\n                        });\n                        return;\n                    }\n                    // 如果需要Markdown格式（比如导出），可以单独生成\n                    var markdownContent = (0,_lexical_markdown__WEBPACK_IMPORTED_MODULE_19__.$convertToMarkdownString)(customTransformers);\n                    console.log(\"\\uD83D\\uDD27 Markdown格式（仅用于显示/导出）:\");\n                    console.log(markdownContent);\n                    // 调试：检查是否包含checkbox语法\n                    if (markdownContent.includes(\"[ ]\") || markdownContent.includes(\"[x]\")) {\n                        console.log(\"\\uD83D\\uDD0D Checkbox detected in markdown\");\n                    }\n                    // 不做任何额外处理，保持Lexical原生的markdown输出\n                    // Lexical的transformers已经正确处理了列表、checkbox等格式\n                    // 简单的内容变化检查\n                    if (markdownContent !== value) {\n                        console.log(\"\\uD83D\\uDD0D Content changed, calling onChange\");\n                        onChange(function() {\n                            return markdownContent;\n                        });\n                    }\n                } catch (error) {\n                    console.error(\"\\uD83D\\uDD0D Error in markdown conversion:\", error);\n                // 如果转换出错，保持原有内容不变\n                }\n            });\n        }\n    }, [\n        onChange,\n        value\n    ]);\n    // 列表退出处理插件 - 处理Enter+Enter退出列表的逻辑\n    var ListExitPlugin = function() {\n        _s();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_17__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            return editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_20__.KEY_ENTER_COMMAND, function(event) {\n                var selection = (0,lexical__WEBPACK_IMPORTED_MODULE_20__.$getSelection)();\n                if (!(0,lexical__WEBPACK_IMPORTED_MODULE_20__.$isRangeSelection)(selection)) {\n                    return false;\n                }\n                var anchorNode = selection.anchor.getNode();\n                // 检查是否在空的列表项中\n                if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListItemNode)(anchorNode)) {\n                    var textContent = anchorNode.getTextContent().trim();\n                    if (textContent === \"\") {\n                        var listNode = anchorNode.getParent();\n                        if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_14__.$isListNode)(listNode)) {\n                            // 如果是空的列表项，退出列表\n                            event === null || event === void 0 ? void 0 : event.preventDefault();\n                            // 创建新段落并在列表后插入\n                            var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_20__.$createParagraphNode)();\n                            listNode.insertAfter(paragraph);\n                            // 删除空的列表项\n                            anchorNode.remove();\n                            // 选中新段落\n                            paragraph.select();\n                            return true;\n                        }\n                    }\n                }\n                return false;\n            }, lexical__WEBPACK_IMPORTED_MODULE_20__.COMMAND_PRIORITY_HIGH);\n        }, [\n            editor\n        ]);\n        return null;\n    };\n    _s(ListExitPlugin, \"mCqe7sh4aC9mLBXPHfG3d/PNTaQ=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext\n        ];\n    });\n    // 内容同步组件 - 模仿TipTap的方式\n    var ContentSyncPlugin = function() {\n        _s2();\n        var _$ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_17__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext)(), 1), editor = _$ref[0];\n        (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n            if (editor && value !== undefined && mounted) {\n                // 使用setTimeout来避免在渲染过程中调用flushSync\n                setTimeout(function() {\n                    if (value.trim()) {\n                        try {\n                            // 🔧 解析JSON格式的编辑器状态\n                            var editorStateData = JSON.parse(value);\n                            console.log(\"\\uD83D\\uDD27 加载Lexical JSON格式内容\");\n                            // 直接设置编辑器状态\n                            var newEditorState = editor.parseEditorState(editorStateData);\n                            editor.setEditorState(newEditorState);\n                        } catch (jsonError) {\n                            console.error(\"\\uD83D\\uDD27 JSON解析失败:\", jsonError);\n                            // 创建空段落作为fallback\n                            editor.update(function() {\n                                var root = (0,lexical__WEBPACK_IMPORTED_MODULE_20__.$getRoot)();\n                                root.clear();\n                                var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_20__.$createParagraphNode)();\n                                root.append(paragraph);\n                            });\n                        }\n                    } else {\n                        // 空内容时清空并创建一个空段落\n                        editor.update(function() {\n                            var root = (0,lexical__WEBPACK_IMPORTED_MODULE_20__.$getRoot)();\n                            root.clear();\n                            var paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_20__.$createParagraphNode)();\n                            root.append(paragraph);\n                        });\n                    }\n                }, 0);\n            }\n        }, [\n            editor,\n            value,\n            mounted\n        ]);\n        return null;\n    };\n    _s2(ContentSyncPlugin, \"mCqe7sh4aC9mLBXPHfG3d/PNTaQ=\", false, function() {\n        return [\n            _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_21__.useLexicalComposerContext\n        ];\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useImperativeHandle)(ref, function() {\n        return {\n            focusAtEnd: function() {\n            // TODO: Implement focus at end\n            },\n            focusAtStart: function() {\n            // TODO: Implement focus at start\n            }\n        };\n    });\n    if (!mounted) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n            [\n                \"91be92fd7d9e7d9b\",\n                [\n                    height ? height + \"px\" : \"100vh\"\n                ]\n            ]\n        ]) + \" \" + \"lexical-editor \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalComposer__WEBPACK_IMPORTED_MODULE_22__.LexicalComposer, {\n                initialConfig: initialConfig,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                        [\n                            \"91be92fd7d9e7d9b\",\n                            [\n                                height ? height + \"px\" : \"100vh\"\n                            ]\n                        ]\n                    ]) + \" \" + \"editor-container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalRichTextPlugin__WEBPACK_IMPORTED_MODULE_23__.RichTextPlugin, {\n                            contentEditable: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalContentEditable__WEBPACK_IMPORTED_MODULE_24__.ContentEditable, {\n                                className: \"editor-input focus:outline-none w-full\",\n                                spellCheck: false\n                            }, void 0, false, void 0, void 0),\n                            placeholder: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Placeholder, {}, void 0, false, void 0, void 0),\n                            ErrorBoundary: _lexical_react_LexicalErrorBoundary__WEBPACK_IMPORTED_MODULE_25__.LexicalErrorBoundary\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 440,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalHistoryPlugin__WEBPACK_IMPORTED_MODULE_26__.HistoryPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 450,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalAutoFocusPlugin__WEBPACK_IMPORTED_MODULE_27__.AutoFocusPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 451,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalLinkPlugin__WEBPACK_IMPORTED_MODULE_28__.LinkPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 452,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalListPlugin__WEBPACK_IMPORTED_MODULE_29__.ListPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 453,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalCheckListPlugin__WEBPACK_IMPORTED_MODULE_30__.CheckListPlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 454,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalMarkdownShortcutPlugin__WEBPACK_IMPORTED_MODULE_31__.MarkdownShortcutPlugin, {\n                            transformers: customTransformers\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 455,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_slash_commands_plugin__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 456,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_floating_toolbar_plugin__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_highlight_plugin__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 458,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_image_plugin__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 459,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalHorizontalRulePlugin__WEBPACK_IMPORTED_MODULE_32__.HorizontalRulePlugin, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 460,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_plugins_ime_plugin__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            enabled: true,\n                            debug: \"development\" === \"development\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 461,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ListExitPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"91be92fd7d9e7d9b\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 463,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ContentSyncPlugin, {\n                            className: styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default().dynamic([\n                                [\n                                    \"91be92fd7d9e7d9b\",\n                                    [\n                                        height ? height + \"px\" : \"100vh\"\n                                    ]\n                                ]\n                            ])\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 21\n                        }, _this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lexical_react_LexicalOnChangePlugin__WEBPACK_IMPORTED_MODULE_33__.OnChangePlugin, {\n                            onChange: handleChange,\n                            ignoreHistoryMergeTagChange: true,\n                            ignoreSelectionChange: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 21\n                        }, _this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                    lineNumber: 439,\n                    columnNumber: 17\n                }, _this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n                lineNumber: 438,\n                columnNumber: 13\n            }, _this),\n            (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"91be92fd7d9e7d9b\",\n                dynamic: [\n                    height ? height + \"px\" : \"100vh\"\n                ],\n                children: \".lexical-editor{position:relative}.editor-container{position:relative}.editor-input{outline:none;padding:1rem 0;min-height:-webkit-calc(\".concat(height ? height + \"px\" : \"100vh\", \" - 14rem);min-height:-moz-calc(\").concat(height ? height + \"px\" : \"100vh\", \" - 14rem);min-height:calc(\").concat(height ? height + \"px\" : \"100vh\", ' - 14rem);padding-bottom:10rem;width:100%;max-width:none;line-height:1.7;font-size:1rem;color:inherit;-webkit-spellcheck:false;-moz-spellcheck:false;-ms-spellcheck:false;spellcheck:false}.lexical-editor{--lexical-indent-base-value:32px}.lexical-indent{--lexical-indent-base-value:32px}.editor-input p[style*=\"margin-left\"],.editor-input h1[style*=\"margin-left\"],.editor-input h2[style*=\"margin-left\"],.editor-input h3[style*=\"margin-left\"],.editor-input h4[style*=\"margin-left\"],.editor-input h5[style*=\"margin-left\"],.editor-input h6[style*=\"margin-left\"],.editor-input li[style*=\"margin-left\"]{-webkit-transition:margin-left.2s ease;-moz-transition:margin-left.2s ease;-o-transition:margin-left.2s ease;transition:margin-left.2s ease}.editor-placeholder{color:#999;overflow:hidden;position:absolute;-o-text-overflow:ellipsis;text-overflow:ellipsis;top:1rem;left:0;font-size:1rem;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;display:inline-block;pointer-events:none}.editor-paragraph{margin:1.2rem 0;line-height:1.8}.editor-heading-h1{font-size:2.8em;font-weight:bold;margin:1.5rem 0 1rem 0;line-height:1.2}.editor-heading-h2{font-size:2.2em;font-weight:bold;margin:1.4rem 0 .8rem 0;line-height:1.3}.editor-heading-h3{font-size:1.8em;font-weight:bold;margin:1.3rem 0 .6rem 0;line-height:1.4}.editor-heading-h4{font-size:1.5em;font-weight:bold;margin:1.2rem 0 .5rem 0;line-height:1.4}.editor-heading-h5{font-size:1.3em;font-weight:bold;margin:1.1rem 0 .4rem 0;line-height:1.5}.editor-heading-h6{font-size:1.2em;font-weight:bold;margin:1rem 0 .3rem 0;line-height:1.5}.editor-quote{margin:1rem 0;padding-left:1rem;border-left:4px solid#ccc;font-style:italic;color:#666}.editor-list-ol,.editor-list-ul{margin:1rem 0;padding-left:2rem}.editor-listitem{margin:.5rem 0}.editor-link{color:#3b82f6;text-decoration:underline;cursor:pointer}.editor-link:hover{color:#1d4ed8}.editor-text-bold{font-weight:bold}.editor-text-italic{font-style:italic}.editor-text-underline{text-decoration:underline}.editor-text-strikethrough{text-decoration:line-through}.editor-text-code{background-color:#e4e4e7;color:black;padding:.2rem .4rem;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;font-family:monospace;font-size:.9em}.editor-code{background-color:#e4e4e7;color:black;border:1px solid#e9ecef;-webkit-border-radius:.375rem;-moz-border-radius:.375rem;border-radius:.375rem;padding:1rem;margin:1rem 0;font-family:\"Courier New\",Courier,monospace;font-size:.9em;line-height:1.4;overflow-x:auto}[data-theme=\"dark\"] .editor-text-code{background-color:#3f3f46;color:white}[data-theme=\"dark\"] .editor-code{background-color:#3f3f46;color:white;border-color:#4b5563}.editor-list-ol{list-style-type:decimal;margin:.5rem 0;padding-left:1.5rem}.editor-list-ul{list-style-type:disc;margin:.5rem 0;padding-left:1.5rem}.editor-listitem{margin:.4rem 0;line-height:1.8}.editor-nested-listitem{margin:.3rem 0}.editor-list-ol-1{list-style-type:decimal}.editor-list-ol-2{list-style-type:lower-alpha}.editor-list-ol-3{list-style-type:lower-roman}.editor-list-ol-4{list-style-type:upper-alpha}.editor-list-ol-5{list-style-type:upper-roman}.editor-list-ul-1{list-style-type:disc}.editor-list-ul-2{list-style-type:circle}.editor-list-ul-3{list-style-type:square}.editor-list-ul-4{list-style-type:disc}.editor-list-ul-5{list-style-type:circle}.task-list{list-style:none;padding-left:0;margin:1rem 0}.task-item{display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:start;-webkit-align-items:flex-start;-moz-box-align:start;-ms-flex-align:start;align-items:flex-start;margin:.5rem 0;list-style:none}.task-checkbox{margin-right:.5rem;margin-top:.125rem;cursor:pointer;width:1rem;height:1rem;border:1px solid#d1d5db;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;background:white;display:-webkit-box;display:-webkit-flex;display:-moz-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-moz-box-align:center;-ms-flex-align:center;align-items:center;-webkit-box-pack:center;-webkit-justify-content:center;-moz-box-pack:center;-ms-flex-pack:center;justify-content:center;-webkit-flex-shrink:0;-ms-flex-negative:0;flex-shrink:0;-moz-appearance:none;-ms-appearance:none;appearance:none;-webkit-appearance:none}.task-checkbox:checked{background-color:#3b82f6;border-color:#3b82f6;color:white}.task-checkbox:checked::after{content:\"✓\";font-size:.75rem;font-weight:bold;color:white}.task-content{-webkit-box-flex:1;-webkit-flex:1;-moz-box-flex:1;-ms-flex:1;flex:1;line-height:1.7}.task-item[data-checked=\"true\"] .task-content{text-decoration:line-through;opacity:.6}.lexical-highlight,mark,.editor-text-highlight{background-color:#eab834!important;color:black!important;padding:.1rem .2rem;-webkit-border-radius:.125rem;-moz-border-radius:.125rem;border-radius:.125rem}.dark .lexical-highlight,.dark mark,.dark .editor-text-highlight,[data-theme=\"dark\"] .lexical-highlight,[data-theme=\"dark\"] mark,[data-theme=\"dark\"] .editor-text-highlight,html.dark .lexical-highlight,html.dark mark,html.dark .editor-text-highlight{background-color:#3185eb!important;color:white!important}.editor-image img{max-width:100%;height:auto;-webkit-border-radius:.5rem;-moz-border-radius:.5rem;border-radius:.5rem;margin:1rem 0}@media(prefers-color-scheme:dark){.editor-placeholder{color:#6b7280}.editor-quote{border-left-color:#4b5563;color:#9ca3af}.editor-text-code{background-color:#374151;color:#f9fafb}.editor-code{background-color:#1f2937;border-color:#374151;color:#f9fafb}}.lexical-editor ul:not([data-lexical-list-type=\"check\"]) li::marker{color:#6b7280}.lexical-editor ol li::marker{color:#6b7280}.lexical-editor .PlaygroundEditorTheme__listItemChecked,.lexical-editor .PlaygroundEditorTheme__listItemUnchecked{position:relative;margin-left:.5em;margin-right:.5em;padding-left:1.5em;padding-right:1.5em;list-style-type:none;outline:none;display:block;min-height:1.5em}.lexical-editor .PlaygroundEditorTheme__listItemChecked>*,.lexical-editor .PlaygroundEditorTheme__listItemUnchecked>*{margin-left:.01em}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before,.lexical-editor .PlaygroundEditorTheme__listItemChecked:before{content:\"\";width:.9em;height:.9em;top:50%;left:0;cursor:pointer;display:block;-webkit-background-size:cover;-moz-background-size:cover;-o-background-size:cover;background-size:cover;position:absolute;-webkit-transform:translateY(-50%);-moz-transform:translateY(-50%);-ms-transform:translateY(-50%);-o-transform:translateY(-50%);transform:translateY(-50%)}.lexical-editor .PlaygroundEditorTheme__listItemChecked{text-decoration:line-through}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:focus:before,.lexical-editor .PlaygroundEditorTheme__listItemChecked:focus:before{-webkit-box-shadow:0 0 0 2px#a6cdfe;-moz-box-shadow:0 0 0 2px#a6cdfe;box-shadow:0 0 0 2px#a6cdfe;-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px}.lexical-editor .PlaygroundEditorTheme__listItemUnchecked:before{border:1px solid#999;-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px}.lexical-editor .PlaygroundEditorTheme__listItemChecked:before{border:1px solid rgb(61,135,245);-webkit-border-radius:2px;-moz-border-radius:2px;border-radius:2px;background-color:#3d87f5;background-repeat:no-repeat}.lexical-editor .PlaygroundEditorTheme__listItemChecked:after{content:\"\";cursor:pointer;border-color:#fff;border-style:solid;position:absolute;display:block;top:45%;width:.2em;left:.35em;height:.4em;-webkit-transform:translateY(-50%)rotate(45deg);-moz-transform:translateY(-50%)rotate(45deg);-ms-transform:translateY(-50%)rotate(45deg);-o-transform:translateY(-50%)rotate(45deg);transform:translateY(-50%)rotate(45deg);border-width:0 .1em .1em 0}')\n            }, void 0, false, void 0, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\lexical-editor.tsx\",\n        lineNumber: 437,\n        columnNumber: 9\n    }, _this);\n}, \"cp2mgoBaCDop5vv5Ezf6hVUYfiE=\", false, function() {\n    return [\n        libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n})), \"cp2mgoBaCDop5vv5Ezf6hVUYfiE=\", false, function() {\n    return [\n        libs_web_hooks_use_mounted__WEBPACK_IMPORTED_MODULE_5__[\"default\"]\n    ];\n});\n_c2 = LexicalEditor;\nLexicalEditor.displayName = \"LexicalEditor\";\n/* harmony default export */ __webpack_exports__[\"default\"] = (LexicalEditor);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Placeholder\");\n$RefreshReg$(_c1, \"LexicalEditor$forwardRef\");\n$RefreshReg$(_c2, \"LexicalEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/editor/lexical-editor.tsx\n"));

/***/ })

});