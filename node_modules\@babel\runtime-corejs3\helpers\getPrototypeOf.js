var _Object$setPrototypeOf = require("@babel/runtime-corejs3/core-js/object/set-prototype-of");
var _bindInstanceProperty = require("@babel/runtime-corejs3/core-js/instance/bind");
var _Object$getPrototypeOf = require("@babel/runtime-corejs3/core-js/object/get-prototype-of");
function _getPrototypeOf(o) {
  var _context;
  module.exports = _getPrototypeOf = _Object$setPrototypeOf ? _bindInstanceProperty(_context = _Object$getPrototypeOf).call(_context) : function _getPrototypeOf(o) {
    return o.__proto__ || _Object$getPrototypeOf(o);
  }, module.exports.__esModule = true, module.exports["default"] = module.exports;
  return _getPrototypeOf(o);
}
module.exports = _getPrototypeOf, module.exports.__esModule = true, module.exports["default"] = module.exports;