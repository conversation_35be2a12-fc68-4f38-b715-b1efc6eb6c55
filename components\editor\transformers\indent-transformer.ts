/**
 * Custom Indent Transformer for Lexical
 * Handles saving and restoring indentation in markdown format
 * Preserves both paragraph and list indentation with proper parent-child relationships
 */

import {
    ElementTransformer,
} from '@lexical/markdown';
import {
    $createParagraphNode,
    $isParagraphNode,
    ElementNode,
    LexicalNode,
} from 'lexical';
import { $createHeadingNode, $isHeadingNode, HeadingTagType } from '@lexical/rich-text';
import { $createListItemNode, $createListNode, $isListItemNode, $isListNode } from '@lexical/list';

// 缩进标记符号 - 使用特殊的markdown语法来保存缩进信息
const INDENT_MARKER = '<!-- indent:';
const INDENT_MARKER_END = ' -->';

// 获取元素的缩进级别
function getElementIndentLevel(element: ElementNode): number {
    // 检查是否有getIndent方法（段落和标题节点有这个方法）
    if ('getIndent' in element && typeof element.getIndent === 'function') {
        return (element as any).getIndent();
    }

    // 对于列表项，检查样式
    const style = element.getStyle();
    const match = style.match(/margin-left:\s*(\d+)px/);
    if (match) {
        return Math.floor(parseInt(match[1]) / 32); // 32px per indent level
    }
    return 0;
}

// 设置元素的缩进级别
function setElementIndentLevel(element: ElementNode, level: number): void {
    // 检查是否有setIndent方法（段落和标题节点有这个方法）
    if ('setIndent' in element && typeof element.setIndent === 'function') {
        (element as any).setIndent(level);
        return;
    }

    // 对于列表项，使用样式设置
    if (level <= 0) {
        // 移除缩进样式
        const currentStyle = element.getStyle();
        const newStyle = currentStyle.replace(/margin-left:\s*\d+px;?\s*/g, '');
        element.setStyle(newStyle);
    } else {
        // 设置缩进样式
        const indentPx = level * 32; // 32px per level
        const currentStyle = element.getStyle();
        const newStyle = currentStyle.replace(/margin-left:\s*\d+px;?\s*/g, '') +
                        `margin-left: ${indentPx}px;`;
        element.setStyle(newStyle.trim());
    }
}

// 段落缩进转换器
export const PARAGRAPH_INDENT_TRANSFORMER: ElementTransformer = {
    dependencies: [],
    export: (node: LexicalNode) => {
        if (!$isParagraphNode(node)) {
            return null;
        }
        
        const indentLevel = getElementIndentLevel(node);
        const children = node.getChildren();
        
        if (children.length === 0) {
            return indentLevel > 0 ? `${INDENT_MARKER}${indentLevel}${INDENT_MARKER_END}\n\n` : '\n';
        }
        
        // 获取段落内容
        let content = '';
        for (const child of children) {
            if (child.getTextContent) {
                content += child.getTextContent();
            }
        }
        
        if (indentLevel > 0) {
            return `${INDENT_MARKER}${indentLevel}${INDENT_MARKER_END}\n${content}\n\n`;
        }
        
        return content ? `${content}\n\n` : '\n';
    },
    regExp: new RegExp(`${INDENT_MARKER.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}(\\d+)${INDENT_MARKER_END.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\n(.*)`, 'gm'),
    replace: (_, children, match) => {
        const indentLevel = parseInt(match[1]);
        const content = match[2] || '';
        
        const paragraphNode = $createParagraphNode();
        
        // 设置缩进
        if (indentLevel > 0) {
            setElementIndentLevel(paragraphNode, indentLevel);
        }
        
        // 添加内容
        if (content.trim()) {
            paragraphNode.append(...children);
        }
        
        return {
            node: paragraphNode,
        };
    },
    type: 'element',
};

// 标题缩进转换器
export const HEADING_INDENT_TRANSFORMER: ElementTransformer = {
    dependencies: [],
    export: (node: LexicalNode) => {
        if (!$isHeadingNode(node)) {
            return null;
        }
        
        const indentLevel = getElementIndentLevel(node);
        const headingTag = node.getTag();
        const level = parseInt(headingTag.replace('h', ''));
        const prefix = '#'.repeat(level);
        
        const children = node.getChildren();
        let content = '';
        for (const child of children) {
            if (child.getTextContent) {
                content += child.getTextContent();
            }
        }
        
        if (indentLevel > 0) {
            return `${INDENT_MARKER}${indentLevel}${INDENT_MARKER_END}\n${prefix} ${content}\n\n`;
        }
        
        return `${prefix} ${content}\n\n`;
    },
    regExp: new RegExp(`${INDENT_MARKER.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}(\\d+)${INDENT_MARKER_END.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\n(#{1,6})\\s+(.*)`, 'gm'),
    replace: (_, children, match) => {
        const indentLevel = parseInt(match[1]);
        const headingLevel = match[2].length;
        const content = match[3] || '';
        
        const headingNode = $createHeadingNode(`h${headingLevel}` as HeadingTagType);
        
        // 设置缩进
        if (indentLevel > 0) {
            setElementIndentLevel(headingNode, indentLevel);
        }
        
        // 添加内容
        if (content.trim()) {
            headingNode.append(...children);
        }
        
        return {
            node: headingNode,
        };
    },
    type: 'element',
};

// 列表项缩进转换器
export const LIST_ITEM_INDENT_TRANSFORMER: ElementTransformer = {
    dependencies: [],
    export: (node: LexicalNode) => {
        if (!$isListItemNode(node)) {
            return null;
        }
        
        const indentLevel = getElementIndentLevel(node);
        const parent = node.getParent();
        
        if (!$isListNode(parent)) {
            return null;
        }
        
        const isOrdered = parent.getListType() === 'number';
        const prefix = isOrdered ? '1.' : '-';
        
        const children = node.getChildren();
        let content = '';
        for (const child of children) {
            if (child.getTextContent) {
                content += child.getTextContent();
            }
        }
        
        if (indentLevel > 0) {
            return `${INDENT_MARKER}${indentLevel}${INDENT_MARKER_END}\n${prefix} ${content}\n`;
        }
        
        return `${prefix} ${content}\n`;
    },
    regExp: new RegExp(`${INDENT_MARKER.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}(\\d+)${INDENT_MARKER_END.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\n([\\-\\*]|\\d+\\.)\\s+(.*)`, 'gm'),
    replace: (_, children, match) => {
        const indentLevel = parseInt(match[1]);
        const listMarker = match[2];
        const content = match[3] || '';
        
        const isOrdered = /^\d+\./.test(listMarker);
        const listNode = $createListNode(isOrdered ? 'number' : 'bullet');
        const listItemNode = $createListItemNode();
        
        // 设置缩进
        if (indentLevel > 0) {
            setElementIndentLevel(listItemNode, indentLevel);
        }
        
        // 添加内容
        if (content.trim()) {
            listItemNode.append(...children);
        }
        
        listNode.append(listItemNode);
        
        return {
            node: listNode,
        };
    },
    type: 'element',
};

// 导出所有缩进转换器
export const INDENT_TRANSFORMERS = [
    PARAGRAPH_INDENT_TRANSFORMER,
    HEADING_INDENT_TRANSFORMER,
    LIST_ITEM_INDENT_TRANSFORMER,
];
