{"version": 3, "names": ["transformFromAstRunner", "gens<PERSON>", "ast", "code", "opts", "config", "loadConfig", "Error", "run", "transformFromAst", "optsOrCallback", "maybe<PERSON><PERSON><PERSON>", "callback", "undefined", "beginHiddenCallStack", "sync", "errback", "transformFromAstSync", "args", "transformFromAstAsync", "async"], "sources": ["../src/transform-ast.ts"], "sourcesContent": ["import gensync, { type Hand<PERSON> } from \"gensync\";\n\nimport loadConfig from \"./config\";\nimport type { InputOptions, ResolvedConfig } from \"./config\";\nimport { run } from \"./transformation\";\nimport type * as t from \"@babel/types\";\n\nimport { beginHiddenCallStack } from \"./errors/rewrite-stack-trace\";\n\nimport type { FileResult, FileResultCallback } from \"./transformation\";\ntype AstRoot = t.File | t.Program;\n\ntype TransformFromAst = {\n  (ast: AstRoot, code: string, callback: FileResultCallback): void;\n  (\n    ast: AstRoot,\n    code: string,\n    opts: InputOptions | undefined | null,\n    callback: FileResultCallback,\n  ): void;\n  (ast: AstRoot, code: string, opts?: InputOptions | null): FileResult | null;\n};\n\nconst transformFromAstRunner = gensync(function* (\n  ast: AstRoot,\n  code: string,\n  opts: InputOptions | undefined | null,\n): Handler<FileResult | null> {\n  const config: ResolvedConfig | null = yield* loadConfig(opts);\n  if (config === null) return null;\n\n  if (!ast) throw new Error(\"No AST given\");\n\n  return yield* run(config, code, ast);\n});\n\nexport const transformFromAst: TransformFromAst = function transformFromAst(\n  ast,\n  code,\n  optsOrCallback?: InputOptions | null | undefined | FileResultCallback,\n  maybeCallback?: FileResultCallback,\n) {\n  let opts: InputOptions | undefined | null;\n  let callback: FileResultCallback | undefined;\n  if (typeof optsOrCallback === \"function\") {\n    callback = optsOrCallback;\n    opts = undefined;\n  } else {\n    opts = optsOrCallback;\n    callback = maybeCallback;\n  }\n\n  if (callback === undefined) {\n    if (process.env.BABEL_8_BREAKING) {\n      throw new Error(\n        \"Starting from Babel 8.0.0, the 'transformFromAst' function expects a callback. If you need to call it synchronously, please use 'transformFromAstSync'.\",\n      );\n    } else {\n      // console.warn(\n      //   \"Starting from Babel 8.0.0, the 'transformFromAst' function will expect a callback. If you need to call it synchronously, please use 'transformFromAstSync'.\",\n      // );\n      return beginHiddenCallStack(transformFromAstRunner.sync)(ast, code, opts);\n    }\n  }\n\n  beginHiddenCallStack(transformFromAstRunner.errback)(\n    ast,\n    code,\n    opts,\n    callback,\n  );\n};\n\nexport function transformFromAstSync(\n  ...args: Parameters<typeof transformFromAstRunner.sync>\n) {\n  return beginHiddenCallStack(transformFromAstRunner.sync)(...args);\n}\n\nexport function transformFromAstAsync(\n  ...args: Parameters<typeof transformFromAstRunner.async>\n) {\n  return beginHiddenCallStack(transformFromAstRunner.async)(...args);\n}\n"], "mappings": ";;;;;;;;;AAAA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AAEA;;AAEA;;AAGA;;AAgBA,MAAMA,sBAAsB,GAAGC,UAAA,CAAQ,WACrCC,GADqC,EAErCC,IAFqC,EAGrCC,IAHqC,EAIT;EAC5B,MAAMC,MAA6B,GAAG,OAAO,IAAAC,eAAA,EAAWF,IAAX,CAA7C;EACA,IAAIC,MAAM,KAAK,IAAf,EAAqB,OAAO,IAAP;EAErB,IAAI,CAACH,GAAL,EAAU,MAAM,IAAIK,KAAJ,CAAU,cAAV,CAAN;EAEV,OAAO,OAAO,IAAAC,mBAAA,EAAIH,MAAJ,EAAYF,IAAZ,EAAkBD,GAAlB,CAAd;AACD,CAX8B,CAA/B;;AAaO,MAAMO,gBAAkC,GAAG,SAASA,gBAAT,CAChDP,GADgD,EAEhDC,IAFgD,EAGhDO,cAHgD,EAIhDC,aAJgD,EAKhD;EACA,IAAIP,IAAJ;EACA,IAAIQ,QAAJ;;EACA,IAAI,OAAOF,cAAP,KAA0B,UAA9B,EAA0C;IACxCE,QAAQ,GAAGF,cAAX;IACAN,IAAI,GAAGS,SAAP;EACD,CAHD,MAGO;IACLT,IAAI,GAAGM,cAAP;IACAE,QAAQ,GAAGD,aAAX;EACD;;EAED,IAAIC,QAAQ,KAAKC,SAAjB,EAA4B;IAKnB;MAIL,OAAO,IAAAC,uCAAA,EAAqBd,sBAAsB,CAACe,IAA5C,EAAkDb,GAAlD,EAAuDC,IAAvD,EAA6DC,IAA7D,CAAP;IACD;EACF;;EAED,IAAAU,uCAAA,EAAqBd,sBAAsB,CAACgB,OAA5C,EACEd,GADF,EAEEC,IAFF,EAGEC,IAHF,EAIEQ,QAJF;AAMD,CAnCM;;;;AAqCA,SAASK,oBAAT,CACL,GAAGC,IADE,EAEL;EACA,OAAO,IAAAJ,uCAAA,EAAqBd,sBAAsB,CAACe,IAA5C,EAAkD,GAAGG,IAArD,CAAP;AACD;;AAEM,SAASC,qBAAT,CACL,GAAGD,IADE,EAEL;EACA,OAAO,IAAAJ,uCAAA,EAAqBd,sBAAsB,CAACoB,KAA5C,EAAmD,GAAGF,IAAtD,CAAP;AACD"}