{"compilerOptions": {"allowJs": true, "alwaysStrict": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "jsx": "preserve", "lib": ["dom", "es2017", "webworker"], "module": "esnext", "moduleResolution": "node", "noEmit": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": true, "noUnusedParameters": true, "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "target": "esnext", "baseUrl": ".", "incremental": true, "paths": {"libs/*": ["./libs/*"], "components/*": ["./components/*"], "pages/*": ["./pages/*"], "public/*": ["./public/*"]}}, "exclude": ["node_modules"], "include": ["**/*.ts", "**/*.tsx", "additional.d.ts"]}