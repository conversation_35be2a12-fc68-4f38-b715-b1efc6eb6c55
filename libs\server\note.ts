import { NoteModel } from 'libs/shared/note';
import { genId } from 'libs/shared/id';
import { jsonToMeta } from 'libs/server/meta';
import { getPathNoteById } from 'libs/server/note-path';
import { ServerState } from './connect';

export const createNote = async (note: NoteModel, state: ServerState) => {
    const { content = '\n', ...meta } = note;

    let noteId = note.id;
    if (!noteId) {
        noteId = genId();
    }

    while (await state.store.hasObject(getPathNoteById(noteId))) {
        noteId = genId();
    }

    // 🔧 检测内容格式并设置正确的contentType
    function detectContentType(content: string): string {
        if (!content) return 'text/markdown';

        try {
            const parsed = JSON.parse(content);
            if (parsed && typeof parsed === 'object' && parsed.root && parsed.root.type === 'root') {
                return 'application/json'; // Lexical JSON格式
            }
        } catch {
            // 不是JSON，继续检查其他格式
        }

        // 默认为markdown
        return 'text/markdown';
    }

    const contentType = detectContentType(content);

    const currentTime = new Date().toISOString();
    const metaWithModel = {
        ...meta,
        id: noteId,
        date: note.date ?? currentTime,
        updated_at: currentTime,
    };
    const metaData = jsonToMeta(metaWithModel);

    await state.store.putObject(getPathNoteById(noteId), content, {
        contentType: contentType,
        meta: metaData,
    });


    const completeNote = {
        ...metaWithModel,
        content,
    };

    return completeNote as NoteModel;
};
