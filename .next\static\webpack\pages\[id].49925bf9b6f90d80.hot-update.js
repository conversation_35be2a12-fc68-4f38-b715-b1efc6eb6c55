"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./components/editor/plugins/floating-toolbar-plugin.tsx":
/*!***************************************************************!*\
  !*** ./components/editor/plugins/floating-toolbar-plugin.tsx ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FloatingToolbarPlugin; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/src/_sliced_to_array.mjs */ \"./node_modules/@swc/helpers/src/_sliced_to_array.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"./node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lexical */ \"./node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var _lexical_link__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @lexical/link */ \"./node_modules/@lexical/link/LexicalLink.dev.mjs\");\n/* harmony import */ var _lexical_list__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @lexical/list */ \"./node_modules/@lexical/list/LexicalList.dev.mjs\");\n/* harmony import */ var _highlight_plugin__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./highlight-plugin */ \"./components/editor/plugins/highlight-plugin.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-dom */ \"./node_modules/react-dom/index.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-themes */ \"./node_modules/next-themes/dist/index.modern.js\");\n/* harmony import */ var _heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @heroicons/react/outline */ \"./node_modules/@heroicons/react/outline/esm/index.js\");\n/**\n * Floating Toolbar Plugin for Lexical\n * Shows formatting options when text is selected\n */ \n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Heroicons\n\nfunction FloatingToolbarPlugin() {\n    var _this = this;\n    _s();\n    var ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_5__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_6__.useLexicalComposerContext)(), 1), editor = ref[0];\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isVisible = ref1[0], setIsVisible = ref1[1];\n    var ref2 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        top: 0,\n        left: 0\n    }), position = ref2[0], setPosition = ref2[1];\n    var ref3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isBold = ref3[0], setIsBold = ref3[1];\n    var ref4 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isUnderline = ref4[0], setIsUnderline = ref4[1];\n    var ref5 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isStrikethrough = ref5[0], setIsStrikethrough = ref5[1];\n    var ref6 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isCode = ref6[0], setIsCode = ref6[1];\n    var ref7 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isLink = ref7[0], setIsLink = ref7[1];\n    var ref8 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isHighlight = ref8[0], setIsHighlight = ref8[1];\n    var theme = (0,next_themes__WEBPACK_IMPORTED_MODULE_4__.useTheme)().theme;\n    var updateToolbar = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var selection = (0,lexical__WEBPACK_IMPORTED_MODULE_7__.$getSelection)();\n        if ((0,lexical__WEBPACK_IMPORTED_MODULE_7__.$isRangeSelection)(selection)) {\n            var anchorNode = selection.anchor.getNode();\n            var element = anchorNode.getKey() === \"root\" ? anchorNode : anchorNode.getTopLevelElementOrThrow();\n            var elementKey = element.getKey();\n            var elementDOM = editor.getElementByKey(elementKey);\n            // 显示条件：只有当有选中文本时才显示\n            var hasSelection = selection.getTextContent() !== \"\";\n            if (elementDOM !== null && hasSelection) {\n                var nativeSelection = window.getSelection();\n                var rootElement = editor.getRootElement();\n                if (nativeSelection !== null && rootElement !== null && rootElement.contains(nativeSelection.anchorNode)) {\n                    var rangeRect = nativeSelection.getRangeAt(0).getBoundingClientRect();\n                    setPosition({\n                        top: rangeRect.top - 60,\n                        left: rangeRect.left + rangeRect.width / 2 - 150\n                    });\n                    setIsVisible(true);\n                    // Update button states\n                    setIsBold(selection.hasFormat(\"bold\"));\n                    setIsUnderline(selection.hasFormat(\"underline\"));\n                    setIsStrikethrough(selection.hasFormat(\"strikethrough\"));\n                    setIsCode(selection.hasFormat(\"code\"));\n                    // Check if selection contains a link\n                    var node = selection.anchor.getNode();\n                    var parent = node.getParent();\n                    setIsLink((0,_lexical_link__WEBPACK_IMPORTED_MODULE_8__.$isLinkNode)(parent) || (0,_lexical_link__WEBPACK_IMPORTED_MODULE_8__.$isLinkNode)(node));\n                    // Check for highlight using Lexical's built-in format\n                    setIsHighlight(selection.hasFormat(\"highlight\"));\n                } else {\n                    setIsVisible(false);\n                }\n            } else {\n                setIsVisible(false);\n            }\n        } else {\n            setIsVisible(false);\n        }\n    }, [\n        editor\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n        return editor.registerUpdateListener(function(param) {\n            var editorState = param.editorState;\n            editorState.read(function() {\n                updateToolbar();\n            });\n        });\n    }, [\n        editor,\n        updateToolbar\n    ]);\n    var handleFormat = function(format) {\n        editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_7__.FORMAT_TEXT_COMMAND, format);\n    };\n    var handleLink = function() {\n        if (isLink) {\n            editor.dispatchCommand(_lexical_link__WEBPACK_IMPORTED_MODULE_8__.TOGGLE_LINK_COMMAND, null);\n        } else {\n            var url = prompt(\"Enter URL:\");\n            if (url) {\n                editor.dispatchCommand(_lexical_link__WEBPACK_IMPORTED_MODULE_8__.TOGGLE_LINK_COMMAND, url);\n            }\n        }\n    };\n    if (!isVisible) {\n        return null;\n    }\n    var toolbarBg = theme === \"dark\" ? \"border-gray-600\" : \"border-gray-200\";\n    var buttonText = theme === \"dark\" ? \"text-white\" : \"text-gray-700\";\n    var buttonHover = theme === \"dark\" ? \"hover:text-white\" : \"hover:text-gray-900\";\n    var buttonActive = theme === \"dark\" ? \"text-white\" : \"text-gray-900\";\n    var toolbarButtons = [\n        {\n            title: \"Bold\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"font-bold text-sm\",\n                children: \"B\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 139,\n                columnNumber: 19\n            }, this),\n            isActive: isBold,\n            action: function() {\n                return handleFormat(\"bold\");\n            }\n        },\n        {\n            title: \"Strikethrough\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"line-through text-sm\",\n                children: \"S\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 145,\n                columnNumber: 19\n            }, this),\n            isActive: isStrikethrough,\n            action: function() {\n                return handleFormat(\"strikethrough\");\n            }\n        },\n        {\n            title: \"Underline\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"underline text-sm\",\n                children: \"U\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 151,\n                columnNumber: 19\n            }, this),\n            isActive: isUnderline,\n            action: function() {\n                return handleFormat(\"underline\");\n            }\n        },\n        {\n            title: \"Highlight\",\n            icon: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-xs px-1 rounded text-white\",\n                style: {\n                    backgroundColor: \"#3185eb\"\n                },\n                children: \"H\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 158,\n                columnNumber: 19\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-xs px-1 rounded\",\n                style: {\n                    backgroundColor: \"#eab834\"\n                },\n                children: \"H\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 159,\n                columnNumber: 19\n            }, this),\n            isActive: isHighlight,\n            action: function() {\n                return editor.dispatchCommand(_highlight_plugin__WEBPACK_IMPORTED_MODULE_1__.TOGGLE_HIGHLIGHT_COMMAND, undefined);\n            }\n        },\n        {\n            title: \"Code\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__.CodeIcon, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 165,\n                columnNumber: 19\n            }, this),\n            isActive: isCode,\n            action: function() {\n                return handleFormat(\"code\");\n            }\n        },\n        {\n            title: isLink ? \"Remove Link\" : \"Add Link\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__.LinkIcon, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 171,\n                columnNumber: 19\n            }, this),\n            isActive: isLink,\n            action: handleLink\n        },\n        // 分隔符\n        {\n            type: \"separator\"\n        },\n        {\n            title: \"Checklist\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm\",\n                children: \"☑\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 179,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: function() {\n                return editor.dispatchCommand(_lexical_list__WEBPACK_IMPORTED_MODULE_10__.INSERT_CHECK_LIST_COMMAND, undefined);\n            }\n        },\n        {\n            title: \"Bullet List\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm\",\n                children: \"•\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 185,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: function() {\n                return editor.dispatchCommand(_lexical_list__WEBPACK_IMPORTED_MODULE_10__.INSERT_UNORDERED_LIST_COMMAND, undefined);\n            }\n        },\n        {\n            title: \"Numbered List\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm\",\n                children: \"1.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 191,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: function() {\n                return editor.dispatchCommand(_lexical_list__WEBPACK_IMPORTED_MODULE_10__.INSERT_ORDERED_LIST_COMMAND, undefined);\n            }\n        },\n        // 分隔符\n        {\n            type: \"separator\"\n        },\n        {\n            title: \"Indent\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__.ArrowRightIcon, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 199,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: function() {\n                editor.update(function() {\n                    var selection = (0,lexical__WEBPACK_IMPORTED_MODULE_7__.$getSelection)();\n                    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_7__.$isRangeSelection)(selection)) {\n                        return;\n                    }\n                    var nodes = selection.getNodes();\n                    var processedListItems = new Set();\n                    var hasListItems = false;\n                    // 找到所有需要缩进的列表项\n                    nodes.forEach(function(node) {\n                        var listItemNode = null;\n                        if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$isListItemNode)(node)) {\n                            listItemNode = node;\n                        } else if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$isListItemNode)(node.getParent())) {\n                            listItemNode = node.getParent();\n                        }\n                        if (listItemNode && (0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$isListItemNode)(listItemNode) && !processedListItems.has(listItemNode.getKey())) {\n                            processedListItems.add(listItemNode.getKey());\n                            var currentIndent = listItemNode.getIndent();\n                            listItemNode.setIndent(currentIndent + 1);\n                            hasListItems = true;\n                        }\n                    });\n                    // 如果没有找到列表项，使用通用缩进\n                    if (!hasListItems) {\n                        editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_7__.INDENT_CONTENT_COMMAND, undefined);\n                    }\n                });\n            }\n        },\n        {\n            title: \"Outdent\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__.ArrowLeftIcon, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 239,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: function() {\n                editor.update(function() {\n                    var selection = (0,lexical__WEBPACK_IMPORTED_MODULE_7__.$getSelection)();\n                    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_7__.$isRangeSelection)(selection)) {\n                        return;\n                    }\n                    var nodes = selection.getNodes();\n                    var processedListItems = new Set();\n                    var hasListItems = false;\n                    // 找到所有需要反缩进的列表项\n                    nodes.forEach(function(node) {\n                        var listItemNode = null;\n                        if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$isListItemNode)(node)) {\n                            listItemNode = node;\n                        } else if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$isListItemNode)(node.getParent())) {\n                            listItemNode = node.getParent();\n                        }\n                        if (listItemNode && (0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$isListItemNode)(listItemNode) && !processedListItems.has(listItemNode.getKey())) {\n                            processedListItems.add(listItemNode.getKey());\n                            var currentIndent = listItemNode.getIndent();\n                            if (currentIndent > 0) {\n                                listItemNode.setIndent(currentIndent - 1);\n                            }\n                            hasListItems = true;\n                        }\n                    });\n                    // 如果没有找到列表项，使用通用反缩进\n                    if (!hasListItems) {\n                        editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_7__.OUTDENT_CONTENT_COMMAND, undefined);\n                    }\n                });\n            }\n        }, \n    ];\n    return /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_3__.createPortal)(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed z-50 \".concat(toolbarBg, \" border rounded-lg p-1.5 flex space-x-0.5 shadow-lg\"),\n        style: {\n            top: position.top,\n            left: position.left,\n            transform: \"translateX(-50%)\",\n            backgroundColor: theme === \"dark\" ? \"#3f3f46\" : \"#e4e4e7\"\n        },\n        children: toolbarButtons.map(function(button, index) {\n            if (button.type === \"separator\") {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-px h-6 \".concat(theme === \"dark\" ? \"bg-gray-600\" : \"bg-gray-300\", \" mx-1\")\n                }, index, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                    lineNumber: 295,\n                    columnNumber: 25\n                }, _this);\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: button.action,\n                title: button.title,\n                className: \"\\n                            px-2.5 py-1.5 rounded transition-colors duration-150 min-w-[30px] h-7 flex items-center justify-center text-sm font-medium\\n                            \".concat(button.isActive ? buttonActive : \"\".concat(buttonText, \" \").concat(buttonHover), \"\\n                        \"),\n                style: {\n                    backgroundColor: button.isActive ? theme === \"dark\" ? \"#3185eb\" : \"#eab834\" : \"transparent\"\n                },\n                onMouseEnter: function(e) {\n                    if (!button.isActive) {\n                        e.currentTarget.style.backgroundColor = theme === \"dark\" ? \"#3185eb\" : \"#eab834\";\n                        if (theme === \"dark\") {\n                            e.currentTarget.style.color = \"white\";\n                        }\n                    }\n                },\n                onMouseLeave: function(e) {\n                    if (!button.isActive) {\n                        e.currentTarget.style.backgroundColor = \"transparent\";\n                        e.currentTarget.style.color = \"\";\n                    }\n                },\n                children: button.icon\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 303,\n                columnNumber: 21\n            }, _this);\n        })\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n        lineNumber: 283,\n        columnNumber: 9\n    }, this), document.body);\n}\n_s(FloatingToolbarPlugin, \"FbU03gSrzEPMPysVlTtv8K886Dg=\", false, function() {\n    return [\n        _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_6__.useLexicalComposerContext,\n        next_themes__WEBPACK_IMPORTED_MODULE_4__.useTheme\n    ];\n});\n_c = FloatingToolbarPlugin;\nvar _c;\n$RefreshReg$(_c, \"FloatingToolbarPlugin\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/editor/plugins/floating-toolbar-plugin.tsx\n"));

/***/ })

});