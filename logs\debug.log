{"level":20,"time":1751514357767,"pid":9376,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751514357772,"pid":9376,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751514357777,"pid":9376,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751514357783,"pid":9376,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751514362917,"pid":9376,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751514363157,"pid":9376,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751514363392,"pid":9376,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751514363625,"pid":9376,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751514363861,"pid":9376,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751514364098,"pid":9376,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751514364330,"pid":9376,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751514364574,"pid":9376,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751514364808,"pid":9376,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751514364808,"pid":9376,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":30,"time":1751514428835,"pid":9376,"hostname":"DESKTOP-CUEETJV","name":"rate-limit","msg":"Rate limiting configured"}
{"level":20,"time":1751514429391,"pid":9376,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751514429392,"pid":9376,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751514429394,"pid":9376,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751514429396,"pid":9376,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751514430378,"pid":9376,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751514439116,"pid":9376,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751514439188,"pid":9376,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751514439189,"pid":9376,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751514439191,"pid":9376,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751514439199,"pid":9376,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751514439206,"pid":9376,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751514439216,"pid":9376,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751515724719,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751515724721,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751515724722,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751515724725,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751515730863,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515731095,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515731326,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515731555,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515731783,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515732013,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515732241,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515732471,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515732700,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751515732700,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":20,"time":1751515739163,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751515739178,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751515739191,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751515739195,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751515743346,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515743573,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515743799,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515744031,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515744259,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515744486,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515744716,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515744943,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515745180,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751515745188,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":30,"time":1751515751923,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"rate-limit","msg":"Rate limiting configured"}
{"level":20,"time":1751515752014,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751515752015,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751515752016,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751515752018,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751515752134,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751515815881,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751515815882,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751515815883,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751515815885,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751515819934,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515820164,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515820394,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515820624,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515820854,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515821085,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515821317,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515821547,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515821777,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751515821777,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":30,"time":1751515824290,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"rate-limit","msg":"Rate limiting configured"}
{"level":20,"time":1751515824299,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751515824300,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751515824301,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751515824302,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751515824387,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751515906705,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751515906706,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751515906714,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751515906717,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751515906732,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751515909144,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515909371,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515909598,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515909827,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515910053,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515910279,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515910508,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515910734,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515910959,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751515910959,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":20,"time":1751515911222,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Successfully put note:"}
{"level":30,"time":1751515965208,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751515965216,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751515968109,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515968339,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515968570,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515968797,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515969027,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515969258,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515969485,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515969713,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751515969944,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751515969944,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":20,"time":1751515970173,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Successfully put note:"}
{"level":30,"time":1751515997861,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751515997873,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751516000227,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516000457,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516000688,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516000918,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516001150,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516001380,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516001609,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516001839,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516002070,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751516002070,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":20,"time":1751516002303,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Successfully put note:"}
{"level":30,"time":1751516009569,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751516009576,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751516011886,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516012113,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516012342,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516012570,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516012796,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516013025,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516013252,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516013478,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516013706,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751516013706,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":20,"time":1751516013935,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Successfully put note:"}
{"level":30,"time":1751516018885,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751516018892,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751516021203,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516021431,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516021662,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516021892,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516022121,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516022349,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516022579,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516022812,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516023040,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751516023040,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":20,"time":1751516023273,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Successfully put note:"}
{"level":20,"time":1751516035733,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751516035734,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751516035734,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751516035735,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751516039625,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516039870,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516040117,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516040363,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516040682,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516041696,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516041938,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516042181,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516042427,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751516042428,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":30,"time":1751516045391,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"rate-limit","msg":"Rate limiting configured"}
{"level":20,"time":1751516045394,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751516045394,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751516045394,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751516045395,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751516045469,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751516049547,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751516049548,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751516049549,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751516049550,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751516049558,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751516118436,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751516118438,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751516118439,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751516118441,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751516120871,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516121105,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516121334,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516121564,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516121792,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516122020,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516122250,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516122480,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516122709,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751516122710,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":20,"time":1751516123181,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":30,"time":1751516123788,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"rate-limit","msg":"Rate limiting configured"}
{"level":20,"time":1751516123792,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751516123792,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751516123792,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751516123793,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751516123810,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751516132757,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751516132758,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751516132759,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751516132759,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751516132780,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751516133341,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751516133344,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751516135068,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516135318,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516135403,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516135569,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516135629,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516135818,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516135857,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516136068,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516136084,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516136310,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516136327,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516136537,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516136576,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516136763,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516136825,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516136989,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516137075,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751516137075,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":20,"time":1751516137215,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751516137215,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":20,"time":1751516137829,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Successfully put non-note object:"}
{"level":20,"time":1751516137897,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Successfully put non-note object:"}
{"level":20,"time":1751516343682,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751516343683,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751516343684,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751516343685,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751516348317,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516348543,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516348768,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516348994,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516349219,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516349445,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516349670,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516349895,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516350121,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751516350121,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":30,"time":1751516350972,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"rate-limit","msg":"Rate limiting configured"}
{"level":30,"time":1751516350976,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751516351045,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751516369128,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"rate-limit","msg":"Rate limiting configured"}
{"level":20,"time":1751516369133,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751516369134,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751516369135,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751516369136,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751516369216,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751516371316,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516371547,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516371777,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516372009,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516372241,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516372470,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516372751,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516372976,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516373205,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751516373206,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":20,"time":1751516374138,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Successfully put note:"}
{"level":20,"time":1751516376096,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"tree-store.postgresql","msg":"Successfully updated tree data"}
{"level":20,"time":1751516457594,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751516457594,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751516457595,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751516457596,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751516459994,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516460244,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516460494,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516460744,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516460997,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516461248,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516461498,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516461748,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516461999,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751516461999,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":20,"time":1751516464463,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751516464464,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751516464464,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751516464465,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751516474198,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"rate-limit","msg":"Rate limiting configured"}
{"level":20,"time":1751516474204,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751516474205,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751516474206,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751516474207,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751516474551,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751516474797,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751516474797,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751516474798,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751516474798,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751516478672,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516478900,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516479139,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516479371,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516479600,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516479836,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516480064,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516480292,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516480519,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751516480519,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":30,"time":1751516482946,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"rate-limit","msg":"Rate limiting configured"}
{"level":20,"time":1751516482951,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751516482952,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751516482953,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751516482955,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751516483018,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751516487177,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751516487178,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751516487178,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751516487179,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751516487182,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751516487183,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751516487185,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751516487191,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751516487219,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751516487222,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751516487224,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751516520095,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751516520096,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751516520097,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751516520099,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751516524172,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516525175,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516525632,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516525863,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516526090,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516526318,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516526547,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516526775,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516527002,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751516527002,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":30,"time":1751516527844,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"rate-limit","msg":"Rate limiting configured"}
{"level":30,"time":1751516527847,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751516527876,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751516548962,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751516548967,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751516551769,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516552017,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516552262,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516552510,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516552757,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516553001,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516553248,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751516553468,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751516553471,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751516553498,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516553742,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751516553742,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":20,"time":1751516554467,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Successfully put note:"}
{"level":20,"time":1751516555794,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516556023,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516556252,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516556482,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516556711,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516556939,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516557168,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516557396,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516557624,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751516557624,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":20,"time":1751516557856,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Successfully put note:"}
{"level":30,"time":1751516565300,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751516565303,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751516567852,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516568097,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516568341,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516568587,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516568831,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516569077,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516569322,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516569566,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751516569810,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751516569810,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":20,"time":1751516570057,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Successfully put note:"}
{"level":20,"time":1751517359232,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751517359233,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751517359233,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751517359235,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751517360967,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751517360968,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751517360969,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751517360970,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751517364467,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517364699,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517364926,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517365157,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517365241,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517365391,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517365499,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517365623,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517365752,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517365857,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517366003,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517366094,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517366255,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517366329,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751517366329,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":20,"time":1751517366503,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517366748,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517366997,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517367245,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751517367246,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":20,"time":1751517402080,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751517402081,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751517402082,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751517402084,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751517407990,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517408237,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517408488,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517408736,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517408983,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517409235,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517409482,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517409730,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517409980,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751517409981,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":30,"time":1751517621297,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"rate-limit","msg":"Rate limiting configured"}
{"level":20,"time":1751517621307,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751517621308,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751517621309,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751517621310,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751517621348,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751517624919,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751517624919,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751517624920,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751517624921,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751517624928,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751517625011,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751517625013,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751517637058,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751517637059,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751517637059,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751517637060,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751517641111,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517641343,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517641576,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517641808,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517642044,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517642284,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517642516,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517642748,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517642989,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751517642989,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":30,"time":1751517643848,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"rate-limit","msg":"Rate limiting configured"}
{"level":20,"time":1751517643854,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751517643855,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751517643856,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751517643857,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751517643874,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751517652835,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751517652838,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751517666330,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751517666332,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751517666333,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751517666334,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751517666340,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751517667466,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751517667468,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751517667469,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751517667470,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751517667478,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751517667526,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751517667551,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751517668535,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517668774,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517669009,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517669242,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517669483,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517669720,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517669953,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517669953,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517670182,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517670185,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517670414,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517670426,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751517670426,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":20,"time":1751517670645,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517670875,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517671104,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517671209,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Successfully put non-note object:"}
{"level":20,"time":1751517671337,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517671564,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517671791,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751517671791,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":20,"time":1751517672413,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"tree-store.postgresql","msg":"Successfully updated tree data"}
{"level":20,"time":1751517672480,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Successfully put non-note object:"}
{"level":20,"time":1751517673366,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517673610,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517673847,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517674089,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517674332,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517674571,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517674808,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517675045,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517675292,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751517675293,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":20,"time":1751517675537,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Successfully put note:"}
{"level":30,"time":1751517693955,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751517693960,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751517696972,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517697213,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517697447,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517697687,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517697928,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517698161,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517698394,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517698633,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517698866,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751517698866,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":20,"time":1751517699108,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Successfully put note:"}
{"level":20,"time":1751517699279,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751517699280,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751517699281,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751517699281,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751517703309,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517703545,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517703781,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517704017,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517704255,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517704492,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517704729,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517704964,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517705200,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751517705201,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":30,"time":1751517705802,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751517705823,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751517705907,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751517705914,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751517707910,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"rate-limit","msg":"Rate limiting configured"}
{"level":20,"time":1751517707915,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751517707916,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751517707916,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751517707917,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751517707937,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751517707972,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517708211,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517708448,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517708682,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517708923,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517709156,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517709389,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517709630,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517709863,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751517709863,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":20,"time":1751517710568,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Successfully put non-note object:"}
{"level":30,"time":1751517711578,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751517711581,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751517719017,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751517719022,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751517722290,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517722519,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517722752,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517722984,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517723213,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517723446,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517723678,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517723907,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517724136,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751517724136,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":20,"time":1751517724374,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Successfully put note:"}
{"level":20,"time":1751517724637,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751517724637,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751517724638,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751517724638,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751517725135,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"rate-limit","msg":"Rate limiting configured"}
{"level":20,"time":1751517725138,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751517725138,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751517725139,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751517725139,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751517725188,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751517727325,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517727564,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517727795,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517728025,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517728263,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517728494,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517728725,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517728782,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517728963,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517729025,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517729197,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751517729197,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":20,"time":1751517729262,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517729502,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517729743,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517729981,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517730155,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Successfully put note:"}
{"level":20,"time":1751517730218,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517730455,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517730697,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751517730697,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":20,"time":1751517731502,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751517731503,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751517731504,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751517731505,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751517731509,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751517731553,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751517731557,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751517732229,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"tree-store.postgresql","msg":"Successfully updated tree data"}
{"level":20,"time":1751517732471,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751517732471,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751517732471,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751517732472,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751517732485,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751517732487,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751517733490,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"rate-limit","msg":"Rate limiting configured"}
{"level":20,"time":1751517733502,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration"}
{"level":20,"time":1751517733503,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Loading configuration from scratch (loadConfigAndListErrors)"}
{"level":20,"time":1751517733504,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"config","msg":"Successfully loaded configuration"}
{"level":30,"time":1751517733505,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751517733532,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751517733639,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517733876,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517734108,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517734337,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517734569,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517734806,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517735041,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517735273,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517735515,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751517735515,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":20,"time":1751517735735,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517735964,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517736198,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517736207,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Successfully put non-note object:"}
{"level":20,"time":1751517736427,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517736657,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517736889,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517737124,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517737352,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517737427,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517737585,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751517737585,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":20,"time":1751517737661,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517737820,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Successfully put note:"}
{"level":20,"time":1751517737889,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517738116,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517738346,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517738575,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751517738660,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751517738663,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751517738802,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517739035,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517739265,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751517739266,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":30,"time":1751517739821,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751517739824,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751517739943,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751517739950,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751517741694,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":30,"time":1751517741704,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"PostgreSQL pool configured:"}
{"level":20,"time":1751517741991,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517742236,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517742474,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517742711,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517742949,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517743183,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517743426,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517743718,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":20,"time":1751517743963,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Created/verified index:"}
{"level":30,"time":1751517743964,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Database tables initialized successfully"}
{"level":20,"time":1751517744680,"pid":9400,"hostname":"DESKTOP-CUEETJV","name":"store.postgresql","msg":"Successfully put non-note object:"}
