{"version": 3, "names": ["getQualifiedName", "node", "isIdentifier", "name", "id", "qualification", "removeTypeDuplicates", "nodes", "generics", "Map", "bases", "typeGroups", "Set", "types", "i", "length", "indexOf", "isAnyTypeAnnotation", "isFlowBaseAnnotation", "set", "type", "isUnionTypeAnnotation", "has", "concat", "add", "isGenericTypeAnnotation", "existing", "get", "typeParameters", "params", "push", "baseType", "genericName"], "sources": ["../../../src/modifications/flow/removeTypeDuplicates.ts"], "sourcesContent": ["import {\n  isAnyTypeAnnotation,\n  isGenericTypeAnnotation,\n  isUnionTypeAnnotation,\n  isFlowBaseAnnotation,\n  isIdentifier,\n} from \"../../validators/generated\";\nimport type * as t from \"../..\";\n\nfunction getQualifiedName(node: t.GenericTypeAnnotation[\"id\"]): string {\n  return isIdentifier(node)\n    ? node.name\n    : `${node.id.name}.${getQualifiedName(node.qualification)}`;\n}\n\n/**\n * Dedupe type annotations.\n */\nexport default function removeTypeDuplicates(\n  // todo(babel-8): change type to Array<...>\n  nodes: ReadonlyArray<t.FlowType | false | null | undefined>,\n): t.FlowType[] {\n  const generics = new Map<string, t.GenericTypeAnnotation>();\n  const bases = new Map<t.FlowBaseAnnotation[\"type\"], t.FlowBaseAnnotation>();\n\n  // store union type groups to circular references\n  const typeGroups = new Set<t.FlowType[]>();\n\n  const types: t.FlowType[] = [];\n\n  for (let i = 0; i < nodes.length; i++) {\n    const node = nodes[i];\n    if (!node) continue;\n\n    // detect duplicates\n    if (types.indexOf(node) >= 0) {\n      continue;\n    }\n\n    // this type matches anything\n    if (isAnyTypeAnnotation(node)) {\n      return [node];\n    }\n\n    if (isFlowBaseAnnotation(node)) {\n      bases.set(node.type, node);\n      continue;\n    }\n\n    if (isUnionTypeAnnotation(node)) {\n      if (!typeGroups.has(node.types)) {\n        // todo(babel-8): use .push when nodes is mutable\n        nodes = nodes.concat(node.types);\n        typeGroups.add(node.types);\n      }\n      continue;\n    }\n\n    // find a matching generic type and merge and deduplicate the type parameters\n    if (isGenericTypeAnnotation(node)) {\n      const name = getQualifiedName(node.id);\n\n      if (generics.has(name)) {\n        let existing: t.Flow = generics.get(name);\n        if (existing.typeParameters) {\n          if (node.typeParameters) {\n            existing.typeParameters.params = removeTypeDuplicates(\n              existing.typeParameters.params.concat(node.typeParameters.params),\n            );\n          }\n        } else {\n          existing = node.typeParameters;\n        }\n      } else {\n        generics.set(name, node);\n      }\n\n      continue;\n    }\n\n    types.push(node);\n  }\n\n  // add back in bases\n  for (const [, baseType] of bases) {\n    types.push(baseType);\n  }\n\n  // add back in generics\n  for (const [, genericName] of generics) {\n    types.push(genericName);\n  }\n\n  return types;\n}\n"], "mappings": ";;;;;;;AAAA;;AASA,SAASA,gBAAT,CAA0BC,IAA1B,EAAuE;EACrE,OAAO,IAAAC,uBAAA,EAAaD,IAAb,IACHA,IAAI,CAACE,IADF,GAEF,GAAEF,IAAI,CAACG,EAAL,CAAQD,IAAK,IAAGH,gBAAgB,CAACC,IAAI,CAACI,aAAN,CAAqB,EAF5D;AAGD;;AAKc,SAASC,oBAAT,CAEbC,KAFa,EAGC;EACd,MAAMC,QAAQ,GAAG,IAAIC,GAAJ,EAAjB;EACA,MAAMC,KAAK,GAAG,IAAID,GAAJ,EAAd;EAGA,MAAME,UAAU,GAAG,IAAIC,GAAJ,EAAnB;EAEA,MAAMC,KAAmB,GAAG,EAA5B;;EAEA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGP,KAAK,CAACQ,MAA1B,EAAkCD,CAAC,EAAnC,EAAuC;IACrC,MAAMb,IAAI,GAAGM,KAAK,CAACO,CAAD,CAAlB;IACA,IAAI,CAACb,IAAL,EAAW;;IAGX,IAAIY,KAAK,CAACG,OAAN,CAAcf,IAAd,KAAuB,CAA3B,EAA8B;MAC5B;IACD;;IAGD,IAAI,IAAAgB,8BAAA,EAAoBhB,IAApB,CAAJ,EAA+B;MAC7B,OAAO,CAACA,IAAD,CAAP;IACD;;IAED,IAAI,IAAAiB,+BAAA,EAAqBjB,IAArB,CAAJ,EAAgC;MAC9BS,KAAK,CAACS,GAAN,CAAUlB,IAAI,CAACmB,IAAf,EAAqBnB,IAArB;MACA;IACD;;IAED,IAAI,IAAAoB,gCAAA,EAAsBpB,IAAtB,CAAJ,EAAiC;MAC/B,IAAI,CAACU,UAAU,CAACW,GAAX,CAAerB,IAAI,CAACY,KAApB,CAAL,EAAiC;QAE/BN,KAAK,GAAGA,KAAK,CAACgB,MAAN,CAAatB,IAAI,CAACY,KAAlB,CAAR;QACAF,UAAU,CAACa,GAAX,CAAevB,IAAI,CAACY,KAApB;MACD;;MACD;IACD;;IAGD,IAAI,IAAAY,kCAAA,EAAwBxB,IAAxB,CAAJ,EAAmC;MACjC,MAAME,IAAI,GAAGH,gBAAgB,CAACC,IAAI,CAACG,EAAN,CAA7B;;MAEA,IAAII,QAAQ,CAACc,GAAT,CAAanB,IAAb,CAAJ,EAAwB;QACtB,IAAIuB,QAAgB,GAAGlB,QAAQ,CAACmB,GAAT,CAAaxB,IAAb,CAAvB;;QACA,IAAIuB,QAAQ,CAACE,cAAb,EAA6B;UAC3B,IAAI3B,IAAI,CAAC2B,cAAT,EAAyB;YACvBF,QAAQ,CAACE,cAAT,CAAwBC,MAAxB,GAAiCvB,oBAAoB,CACnDoB,QAAQ,CAACE,cAAT,CAAwBC,MAAxB,CAA+BN,MAA/B,CAAsCtB,IAAI,CAAC2B,cAAL,CAAoBC,MAA1D,CADmD,CAArD;UAGD;QACF,CAND,MAMO;UACLH,QAAQ,GAAGzB,IAAI,CAAC2B,cAAhB;QACD;MACF,CAXD,MAWO;QACLpB,QAAQ,CAACW,GAAT,CAAahB,IAAb,EAAmBF,IAAnB;MACD;;MAED;IACD;;IAEDY,KAAK,CAACiB,IAAN,CAAW7B,IAAX;EACD;;EAGD,KAAK,MAAM,GAAG8B,QAAH,CAAX,IAA2BrB,KAA3B,EAAkC;IAChCG,KAAK,CAACiB,IAAN,CAAWC,QAAX;EACD;;EAGD,KAAK,MAAM,GAAGC,WAAH,CAAX,IAA8BxB,QAA9B,EAAwC;IACtCK,KAAK,CAACiB,IAAN,CAAWE,WAAX;EACD;;EAED,OAAOnB,KAAP;AACD"}