"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./components/editor/plugins/floating-toolbar-plugin.tsx":
/*!***************************************************************!*\
  !*** ./components/editor/plugins/floating-toolbar-plugin.tsx ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FloatingToolbarPlugin; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/src/_sliced_to_array.mjs */ \"./node_modules/@swc/helpers/src/_sliced_to_array.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"./node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lexical */ \"./node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var _lexical_link__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @lexical/link */ \"./node_modules/@lexical/link/LexicalLink.dev.mjs\");\n/* harmony import */ var _lexical_list__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @lexical/list */ \"./node_modules/@lexical/list/LexicalList.dev.mjs\");\n/* harmony import */ var _highlight_plugin__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./highlight-plugin */ \"./components/editor/plugins/highlight-plugin.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-dom */ \"./node_modules/react-dom/index.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-themes */ \"./node_modules/next-themes/dist/index.modern.js\");\n/* harmony import */ var _heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @heroicons/react/outline */ \"./node_modules/@heroicons/react/outline/esm/index.js\");\n/**\n * Floating Toolbar Plugin for Lexical\n * Shows formatting options when text is selected\n */ \n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Heroicons\n\nfunction FloatingToolbarPlugin() {\n    var _this = this;\n    _s();\n    var ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_5__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_6__.useLexicalComposerContext)(), 1), editor = ref[0];\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isVisible = ref1[0], setIsVisible = ref1[1];\n    var ref2 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        top: 0,\n        left: 0\n    }), position = ref2[0], setPosition = ref2[1];\n    var ref3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isBold = ref3[0], setIsBold = ref3[1];\n    var ref4 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isUnderline = ref4[0], setIsUnderline = ref4[1];\n    var ref5 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isStrikethrough = ref5[0], setIsStrikethrough = ref5[1];\n    var ref6 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isCode = ref6[0], setIsCode = ref6[1];\n    var ref7 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isLink = ref7[0], setIsLink = ref7[1];\n    var ref8 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isHighlight = ref8[0], setIsHighlight = ref8[1];\n    var theme = (0,next_themes__WEBPACK_IMPORTED_MODULE_4__.useTheme)().theme;\n    var updateToolbar = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var selection = (0,lexical__WEBPACK_IMPORTED_MODULE_7__.$getSelection)();\n        if ((0,lexical__WEBPACK_IMPORTED_MODULE_7__.$isRangeSelection)(selection)) {\n            var anchorNode = selection.anchor.getNode();\n            var element = anchorNode.getKey() === \"root\" ? anchorNode : anchorNode.getTopLevelElementOrThrow();\n            var elementKey = element.getKey();\n            var elementDOM = editor.getElementByKey(elementKey);\n            // 显示条件：只有当有选中文本时才显示\n            var hasSelection = selection.getTextContent() !== \"\";\n            if (elementDOM !== null && hasSelection) {\n                var nativeSelection = window.getSelection();\n                var rootElement = editor.getRootElement();\n                if (nativeSelection !== null && rootElement !== null && rootElement.contains(nativeSelection.anchorNode)) {\n                    var rangeRect = nativeSelection.getRangeAt(0).getBoundingClientRect();\n                    setPosition({\n                        top: rangeRect.top - 60,\n                        left: rangeRect.left + rangeRect.width / 2 - 150\n                    });\n                    setIsVisible(true);\n                    // Update button states\n                    setIsBold(selection.hasFormat(\"bold\"));\n                    setIsUnderline(selection.hasFormat(\"underline\"));\n                    setIsStrikethrough(selection.hasFormat(\"strikethrough\"));\n                    setIsCode(selection.hasFormat(\"code\"));\n                    // Check if selection contains a link\n                    var node = selection.anchor.getNode();\n                    var parent = node.getParent();\n                    setIsLink((0,_lexical_link__WEBPACK_IMPORTED_MODULE_8__.$isLinkNode)(parent) || (0,_lexical_link__WEBPACK_IMPORTED_MODULE_8__.$isLinkNode)(node));\n                    // Check for highlight using Lexical's built-in format\n                    setIsHighlight(selection.hasFormat(\"highlight\"));\n                } else {\n                    setIsVisible(false);\n                }\n            } else {\n                setIsVisible(false);\n            }\n        } else {\n            setIsVisible(false);\n        }\n    }, [\n        editor\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n        return editor.registerUpdateListener(function(param) {\n            var editorState = param.editorState;\n            editorState.read(function() {\n                updateToolbar();\n            });\n        });\n    }, [\n        editor,\n        updateToolbar\n    ]);\n    var handleFormat = function(format) {\n        editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_7__.FORMAT_TEXT_COMMAND, format);\n    };\n    var handleLink = function() {\n        if (isLink) {\n            editor.dispatchCommand(_lexical_link__WEBPACK_IMPORTED_MODULE_8__.TOGGLE_LINK_COMMAND, null);\n        } else {\n            var url = prompt(\"Enter URL:\");\n            if (url) {\n                editor.dispatchCommand(_lexical_link__WEBPACK_IMPORTED_MODULE_8__.TOGGLE_LINK_COMMAND, url);\n            }\n        }\n    };\n    if (!isVisible) {\n        return null;\n    }\n    var toolbarBg = theme === \"dark\" ? \"border-gray-600\" : \"border-gray-200\";\n    var buttonText = theme === \"dark\" ? \"text-white\" : \"text-gray-700\";\n    var buttonHover = theme === \"dark\" ? \"hover:text-white\" : \"hover:text-gray-900\";\n    var buttonActive = theme === \"dark\" ? \"text-white\" : \"text-gray-900\";\n    var toolbarButtons = [\n        {\n            title: \"Bold\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"font-bold text-sm\",\n                children: \"B\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 139,\n                columnNumber: 19\n            }, this),\n            isActive: isBold,\n            action: function() {\n                return handleFormat(\"bold\");\n            }\n        },\n        {\n            title: \"Strikethrough\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"line-through text-sm\",\n                children: \"S\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 145,\n                columnNumber: 19\n            }, this),\n            isActive: isStrikethrough,\n            action: function() {\n                return handleFormat(\"strikethrough\");\n            }\n        },\n        {\n            title: \"Underline\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"underline text-sm\",\n                children: \"U\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 151,\n                columnNumber: 19\n            }, this),\n            isActive: isUnderline,\n            action: function() {\n                return handleFormat(\"underline\");\n            }\n        },\n        {\n            title: \"Highlight\",\n            icon: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-xs px-1 rounded text-white\",\n                style: {\n                    backgroundColor: \"#3185eb\"\n                },\n                children: \"H\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 158,\n                columnNumber: 19\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-xs px-1 rounded\",\n                style: {\n                    backgroundColor: \"#eab834\"\n                },\n                children: \"H\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 159,\n                columnNumber: 19\n            }, this),\n            isActive: isHighlight,\n            action: function() {\n                return editor.dispatchCommand(_highlight_plugin__WEBPACK_IMPORTED_MODULE_1__.TOGGLE_HIGHLIGHT_COMMAND, undefined);\n            }\n        },\n        {\n            title: \"Code\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__.CodeIcon, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 165,\n                columnNumber: 19\n            }, this),\n            isActive: isCode,\n            action: function() {\n                return handleFormat(\"code\");\n            }\n        },\n        {\n            title: isLink ? \"Remove Link\" : \"Add Link\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__.LinkIcon, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 171,\n                columnNumber: 19\n            }, this),\n            isActive: isLink,\n            action: handleLink\n        },\n        // 分隔符\n        {\n            type: \"separator\"\n        },\n        {\n            title: \"Checklist\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm\",\n                children: \"☑\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 179,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: function() {\n                return editor.dispatchCommand(_lexical_list__WEBPACK_IMPORTED_MODULE_10__.INSERT_CHECK_LIST_COMMAND, undefined);\n            }\n        },\n        {\n            title: \"Bullet List\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm\",\n                children: \"•\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 185,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: function() {\n                return editor.dispatchCommand(_lexical_list__WEBPACK_IMPORTED_MODULE_10__.INSERT_UNORDERED_LIST_COMMAND, undefined);\n            }\n        },\n        {\n            title: \"Numbered List\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm\",\n                children: \"1.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 191,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: function() {\n                return editor.dispatchCommand(_lexical_list__WEBPACK_IMPORTED_MODULE_10__.INSERT_ORDERED_LIST_COMMAND, undefined);\n            }\n        },\n        // 分隔符\n        {\n            type: \"separator\"\n        },\n        {\n            title: \"Indent\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__.ArrowRightIcon, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 199,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: function() {\n                editor.update(function() {\n                    var selection = (0,lexical__WEBPACK_IMPORTED_MODULE_7__.$getSelection)();\n                    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_7__.$isRangeSelection)(selection)) {\n                        return;\n                    }\n                    var nodes = selection.getNodes();\n                    var listItemNodes = nodes.filter(function(node) {\n                        return (0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$isListItemNode)(node) || (0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$isListItemNode)(node.getParent());\n                    });\n                    if (listItemNodes.length > 0) {\n                        // 如果选中的是列表项，使用专门的列表缩进函数\n                        listItemNodes.forEach(function(node) {\n                            var listItemNode = (0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$isListItemNode)(node) ? node : node.getParent();\n                            if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$isListItemNode)(listItemNode)) {\n                                (0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$handleIndent)(listItemNode);\n                            }\n                        });\n                    } else {\n                        // 如果不是列表项，使用通用的缩进命令\n                        editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_7__.INDENT_CONTENT_COMMAND, undefined);\n                    }\n                });\n            }\n        },\n        {\n            title: \"Outdent\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__.ArrowLeftIcon, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 228,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: function() {\n                editor.update(function() {\n                    var selection = (0,lexical__WEBPACK_IMPORTED_MODULE_7__.$getSelection)();\n                    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_7__.$isRangeSelection)(selection)) {\n                        return;\n                    }\n                    var nodes = selection.getNodes();\n                    var listItemNodes = nodes.filter(function(node) {\n                        return (0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$isListItemNode)(node) || (0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$isListItemNode)(node.getParent());\n                    });\n                    if (listItemNodes.length > 0) {\n                        // 如果选中的是列表项，使用专门的列表反缩进函数\n                        listItemNodes.forEach(function(node) {\n                            var listItemNode = (0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$isListItemNode)(node) ? node : node.getParent();\n                            if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$isListItemNode)(listItemNode)) {\n                                (0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$handleOutdent)(listItemNode);\n                            }\n                        });\n                    } else {\n                        // 如果不是列表项，使用通用的反缩进命令\n                        editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_7__.OUTDENT_CONTENT_COMMAND, undefined);\n                    }\n                });\n            }\n        }, \n    ];\n    return /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_3__.createPortal)(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed z-50 \".concat(toolbarBg, \" border rounded-lg p-1.5 flex space-x-0.5 shadow-lg\"),\n        style: {\n            top: position.top,\n            left: position.left,\n            transform: \"translateX(-50%)\",\n            backgroundColor: theme === \"dark\" ? \"#3f3f46\" : \"#e4e4e7\"\n        },\n        children: toolbarButtons.map(function(button, index) {\n            if (button.type === \"separator\") {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-px h-6 \".concat(theme === \"dark\" ? \"bg-gray-600\" : \"bg-gray-300\", \" mx-1\")\n                }, index, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                    lineNumber: 271,\n                    columnNumber: 25\n                }, _this);\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: button.action,\n                title: button.title,\n                className: \"\\n                            px-2.5 py-1.5 rounded transition-colors duration-150 min-w-[30px] h-7 flex items-center justify-center text-sm font-medium\\n                            \".concat(button.isActive ? buttonActive : \"\".concat(buttonText, \" \").concat(buttonHover), \"\\n                        \"),\n                style: {\n                    backgroundColor: button.isActive ? theme === \"dark\" ? \"#3185eb\" : \"#eab834\" : \"transparent\"\n                },\n                onMouseEnter: function(e) {\n                    if (!button.isActive) {\n                        e.currentTarget.style.backgroundColor = theme === \"dark\" ? \"#3185eb\" : \"#eab834\";\n                        if (theme === \"dark\") {\n                            e.currentTarget.style.color = \"white\";\n                        }\n                    }\n                },\n                onMouseLeave: function(e) {\n                    if (!button.isActive) {\n                        e.currentTarget.style.backgroundColor = \"transparent\";\n                        e.currentTarget.style.color = \"\";\n                    }\n                },\n                children: button.icon\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 279,\n                columnNumber: 21\n            }, _this);\n        })\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n        lineNumber: 259,\n        columnNumber: 9\n    }, this), document.body);\n}\n_s(FloatingToolbarPlugin, \"FbU03gSrzEPMPysVlTtv8K886Dg=\", false, function() {\n    return [\n        _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_6__.useLexicalComposerContext,\n        next_themes__WEBPACK_IMPORTED_MODULE_4__.useTheme\n    ];\n});\n_c = FloatingToolbarPlugin;\nvar _c;\n$RefreshReg$(_c, \"FloatingToolbarPlugin\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/editor/plugins/floating-toolbar-plugin.tsx\n"));

/***/ })

});