{"version": 3, "names": ["run", "config", "code", "ast", "file", "normalizeFile", "passes", "normalizeOptions", "opts", "transformFile", "e", "message", "filename", "outputCode", "outputMap", "generateCode", "metadata", "options", "undefined", "map", "sourceType", "program", "externalDependencies", "flattenToSet", "pluginPasses", "pluginPairs", "passPairs", "visitors", "plugin", "concat", "loadBlockHoistPlugin", "pass", "Plug<PERSON><PERSON><PERSON>", "key", "push", "visitor", "fn", "pre", "result", "call", "isThenable", "Error", "traverse", "merge", "wrapPluginVisitorMethod", "scope", "post", "val", "then"], "sources": ["../../src/transformation/index.ts"], "sourcesContent": ["import traverse from \"@babel/traverse\";\nimport type * as t from \"@babel/types\";\ntype SourceMap = any;\nimport type { <PERSON><PERSON> } from \"gensync\";\n\nimport type { ResolvedConfig, Plugin, PluginPasses } from \"../config\";\n\nimport PluginPass from \"./plugin-pass\";\nimport loadBlockHoistPlugin from \"./block-hoist-plugin\";\nimport normalizeOptions from \"./normalize-opts\";\nimport normalizeFile from \"./normalize-file\";\n\nimport generateCode from \"./file/generate\";\nimport type File from \"./file/file\";\n\nimport { flattenToSet } from \"../config/helpers/deep-array\";\n\nexport type FileResultCallback = {\n  (err: Error, file: null): void;\n  (err: null, file: FileResult | null): void;\n};\n\nexport type FileResult = {\n  metadata: { [key: string]: any };\n  options: { [key: string]: any };\n  ast: t.File | null;\n  code: string | null;\n  map: SourceMap | null;\n  sourceType: \"script\" | \"module\";\n  externalDependencies: Set<string>;\n};\n\nexport function* run(\n  config: ResolvedConfig,\n  code: string,\n  ast?: t.File | t.Program | null,\n): Handler<FileResult> {\n  const file = yield* normalizeFile(\n    config.passes,\n    normalizeOptions(config),\n    code,\n    ast,\n  );\n\n  const opts = file.opts;\n  try {\n    yield* transformFile(file, config.passes);\n  } catch (e) {\n    e.message = `${opts.filename ?? \"unknown file\"}: ${e.message}`;\n    if (!e.code) {\n      e.code = \"BABEL_TRANSFORM_ERROR\";\n    }\n    throw e;\n  }\n\n  let outputCode, outputMap;\n  try {\n    if (opts.code !== false) {\n      ({ outputCode, outputMap } = generateCode(config.passes, file));\n    }\n  } catch (e) {\n    e.message = `${opts.filename ?? \"unknown file\"}: ${e.message}`;\n    if (!e.code) {\n      e.code = \"BABEL_GENERATE_ERROR\";\n    }\n    throw e;\n  }\n\n  return {\n    metadata: file.metadata,\n    options: opts,\n    ast: opts.ast === true ? file.ast : null,\n    code: outputCode === undefined ? null : outputCode,\n    map: outputMap === undefined ? null : outputMap,\n    sourceType: file.ast.program.sourceType,\n    externalDependencies: flattenToSet(config.externalDependencies),\n  };\n}\n\nfunction* transformFile(file: File, pluginPasses: PluginPasses): Handler<void> {\n  for (const pluginPairs of pluginPasses) {\n    const passPairs: [Plugin, PluginPass][] = [];\n    const passes = [];\n    const visitors = [];\n\n    for (const plugin of pluginPairs.concat([loadBlockHoistPlugin()])) {\n      const pass = new PluginPass(file, plugin.key, plugin.options);\n\n      passPairs.push([plugin, pass]);\n      passes.push(pass);\n      visitors.push(plugin.visitor);\n    }\n\n    for (const [plugin, pass] of passPairs) {\n      const fn = plugin.pre;\n      if (fn) {\n        const result = fn.call(pass, file);\n\n        // @ts-expect-error - If we want to support async .pre\n        yield* [];\n\n        if (isThenable(result)) {\n          throw new Error(\n            `You appear to be using an plugin with an async .pre, ` +\n              `which your current version of Babel does not support. ` +\n              `If you're using a published plugin, you may need to upgrade ` +\n              `your @babel/core version.`,\n          );\n        }\n      }\n    }\n\n    // merge all plugin visitors into a single visitor\n    const visitor = traverse.visitors.merge(\n      visitors,\n      passes,\n      file.opts.wrapPluginVisitorMethod,\n    );\n    traverse(file.ast, visitor, file.scope);\n\n    for (const [plugin, pass] of passPairs) {\n      const fn = plugin.post;\n      if (fn) {\n        const result = fn.call(pass, file);\n\n        // @ts-expect-error - If we want to support async .post\n        yield* [];\n\n        if (isThenable(result)) {\n          throw new Error(\n            `You appear to be using an plugin with an async .post, ` +\n              `which your current version of Babel does not support. ` +\n              `If you're using a published plugin, you may need to upgrade ` +\n              `your @babel/core version.`,\n          );\n        }\n      }\n    }\n  }\n}\n\nfunction isThenable<T extends PromiseLike<any>>(val: any): val is T {\n  return (\n    !!val &&\n    (typeof val === \"object\" || typeof val === \"function\") &&\n    !!val.then &&\n    typeof val.then === \"function\"\n  );\n}\n"], "mappings": ";;;;;;;AAAA;EAAA;;EAAA;IAAA;EAAA;;EAAA;AAAA;;AAOA;;AACA;;AACA;;AACA;;AAEA;;AAGA;;AAiBO,UAAUA,GAAV,CACLC,MADK,EAELC,IAFK,EAGLC,GAHK,EAIgB;EACrB,MAAMC,IAAI,GAAG,OAAO,IAAAC,sBAAA,EAClBJ,MAAM,CAACK,MADW,EAElB,IAAAC,sBAAA,EAAiBN,MAAjB,CAFkB,EAGlBC,IAHkB,EAIlBC,GAJkB,CAApB;EAOA,MAAMK,IAAI,GAAGJ,IAAI,CAACI,IAAlB;;EACA,IAAI;IACF,OAAOC,aAAa,CAACL,IAAD,EAAOH,MAAM,CAACK,MAAd,CAApB;EACD,CAFD,CAEE,OAAOI,CAAP,EAAU;IAAA;;IACVA,CAAC,CAACC,OAAF,GAAa,GAAD,kBAAGH,IAAI,CAACI,QAAR,6BAAoB,cAAe,KAAIF,CAAC,CAACC,OAAQ,EAA7D;;IACA,IAAI,CAACD,CAAC,CAACR,IAAP,EAAa;MACXQ,CAAC,CAACR,IAAF,GAAS,uBAAT;IACD;;IACD,MAAMQ,CAAN;EACD;;EAED,IAAIG,UAAJ,EAAgBC,SAAhB;;EACA,IAAI;IACF,IAAIN,IAAI,CAACN,IAAL,KAAc,KAAlB,EAAyB;MACvB,CAAC;QAAEW,UAAF;QAAcC;MAAd,IAA4B,IAAAC,iBAAA,EAAad,MAAM,CAACK,MAApB,EAA4BF,IAA5B,CAA7B;IACD;EACF,CAJD,CAIE,OAAOM,CAAP,EAAU;IAAA;;IACVA,CAAC,CAACC,OAAF,GAAa,GAAD,mBAAGH,IAAI,CAACI,QAAR,8BAAoB,cAAe,KAAIF,CAAC,CAACC,OAAQ,EAA7D;;IACA,IAAI,CAACD,CAAC,CAACR,IAAP,EAAa;MACXQ,CAAC,CAACR,IAAF,GAAS,sBAAT;IACD;;IACD,MAAMQ,CAAN;EACD;;EAED,OAAO;IACLM,QAAQ,EAAEZ,IAAI,CAACY,QADV;IAELC,OAAO,EAAET,IAFJ;IAGLL,GAAG,EAAEK,IAAI,CAACL,GAAL,KAAa,IAAb,GAAoBC,IAAI,CAACD,GAAzB,GAA+B,IAH/B;IAILD,IAAI,EAAEW,UAAU,KAAKK,SAAf,GAA2B,IAA3B,GAAkCL,UAJnC;IAKLM,GAAG,EAAEL,SAAS,KAAKI,SAAd,GAA0B,IAA1B,GAAiCJ,SALjC;IAMLM,UAAU,EAAEhB,IAAI,CAACD,GAAL,CAASkB,OAAT,CAAiBD,UANxB;IAOLE,oBAAoB,EAAE,IAAAC,uBAAA,EAAatB,MAAM,CAACqB,oBAApB;EAPjB,CAAP;AASD;;AAED,UAAUb,aAAV,CAAwBL,IAAxB,EAAoCoB,YAApC,EAA+E;EAC7E,KAAK,MAAMC,WAAX,IAA0BD,YAA1B,EAAwC;IACtC,MAAME,SAAiC,GAAG,EAA1C;IACA,MAAMpB,MAAM,GAAG,EAAf;IACA,MAAMqB,QAAQ,GAAG,EAAjB;;IAEA,KAAK,MAAMC,MAAX,IAAqBH,WAAW,CAACI,MAAZ,CAAmB,CAAC,IAAAC,yBAAA,GAAD,CAAnB,CAArB,EAAmE;MACjE,MAAMC,IAAI,GAAG,IAAIC,mBAAJ,CAAe5B,IAAf,EAAqBwB,MAAM,CAACK,GAA5B,EAAiCL,MAAM,CAACX,OAAxC,CAAb;MAEAS,SAAS,CAACQ,IAAV,CAAe,CAACN,MAAD,EAASG,IAAT,CAAf;MACAzB,MAAM,CAAC4B,IAAP,CAAYH,IAAZ;MACAJ,QAAQ,CAACO,IAAT,CAAcN,MAAM,CAACO,OAArB;IACD;;IAED,KAAK,MAAM,CAACP,MAAD,EAASG,IAAT,CAAX,IAA6BL,SAA7B,EAAwC;MACtC,MAAMU,EAAE,GAAGR,MAAM,CAACS,GAAlB;;MACA,IAAID,EAAJ,EAAQ;QACN,MAAME,MAAM,GAAGF,EAAE,CAACG,IAAH,CAAQR,IAAR,EAAc3B,IAAd,CAAf;QAGA,OAAO,EAAP;;QAEA,IAAIoC,UAAU,CAACF,MAAD,CAAd,EAAwB;UACtB,MAAM,IAAIG,KAAJ,CACH,uDAAD,GACG,wDADH,GAEG,8DAFH,GAGG,2BAJC,CAAN;QAMD;MACF;IACF;;IAGD,MAAMN,OAAO,GAAGO,mBAAA,CAASf,QAAT,CAAkBgB,KAAlB,CACdhB,QADc,EAEdrB,MAFc,EAGdF,IAAI,CAACI,IAAL,CAAUoC,uBAHI,CAAhB;;IAKA,IAAAF,mBAAA,EAAStC,IAAI,CAACD,GAAd,EAAmBgC,OAAnB,EAA4B/B,IAAI,CAACyC,KAAjC;;IAEA,KAAK,MAAM,CAACjB,MAAD,EAASG,IAAT,CAAX,IAA6BL,SAA7B,EAAwC;MACtC,MAAMU,EAAE,GAAGR,MAAM,CAACkB,IAAlB;;MACA,IAAIV,EAAJ,EAAQ;QACN,MAAME,MAAM,GAAGF,EAAE,CAACG,IAAH,CAAQR,IAAR,EAAc3B,IAAd,CAAf;QAGA,OAAO,EAAP;;QAEA,IAAIoC,UAAU,CAACF,MAAD,CAAd,EAAwB;UACtB,MAAM,IAAIG,KAAJ,CACH,wDAAD,GACG,wDADH,GAEG,8DAFH,GAGG,2BAJC,CAAN;QAMD;MACF;IACF;EACF;AACF;;AAED,SAASD,UAAT,CAAgDO,GAAhD,EAAoE;EAClE,OACE,CAAC,CAACA,GAAF,KACC,OAAOA,GAAP,KAAe,QAAf,IAA2B,OAAOA,GAAP,KAAe,UAD3C,KAEA,CAAC,CAACA,GAAG,CAACC,IAFN,IAGA,OAAOD,GAAG,CAACC,IAAX,KAAoB,UAJtB;AAMD"}