{"version": 3, "names": ["buildMatchMemberExpression", "match", "allowPartial", "parts", "split", "member", "matchesPattern"], "sources": ["../../src/validators/buildMatchMemberExpression.ts"], "sourcesContent": ["import matchesPattern from \"./matchesPattern\";\nimport type * as t from \"..\";\n\n/**\n * Build a function that when called will return whether or not the\n * input `node` `MemberExpression` matches the input `match`.\n *\n * For example, given the match `React.createClass` it would match the\n * parsed nodes of `React.createClass` and `React[\"createClass\"]`.\n */\nexport default function buildMatchMemberExpression(\n  match: string,\n  allowPartial?: boolean,\n) {\n  const parts = match.split(\".\");\n\n  return (member: t.Node) => matchesPattern(member, parts, allowPartial);\n}\n"], "mappings": ";;;;;;;AAAA;;AAUe,SAASA,0BAAT,CACbC,KADa,EAEbC,YAFa,EAGb;EACA,MAAMC,KAAK,GAAGF,KAAK,CAACG,KAAN,CAAY,GAAZ,CAAd;EAEA,OAAQC,MAAD,IAAoB,IAAAC,uBAAA,EAAeD,MAAf,EAAuBF,KAAvB,EAA8BD,YAA9B,CAA3B;AACD"}