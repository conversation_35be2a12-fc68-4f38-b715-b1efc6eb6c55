if(!self.define){let e,s={};const n=(n,a)=>(n=new URL(n+".js",a).href,s[n]||new Promise((s=>{if("document"in self){const e=document.createElement("script");e.src=n,e.onload=s,document.head.appendChild(e)}else e=n,importScripts(n),s()})).then((()=>{let e=s[n];if(!e)throw new Error(`Module ${n} didn’t register its module`);return e})));self.define=(a,i)=>{const t=e||("document"in self?document.currentScript.src:"")||location.href;if(s[t])return;let c={};const o=e=>n(e,t),r={module:{uri:t},exports:c,require:o};s[t]=Promise.all(a.map((e=>r[e]||o(e)))).then((e=>(i(...e),c)))}}define(["./workbox-9a5fa34b"],(function(e){"use strict";importScripts(),self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"/_next/static/M9CES8gH_keLnO8PHGjDs/_buildManifest.js",revision:"48dbaf4145a80cd77bc83023aa1cf7cd"},{url:"/_next/static/M9CES8gH_keLnO8PHGjDs/_ssgManifest.js",revision:"b6652df95db52feb4daf4eca35380933"},{url:"/_next/static/chunks/198-e71aa9ee021ed871.js",revision:"e71aa9ee021ed871"},{url:"/_next/static/chunks/21-047f20d12147761d.js",revision:"047f20d12147761d"},{url:"/_next/static/chunks/275-cdbd8a9bfb61e173.js",revision:"cdbd8a9bfb61e173"},{url:"/_next/static/chunks/2a71daf2-8c2f3f8a2e8db50d.js",revision:"8c2f3f8a2e8db50d"},{url:"/_next/static/chunks/321-35e98bee12d29673.js",revision:"35e98bee12d29673"},{url:"/_next/static/chunks/373-4212ffb0d8cca5d9.js",revision:"4212ffb0d8cca5d9"},{url:"/_next/static/chunks/444-adec81242e845f2e.js",revision:"adec81242e845f2e"},{url:"/_next/static/chunks/503-2ae9a6630f2c1960.js",revision:"2ae9a6630f2c1960"},{url:"/_next/static/chunks/520c8229-72ec454883dade9a.js",revision:"72ec454883dade9a"},{url:"/_next/static/chunks/55-453539d6895e20e6.js",revision:"453539d6895e20e6"},{url:"/_next/static/chunks/632-34189f86c9febc88.js",revision:"34189f86c9febc88"},{url:"/_next/static/chunks/67-e26cf8d38f4186c4.js",revision:"e26cf8d38f4186c4"},{url:"/_next/static/chunks/862-74a1e35554d3d42b.js",revision:"74a1e35554d3d42b"},{url:"/_next/static/chunks/866-8466537436f47b1f.js",revision:"8466537436f47b1f"},{url:"/_next/static/chunks/964-225ba4d71e54e9c8.js",revision:"225ba4d71e54e9c8"},{url:"/_next/static/chunks/fc83e031-a7d67ff4b3a18fe1.js",revision:"a7d67ff4b3a18fe1"},{url:"/_next/static/chunks/framework-28c999baf2863c3d.js",revision:"28c999baf2863c3d"},{url:"/_next/static/chunks/main-551ecb8fa951e26e.js",revision:"551ecb8fa951e26e"},{url:"/_next/static/chunks/pages/%5Bid%5D-c180971f48480564.js",revision:"c180971f48480564"},{url:"/_next/static/chunks/pages/_app-bd3e3f10b6554109.js",revision:"bd3e3f10b6554109"},{url:"/_next/static/chunks/pages/_error-a51993fe870ec2c8.js",revision:"a51993fe870ec2c8"},{url:"/_next/static/chunks/pages/debug-68db70316d5fd0f2.js",revision:"68db70316d5fd0f2"},{url:"/_next/static/chunks/pages/index-5064e29bfa421307.js",revision:"5064e29bfa421307"},{url:"/_next/static/chunks/pages/login-3da18e2b7888d5f2.js",revision:"3da18e2b7888d5f2"},{url:"/_next/static/chunks/pages/settings-d0a968ac1a38fc6f.js",revision:"d0a968ac1a38fc6f"},{url:"/_next/static/chunks/pages/share/%5Bid%5D-0005817e197927af.js",revision:"0005817e197927af"},{url:"/_next/static/chunks/pages/tiptap-test-64d706d4f3c92cde.js",revision:"64d706d4f3c92cde"},{url:"/_next/static/chunks/polyfills-c67a75d1b6f99dc8.js",revision:"837c0df77fd5009c9e46d446188ecfd0"},{url:"/_next/static/chunks/webpack-38cee4c0e358b1a3.js",revision:"38cee4c0e358b1a3"},{url:"/_next/static/css/2c377e720aad43b2.css",revision:"2c377e720aad43b2"},{url:"/_next/static/css/6f394ea9a79389b2.css",revision:"6f394ea9a79389b2"},{url:"/_next/static/media/noto-sans-latin-100-normal.0a18f1e0.woff2",revision:"0a18f1e0"},{url:"/_next/static/media/noto-sans-latin-100-normal.1aa4e9f8.woff",revision:"1aa4e9f8"},{url:"/_next/static/media/noto-sans-latin-200-normal.1291c84d.woff2",revision:"1291c84d"},{url:"/_next/static/media/noto-sans-latin-200-normal.3cdc6b74.woff",revision:"3cdc6b74"},{url:"/_next/static/media/noto-sans-latin-300-normal.788aee92.woff2",revision:"788aee92"},{url:"/_next/static/media/noto-sans-latin-300-normal.d9ac6860.woff",revision:"d9ac6860"},{url:"/_next/static/media/noto-sans-latin-400-normal.93ca6acd.woff",revision:"93ca6acd"},{url:"/_next/static/media/noto-sans-latin-400-normal.947e053c.woff2",revision:"947e053c"},{url:"/_next/static/media/noto-sans-latin-500-normal.39be06a7.woff",revision:"39be06a7"},{url:"/_next/static/media/noto-sans-latin-500-normal.6c97332f.woff2",revision:"6c97332f"},{url:"/_next/static/media/noto-sans-latin-600-normal.0c5b2fc9.woff2",revision:"0c5b2fc9"},{url:"/_next/static/media/noto-sans-latin-600-normal.16995775.woff",revision:"16995775"},{url:"/_next/static/media/noto-sans-latin-700-normal.b942360d.woff2",revision:"b942360d"},{url:"/_next/static/media/noto-sans-latin-700-normal.d52bd54e.woff",revision:"d52bd54e"},{url:"/_next/static/media/noto-sans-latin-800-normal.801f8699.woff2",revision:"801f8699"},{url:"/_next/static/media/noto-sans-latin-800-normal.f0034835.woff",revision:"f0034835"},{url:"/_next/static/media/noto-sans-latin-900-normal.a5fa9a6c.woff2",revision:"a5fa9a6c"},{url:"/_next/static/media/noto-sans-latin-900-normal.f381b523.woff",revision:"f381b523"},{url:"/logo.svg",revision:"2ce226d8eabc7c1b95c4a1315f9ce747"},{url:"/logo_1280x640.png",revision:"9fb1324f40f84c763ffa8aabbc1b7658"},{url:"/static/icons/icon-128x128.png",revision:"5cc9308d160893350d4ad2691feb2c58"},{url:"/static/icons/icon-144x144.png",revision:"10fafd514f6d81a1d88f4d5ba53b2e0c"},{url:"/static/icons/icon-152x152.png",revision:"4fa10918244cb7435eb968ddde190f5f"},{url:"/static/icons/icon-192x192.png",revision:"d36705211a4f3d6c3efe797e19fb9483"},{url:"/static/icons/icon-384x384.png",revision:"985217d38708cfe7211370e5417ab8ca"},{url:"/static/icons/icon-512x512.png",revision:"ebe418bf2f95510611bd837780239508"},{url:"/static/icons/icon-72x72.png",revision:"8e40b65c0939608aa38ca80c667fa51d"},{url:"/static/icons/icon-96x96.png",revision:"89c73707b1a53875f450feb090287028"},{url:"/static/manifest.json",revision:"b09388ae32fb5292e36e842e2279ab04"}],{ignoreURLParametersMatching:[]}),e.cleanupOutdatedCaches(),e.registerRoute("/",new e.NetworkFirst({cacheName:"start-url",plugins:[{cacheWillUpdate:async({request:e,response:s,event:n,state:a})=>s&&"opaqueredirect"===s.type?new Response(s.body,{status:200,statusText:"OK",headers:s.headers}):s}]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:gstatic)\.com\/.*/i,new e.CacheFirst({cacheName:"google-fonts-webfonts",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:31536e3})]}),"GET"),e.registerRoute(/^https:\/\/fonts\.(?:googleapis)\.com\/.*/i,new e.StaleWhileRevalidate({cacheName:"google-fonts-stylesheets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:eot|otf|ttc|ttf|woff|woff2|font.css)$/i,new e.StaleWhileRevalidate({cacheName:"static-font-assets",plugins:[new e.ExpirationPlugin({maxEntries:4,maxAgeSeconds:604800})]}),"GET"),e.registerRoute(/\.(?:jpg|jpeg|gif|png|svg|ico|webp)$/i,new e.NetworkOnly,"GET"),e.registerRoute(/\/_next\/image\?url=.+$/i,new e.StaleWhileRevalidate({cacheName:"next-image",plugins:[new e.ExpirationPlugin({maxEntries:64,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp3|wav|ogg)$/i,new e.CacheFirst({cacheName:"static-audio-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:mp4)$/i,new e.CacheFirst({cacheName:"static-video-assets",plugins:[new e.RangeRequestsPlugin,new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:js)$/i,new e.StaleWhileRevalidate({cacheName:"static-js-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:css|less)$/i,new e.StaleWhileRevalidate({cacheName:"static-style-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\/_next\/data\/.+\/.+\.json$/i,new e.StaleWhileRevalidate({cacheName:"next-data",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute(/\.(?:json|xml|csv)$/i,new e.NetworkFirst({cacheName:"static-data-assets",plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute((({url:e})=>{if(!(self.origin===e.origin))return!1;const s=e.pathname;return!s.startsWith("/api/auth/")&&!!s.startsWith("/api/")}),new e.NetworkFirst({cacheName:"apis",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:16,maxAgeSeconds:86400})]}),"GET"),e.registerRoute((({url:e})=>{if(!(self.origin===e.origin))return!1;return!e.pathname.startsWith("/api/")}),new e.NetworkFirst({cacheName:"others",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:86400})]}),"GET"),e.registerRoute((({url:e})=>!(self.origin===e.origin)),new e.NetworkFirst({cacheName:"cross-origin",networkTimeoutSeconds:10,plugins:[new e.ExpirationPlugin({maxEntries:32,maxAgeSeconds:3600})]}),"GET")}));
