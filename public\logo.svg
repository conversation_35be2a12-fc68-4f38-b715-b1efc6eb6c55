<svg width="500" height="500" viewBox="0 0 500 500" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g clip-path="url(#clip0)">
        <g filter="url(#filter0_i)">
            <path d="M378 180C378 250.692 320.692 308 250 308C179.308 308 122 250.692 122 180C122 109.308 179.308 52 250 52C320.692 52 378 109.308 378 180Z"
                  stroke="#828282" stroke-width="50"/>
        </g>
        <g filter="url(#filter1_i)">
            <path d="M52.304 480.144C50.192 480.144 48.416 479.472 46.976 478.128C45.632 476.688 44.96 474.912 44.96 472.8V408.576C44.96 406.368 45.632 404.592 46.976 403.248C48.416 401.904 50.192 401.232 52.304 401.232C54.512 401.232 56.288 401.904 57.632 403.248C58.976 404.592 59.648 406.368 59.648 408.576V410.16C62.624 407.184 66.224 404.832 70.448 403.104C74.672 401.376 79.232 400.512 84.128 400.512C90.752 400.512 96.656 401.952 101.84 404.832C107.12 407.616 111.248 411.744 114.224 417.216C117.296 422.688 118.832 429.408 118.832 437.376V472.8C118.832 474.912 118.112 476.688 116.672 478.128C115.328 479.472 113.6 480.144 111.488 480.144C109.376 480.144 107.6 479.472 106.16 478.128C104.816 476.688 104.144 474.912 104.144 472.8V437.376C104.144 429.216 101.936 423.264 97.52 419.52C93.2 415.68 87.776 413.76 81.248 413.76C77.216 413.76 73.568 414.576 70.304 416.208C67.04 417.84 64.448 420.048 62.528 422.832C60.608 425.52 59.648 428.64 59.648 432.192V472.8C59.648 474.912 58.976 476.688 57.632 478.128C56.288 479.472 54.512 480.144 52.304 480.144ZM178.828 480.576C171.052 480.576 164.188 478.896 158.236 475.536C152.284 472.08 147.58 467.376 144.124 461.424C140.764 455.376 139.084 448.464 139.084 440.688C139.084 432.816 140.764 425.904 144.124 419.952C147.58 413.904 152.284 409.2 158.236 405.84C164.188 402.384 171.052 400.656 178.828 400.656C186.508 400.656 193.324 402.384 199.276 405.84C205.228 409.2 209.884 413.904 213.244 419.952C216.7 425.904 218.428 432.816 218.428 440.688C218.428 448.464 216.748 455.376 213.388 461.424C210.028 467.376 205.372 472.08 199.42 475.536C193.468 478.896 186.604 480.576 178.828 480.576ZM178.828 467.616C183.82 467.616 188.236 466.464 192.076 464.16C195.916 461.856 198.892 458.688 201.004 454.656C203.212 450.624 204.316 445.968 204.316 440.688C204.316 435.408 203.212 430.752 201.004 426.72C198.892 422.592 195.916 419.376 192.076 417.072C188.236 414.768 183.82 413.616 178.828 413.616C173.836 413.616 169.42 414.768 165.58 417.072C161.74 419.376 158.716 422.592 156.508 426.72C154.3 430.752 153.196 435.408 153.196 440.688C153.196 445.968 154.3 450.624 156.508 454.656C158.716 458.688 161.74 461.856 165.58 464.16C169.42 466.464 173.836 467.616 178.828 467.616ZM268.944 480C264.144 480 259.824 478.8 255.984 476.4C252.144 473.904 249.12 470.544 246.912 466.32C244.704 462.096 243.6 457.344 243.6 452.064V416.496H236.976C234.96 416.496 233.328 415.92 232.08 414.768C230.832 413.616 230.208 412.176 230.208 410.448C230.208 408.528 230.832 406.992 232.08 405.84C233.328 404.688 234.96 404.112 236.976 404.112H243.6V383.376C243.6 381.264 244.272 379.536 245.616 378.192C246.96 376.848 248.688 376.176 250.8 376.176C252.912 376.176 254.64 376.848 255.984 378.192C257.328 379.536 258 381.264 258 383.376V404.112H270.24C272.256 404.112 273.888 404.688 275.136 405.84C276.384 406.992 277.008 408.528 277.008 410.448C277.008 412.176 276.384 413.616 275.136 414.768C273.888 415.92 272.256 416.496 270.24 416.496H258V452.064C258 455.904 259.056 459.12 261.168 461.712C263.28 464.304 265.872 465.6 268.944 465.6H273.84C275.568 465.6 277.008 466.272 278.16 467.616C279.408 468.96 280.032 470.688 280.032 472.8C280.032 474.912 279.216 476.64 277.584 477.984C276.048 479.328 274.032 480 271.536 480H268.944ZM335.093 480.576C327.125 480.576 320.021 478.896 313.781 475.536C307.637 472.08 302.789 467.376 299.237 461.424C295.781 455.376 294.053 448.464 294.053 440.688C294.053 432.816 295.685 425.904 298.949 419.952C302.309 413.904 306.917 409.2 312.773 405.84C318.629 402.384 325.349 400.656 332.933 400.656C340.421 400.656 346.853 402.336 352.229 405.696C357.605 408.96 361.685 413.52 364.469 419.376C367.349 425.136 368.789 431.808 368.789 439.392C368.789 441.216 368.165 442.752 366.917 444C365.669 445.152 364.085 445.728 362.165 445.728H307.445C308.501 452.352 311.525 457.776 316.517 462C321.605 466.128 327.797 468.192 335.093 468.192C338.069 468.192 341.093 467.664 344.165 466.608C347.333 465.456 349.877 464.16 351.797 462.72C353.237 461.664 354.773 461.136 356.405 461.136C358.133 461.04 359.621 461.52 360.869 462.576C362.501 464.016 363.365 465.6 363.461 467.328C363.557 469.056 362.789 470.544 361.157 471.792C357.893 474.384 353.813 476.496 348.917 478.128C344.117 479.76 339.509 480.576 335.093 480.576ZM332.933 413.04C325.829 413.04 320.117 415.008 315.797 418.944C311.477 422.88 308.741 427.968 307.589 434.208H355.541C354.677 428.064 352.325 423.024 348.485 419.088C344.645 415.056 339.461 413.04 332.933 413.04ZM417.542 480.576C410.534 480.576 404.246 478.848 398.678 475.392C393.11 471.84 388.694 467.088 385.43 461.136C382.262 455.088 380.678 448.272 380.678 440.688C380.678 433.104 382.406 426.288 385.862 420.24C389.414 414.192 394.166 409.44 400.118 405.984C406.166 402.432 412.934 400.656 420.422 400.656C427.91 400.656 434.63 402.432 440.582 405.984C446.534 409.44 451.238 414.192 454.694 420.24C458.246 426.288 460.022 433.104 460.022 440.688V472.8C460.022 474.912 459.302 476.688 457.862 478.128C456.518 479.472 454.79 480.144 452.678 480.144C450.566 480.144 448.79 479.472 447.35 478.128C446.006 476.688 445.334 474.912 445.334 472.8V467.472C441.974 471.504 437.894 474.72 433.094 477.12C428.39 479.424 423.206 480.576 417.542 480.576ZM420.422 467.616C425.318 467.616 429.686 466.464 433.526 464.16C437.366 461.76 440.39 458.544 442.598 454.512C444.806 450.384 445.91 445.776 445.91 440.688C445.91 435.504 444.806 430.896 442.598 426.864C440.39 422.736 437.366 419.52 433.526 417.216C429.686 414.816 425.318 413.616 420.422 413.616C415.622 413.616 411.254 414.816 407.318 417.216C403.478 419.52 400.406 422.736 398.102 426.864C395.894 430.896 394.79 435.504 394.79 440.688C394.79 445.776 395.894 450.384 398.102 454.512C400.406 458.544 403.478 461.76 407.318 464.16C411.254 466.464 415.622 467.616 420.422 467.616Z"
                  fill="#828282"/>
        </g>
    </g>
    <defs>
        <filter id="filter0_i" x="97" y="27" width="306" height="310" filterUnits="userSpaceOnUse"
                color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="4"/>
            <feGaussianBlur stdDeviation="2"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
            <feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
        </filter>
        <filter id="filter1_i" x="44.96" y="376.176" width="415.062" height="108.4" filterUnits="userSpaceOnUse"
                color-interpolation-filters="sRGB">
            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
            <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                           result="hardAlpha"/>
            <feOffset dy="4"/>
            <feGaussianBlur stdDeviation="2"/>
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
            <feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
        </filter>
        <clipPath id="clip0">
            <rect width="500" height="500" fill="white"/>
        </clipPath>
    </defs>
</svg>
