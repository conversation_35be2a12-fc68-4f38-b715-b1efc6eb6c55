"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/notes";
exports.ids = ["pages/api/notes"];
exports.modules = {

/***/ "@atlaskit/tree":
/*!*********************************!*\
  !*** external "@atlaskit/tree" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@atlaskit/tree");

/***/ }),

/***/ "csrf":
/*!***********************!*\
  !*** external "csrf" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("csrf");

/***/ }),

/***/ "express-rate-limit":
/*!*************************************!*\
  !*** external "express-rate-limit" ***!
  \*************************************/
/***/ ((module) => {

module.exports = require("express-rate-limit");

/***/ }),

/***/ "js-yaml":
/*!**************************!*\
  !*** external "js-yaml" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("js-yaml");

/***/ }),

/***/ "lodash":
/*!*************************!*\
  !*** external "lodash" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("lodash");

/***/ }),

/***/ "lzutf8":
/*!*************************!*\
  !*** external "lzutf8" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("lzutf8");

/***/ }),

/***/ "md5":
/*!**********************!*\
  !*** external "md5" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("md5");

/***/ }),

/***/ "nanoid":
/*!*************************!*\
  !*** external "nanoid" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("nanoid");

/***/ }),

/***/ "next-connect":
/*!*******************************!*\
  !*** external "next-connect" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("next-connect");

/***/ }),

/***/ "next-iron-session":
/*!************************************!*\
  !*** external "next-iron-session" ***!
  \************************************/
/***/ ((module) => {

module.exports = require("next-iron-session");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("pg");

/***/ }),

/***/ "pino":
/*!***********************!*\
  !*** external "pino" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("pino");

/***/ }),

/***/ "pino-pretty":
/*!******************************!*\
  !*** external "pino-pretty" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("pino-pretty");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(api)/./libs/server/config.ts":
/*!*******************************!*\
  !*** ./libs/server/config.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"config\": () => (/* binding */ config),\n/* harmony export */   \"loadConfig\": () => (/* binding */ loadConfig),\n/* harmony export */   \"loadConfigAndListErrors\": () => (/* binding */ loadConfigAndListErrors)\n/* harmony export */ });\n/* harmony import */ var js_yaml__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-yaml */ \"js-yaml\");\n/* harmony import */ var js_yaml__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(js_yaml__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var libs_shared_env__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! libs/shared/env */ \"(api)/./libs/shared/env.ts\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var libs_server_debugging__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! libs/server/debugging */ \"(api)/./libs/server/debugging.ts\");\n/**\n * Server Configuration\n * Based on the open-source project Notea, originally created by qingwei-li<<EMAIL>>.\n * Modified and maintained by waycaan, 2025.\n *\n * Key modifications:\n * - Replaced S3 storage configuration with PostgreSQL\n * - Added support for multiple PostgreSQL providers (Neon, Supabase, self-hosted)\n * - Enhanced error handling and validation for database connections\n */ \n\n\n\nconst logger = (0,libs_server_debugging__WEBPACK_IMPORTED_MODULE_3__.createLogger)(\"config\");\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nlet loaded = undefined;\nvar ErrTitle;\n(function(ErrTitle) {\n    ErrTitle[\"CONFIG_FILE_READ_FAIL\"] = \"Failed to load configuration file\";\n    ErrTitle[\"INVALID_AUTH_CONFIG\"] = \"Invalid authorisation configuration\";\n    ErrTitle[\"CONFIG_FILE_PARSE_FAIL\"] = \"Could not parse configuration file\";\n    ErrTitle[\"INVALID_STORE_CONFIG\"] = \"Invalid store configuration\";\n})(ErrTitle || (ErrTitle = {}));\nvar ErrInstruction;\n(function(ErrInstruction) {\n    ErrInstruction[\"ENV_EDIT\"] = \"If using Vercel or Netlify, go to the environment settings. If using an environment file, edit that file.\";\n    ErrInstruction[\"CONFIG_FILE_OPEN\"] = \"Edit the configuration file. Note that this does not work for Vercel nor Netlify.\";\n})(ErrInstruction || (ErrInstruction = {}));\nfunction loadConfigAndListErrors() {\n    logger.debug(\"Loading configuration from scratch (loadConfigAndListErrors)\");\n    // TODO: More config errors\n    const configFile = libs_shared_env__WEBPACK_IMPORTED_MODULE_1__.getEnvRaw(\"CONFIG_FILE\", false) ?? \"./notea.yml\";\n    const errors = [];\n    function tryElseAddError(f, issue) {\n        try {\n            f();\n        } catch (e) {\n            errors.push(issue(e));\n        }\n    }\n    let baseConfig = {};\n    if ((0,fs__WEBPACK_IMPORTED_MODULE_2__.existsSync)(configFile)) {\n        let data;\n        try {\n            data = (0,fs__WEBPACK_IMPORTED_MODULE_2__.readFileSync)(configFile, \"utf-8\");\n        } catch (e) {\n            errors.push({\n                name: ErrTitle.CONFIG_FILE_READ_FAIL,\n                description: \"The configuration file couldn't be read.\",\n                cause: (0,libs_server_debugging__WEBPACK_IMPORTED_MODULE_3__.coerceToValidCause)(e),\n                severity: libs_server_debugging__WEBPACK_IMPORTED_MODULE_3__.IssueSeverity.WARNING,\n                category: libs_server_debugging__WEBPACK_IMPORTED_MODULE_3__.IssueCategory.CONFIG,\n                fixes: [\n                    {\n                        description: \"Make sure Notea has read access to the configuration file\",\n                        recommendation: libs_server_debugging__WEBPACK_IMPORTED_MODULE_3__.IssueFixRecommendation.NEUTRAL\n                    },\n                    {\n                        description: \"Make sure no other programme is using the configuration file\",\n                        recommendation: libs_server_debugging__WEBPACK_IMPORTED_MODULE_3__.IssueFixRecommendation.NEUTRAL\n                    }, \n                ]\n            });\n        }\n        if (data) {\n            try {\n                baseConfig = js_yaml__WEBPACK_IMPORTED_MODULE_0___default().load(data);\n            } catch (e1) {\n                errors.push({\n                    name: ErrTitle.CONFIG_FILE_PARSE_FAIL,\n                    description: \"The configuration file could not be parsed, probably due to a syntax error.\",\n                    severity: libs_server_debugging__WEBPACK_IMPORTED_MODULE_3__.IssueSeverity.WARNING,\n                    category: libs_server_debugging__WEBPACK_IMPORTED_MODULE_3__.IssueCategory.CONFIG,\n                    cause: (0,libs_server_debugging__WEBPACK_IMPORTED_MODULE_3__.coerceToValidCause)(e1),\n                    fixes: [\n                        {\n                            description: \"Check your configuration file for syntax errors.\",\n                            recommendation: libs_server_debugging__WEBPACK_IMPORTED_MODULE_3__.IssueFixRecommendation.RECOMMENDED\n                        }, \n                    ]\n                });\n            }\n        }\n    }\n    const disablePassword = libs_shared_env__WEBPACK_IMPORTED_MODULE_1__.parseBool(libs_shared_env__WEBPACK_IMPORTED_MODULE_1__.getEnvRaw(\"DISABLE_PASSWORD\", false), false);\n    let auth = {\n        type: \"none\"\n    };\n    if (!disablePassword) {\n        const envPassword = libs_shared_env__WEBPACK_IMPORTED_MODULE_1__.getEnvRaw(\"PASSWORD\", false);\n        if (baseConfig.auth === undefined) {\n            if (envPassword === undefined) {\n                errors.push({\n                    name: ErrTitle.INVALID_AUTH_CONFIG,\n                    description: \"Neither the configuration file, the PASSWORD environment variable, nor the DISABLE_PASSWORD environment variable was set.\",\n                    severity: libs_server_debugging__WEBPACK_IMPORTED_MODULE_3__.IssueSeverity.FATAL_ERROR,\n                    category: libs_server_debugging__WEBPACK_IMPORTED_MODULE_3__.IssueCategory.CONFIG,\n                    fixes: [\n                        {\n                            description: \"Set the PASSWORD environment variable\",\n                            recommendation: libs_server_debugging__WEBPACK_IMPORTED_MODULE_3__.IssueFixRecommendation.RECOMMENDED,\n                            steps: [\n                                ErrInstruction.ENV_EDIT,\n                                \"Set a variable with PASSWORD as key and your desired password as the variable.\", \n                            ]\n                        },\n                        {\n                            description: \"Include an auth section in the configuration file\",\n                            recommendation: libs_server_debugging__WEBPACK_IMPORTED_MODULE_3__.IssueFixRecommendation.RECOMMENDED,\n                            steps: [\n                                ErrInstruction.CONFIG_FILE_OPEN,\n                                \"Configure the auth section as you desire the authentication to work. Note that as of now it only supports basic authentication.\", \n                            ]\n                        },\n                        {\n                            description: \"Disable authentication\",\n                            recommendation: libs_server_debugging__WEBPACK_IMPORTED_MODULE_3__.IssueFixRecommendation.NOT_ADVISED,\n                            steps: [\n                                \"Set either the DISABLE_PASSWORD environment variable or set auth.type to none in the configuration file.\", \n                            ]\n                        }, \n                    ]\n                });\n            } else {\n                auth = {\n                    type: \"basic\",\n                    password: envPassword.toString()\n                };\n            }\n        } else {\n            auth = baseConfig.auth;\n            if (envPassword !== undefined) {\n                errors.push({\n                    name: ErrTitle.INVALID_AUTH_CONFIG,\n                    description: \"The PASSWORD environment variable cannot be set when the file configuration contains an auth section.\",\n                    category: libs_server_debugging__WEBPACK_IMPORTED_MODULE_3__.IssueCategory.CONFIG,\n                    severity: libs_server_debugging__WEBPACK_IMPORTED_MODULE_3__.IssueSeverity.FATAL_ERROR,\n                    fixes: [\n                        {\n                            description: \"Don't set the PASSWORD environment variable prior to running Notea.\",\n                            recommendation: libs_server_debugging__WEBPACK_IMPORTED_MODULE_3__.IssueFixRecommendation.RECOMMENDED\n                        },\n                        {\n                            description: \"Remove the auth section from your file configuration.\",\n                            recommendation: libs_server_debugging__WEBPACK_IMPORTED_MODULE_3__.IssueFixRecommendation.NEUTRAL\n                        }, \n                    ]\n                });\n            }\n            if (auth.type === \"basic\") {\n                if (auth.users) {\n                    errors.push({\n                        name: ErrTitle.INVALID_AUTH_CONFIG,\n                        description: \"Multiple users are not yet supported\",\n                        severity: libs_server_debugging__WEBPACK_IMPORTED_MODULE_3__.IssueSeverity.FATAL_ERROR,\n                        category: libs_server_debugging__WEBPACK_IMPORTED_MODULE_3__.IssueCategory.CONFIG,\n                        fixes: [\n                            {\n                                description: \"Change to a single-user configuration.\",\n                                recommendation: libs_server_debugging__WEBPACK_IMPORTED_MODULE_3__.IssueFixRecommendation.RECOMMENDED\n                            }, \n                        ]\n                    });\n                } else {\n                    auth.username = auth.username?.toString();\n                    auth.password = auth.password.toString();\n                }\n            }\n        }\n    } else {\n        auth = {\n            type: \"none\"\n        };\n    }\n    let store;\n    // This version only supports PostgreSQL\n    const postgresUrl = libs_shared_env__WEBPACK_IMPORTED_MODULE_1__.getEnvRaw(\"DATABASE_URL\", false);\n    const supabaseUrl = libs_shared_env__WEBPACK_IMPORTED_MODULE_1__.getEnvRaw(\"SUPABASE_URL\", false);\n    const supabaseKey = libs_shared_env__WEBPACK_IMPORTED_MODULE_1__.getEnvRaw(\"SUPABASE_ANON_KEY\", false);\n    const dbProvider = libs_shared_env__WEBPACK_IMPORTED_MODULE_1__.getEnvRaw(\"DB_PROVIDER\", false);\n    let provider = \"self-hosted\";\n    let connectionString = postgresUrl || \"\";\n    let ssl = false;\n    // Auto-detect provider based on connection string or explicit provider setting\n    if (dbProvider) {\n        provider = dbProvider;\n    } else if (postgresUrl) {\n        if (postgresUrl.includes(\"neon.tech\") || postgresUrl.includes(\"neon.\")) {\n            provider = \"neon\";\n        } else if (postgresUrl.includes(\"supabase.co\") || postgresUrl.includes(\"supabase.\")) {\n            provider = \"supabase\";\n        } else {\n            provider = \"self-hosted\";\n        }\n    }\n    // Configure based on provider\n    if (supabaseUrl && supabaseKey && provider === \"supabase\") {\n        connectionString = postgresUrl || `postgresql://postgres:[YOUR-PASSWORD]@${supabaseUrl.replace(\"https://\", \"\").replace(\"http://\", \"\")}:5432/postgres`;\n        ssl = true;\n    } else if (postgresUrl) {\n        connectionString = postgresUrl;\n        // Auto-detect SSL requirements\n        ssl = postgresUrl.includes(\"sslmode=require\") || postgresUrl.includes(\"ssl=true\") || provider === \"neon\" || provider === \"supabase\";\n    }\n    store = {\n        type: \"postgresql\",\n        connectionString,\n        prefix: libs_shared_env__WEBPACK_IMPORTED_MODULE_1__.getEnvRaw(\"STORE_PREFIX\", false) || \"\",\n        provider,\n        ssl\n    };\n    if (!connectionString) {\n        errors.push({\n            name: \"PostgreSQL connection string missing\",\n            description: \"DATABASE_URL environment variable is required, or SUPABASE_URL + SUPABASE_ANON_KEY for Supabase\",\n            severity: libs_server_debugging__WEBPACK_IMPORTED_MODULE_3__.IssueSeverity.FATAL_ERROR,\n            category: libs_server_debugging__WEBPACK_IMPORTED_MODULE_3__.IssueCategory.CONFIG,\n            fixes: [\n                {\n                    description: \"Set the DATABASE_URL environment variable for self-hosted PostgreSQL\",\n                    recommendation: libs_server_debugging__WEBPACK_IMPORTED_MODULE_3__.IssueFixRecommendation.RECOMMENDED,\n                    steps: [\n                        ErrInstruction.ENV_EDIT,\n                        \"Set DATABASE_URL to your PostgreSQL connection string (e.g., postgresql://user:password@host:port/database)\", \n                    ]\n                },\n                {\n                    description: \"Set Supabase environment variables\",\n                    recommendation: libs_server_debugging__WEBPACK_IMPORTED_MODULE_3__.IssueFixRecommendation.RECOMMENDED,\n                    steps: [\n                        ErrInstruction.ENV_EDIT,\n                        \"Set SUPABASE_URL to your Supabase project URL\",\n                        \"Set SUPABASE_ANON_KEY to your Supabase anon key\",\n                        \"Set DATABASE_URL to your Supabase PostgreSQL connection string\", \n                    ]\n                }, \n            ]\n        });\n    }\n    let server;\n    if (!baseConfig.server) {\n        server = {};\n    } else {\n        server = baseConfig.server;\n    }\n    {\n        server.useSecureCookies = libs_shared_env__WEBPACK_IMPORTED_MODULE_1__.parseBool(libs_shared_env__WEBPACK_IMPORTED_MODULE_1__.getEnvRaw(\"COOKIE_SECURE\", false), server.useSecureCookies ?? \"development\" === \"production\");\n        server.baseUrl = libs_shared_env__WEBPACK_IMPORTED_MODULE_1__.getEnvRaw(\"BASE_URL\", false) ?? server.baseUrl;\n    }\n    return {\n        config: {\n            auth,\n            store,\n            server\n        },\n        errors\n    };\n}\nconst MAX_ERRORS = 2;\nfunction loadConfig() {\n    const result = loadConfigAndListErrors();\n    if (!result.config) {\n        const { errors  } = result;\n        let name = errors.slice(0, MAX_ERRORS).map((v)=>v.name).join(\", \");\n        if (errors.length > MAX_ERRORS) {\n            const rest = errors.length - MAX_ERRORS;\n            name += \" and \" + rest + \" other error\" + (rest > 1 ? \"s\" : \"\");\n        }\n        throw new Error(name);\n    }\n    loaded = result.config;\n    return loaded;\n}\nfunction config() {\n    if (!loaded) {\n        logger.debug(\"Loading configuration\");\n        loadConfig();\n        logger.debug(\"Successfully loaded configuration\");\n    }\n    return loaded;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./libs/server/config.ts\n");

/***/ }),

/***/ "(api)/./libs/server/connect.ts":
/*!********************************!*\
  !*** ./libs/server/connect.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"api\": () => (/* binding */ api),\n/* harmony export */   \"ssr\": () => (/* binding */ ssr)\n/* harmony export */ });\n/* harmony import */ var next_connect__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-connect */ \"next-connect\");\n/* harmony import */ var next_connect__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_connect__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _middlewares_error__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./middlewares/error */ \"(api)/./libs/server/middlewares/error.ts\");\n/* harmony import */ var _middlewares_session__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./middlewares/session */ \"(api)/./libs/server/middlewares/session.ts\");\n/* harmony import */ var libs_server_middlewares_csrf__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! libs/server/middlewares/csrf */ \"(api)/./libs/server/middlewares/csrf.ts\");\n/* harmony import */ var _middlewares_store__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./middlewares/store */ \"(api)/./libs/server/middlewares/store.ts\");\n\n\n\n\n\nconst api = ()=>next_connect__WEBPACK_IMPORTED_MODULE_0___default()({\n        onError: _middlewares_error__WEBPACK_IMPORTED_MODULE_1__.onError\n    }).use(_middlewares_error__WEBPACK_IMPORTED_MODULE_1__.useError).use(_middlewares_store__WEBPACK_IMPORTED_MODULE_4__.useStore).use(_middlewares_session__WEBPACK_IMPORTED_MODULE_2__.useSession).use(libs_server_middlewares_csrf__WEBPACK_IMPORTED_MODULE_3__.useCsrf);\n// used by getServerSideProps\nconst ssr = ()=>next_connect__WEBPACK_IMPORTED_MODULE_0___default()({\n        onError: _middlewares_error__WEBPACK_IMPORTED_MODULE_1__.onErrorWithNext\n    })// init props\n    .use((req, _res, next)=>{\n        req.props = {};\n        req.state = {};\n        next();\n    }).use(_middlewares_error__WEBPACK_IMPORTED_MODULE_1__.useError).use(_middlewares_store__WEBPACK_IMPORTED_MODULE_4__.useStore);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./libs/server/connect.ts\n");

/***/ }),

/***/ "(api)/./libs/server/debugging.ts":
/*!**********************************!*\
  !*** ./libs/server/debugging.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"collectDebugInformation\": () => (/* binding */ collectDebugInformation),\n/* harmony export */   \"createLogger\": () => (/* binding */ createLogger),\n/* harmony export */   \"findIssues\": () => (/* binding */ findIssues),\n/* harmony export */   \"reportRuntimeIssue\": () => (/* binding */ reportRuntimeIssue),\n/* harmony export */   \"setKeyedRuntimeIssue\": () => (/* binding */ setKeyedRuntimeIssue)\n/* harmony export */ });\n/* harmony import */ var libs_server_config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! libs/server/config */ \"(api)/./libs/server/config.ts\");\n/* harmony import */ var pino__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! pino */ \"pino\");\n/* harmony import */ var pino__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(pino__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var pino_pretty__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! pino-pretty */ \"pino-pretty\");\n/* harmony import */ var pino_pretty__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(pino_pretty__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var libs_shared_debugging__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! libs/shared/debugging */ \"(api)/./libs/shared/debugging.ts\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in libs_shared_debugging__WEBPACK_IMPORTED_MODULE_5__) if([\"default\",\"reportRuntimeIssue\",\"setKeyedRuntimeIssue\",\"findIssues\",\"collectDebugInformation\",\"createLogger\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => libs_shared_debugging__WEBPACK_IMPORTED_MODULE_5__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n\n\n\n // here's a lil' lesson in trickery\nconst serialRuntimeIssues = [];\nconst keyedRuntimeIssues = {};\nfunction reportRuntimeIssue(issue) {\n    const complete = {\n        ...issue,\n        isRuntime: true\n    };\n    serialRuntimeIssues.push(complete);\n}\nfunction setKeyedRuntimeIssue(key, issue) {\n    if (issue === null) {\n        delete keyedRuntimeIssues[key];\n    } else {\n        keyedRuntimeIssues[key] = issue;\n    }\n}\nfunction getAllKeyedRuntimeIssues() {\n    const values = [];\n    Object.values(keyedRuntimeIssues).forEach((v)=>{\n        if (v != undefined) {\n            values.push(v);\n        }\n    });\n    return values;\n}\nfunction findIssues() {\n    const issues = [];\n    try {\n        const cfg = (0,libs_server_config__WEBPACK_IMPORTED_MODULE_0__.loadConfigAndListErrors)();\n        issues.push(...cfg.errors);\n    } catch (e) {\n        issues.push({\n            severity: libs_shared_debugging__WEBPACK_IMPORTED_MODULE_5__.IssueSeverity.FATAL_ERROR,\n            category: libs_shared_debugging__WEBPACK_IMPORTED_MODULE_5__.IssueCategory.CONFIG,\n            name: \"Cannot load config\",\n            cause: (0,libs_shared_debugging__WEBPACK_IMPORTED_MODULE_5__.coerceToValidCause)(e),\n            fixes: []\n        });\n    }\n    issues.push(...serialRuntimeIssues, ...getAllKeyedRuntimeIssues());\n    return issues;\n}\nfunction collectDebugInformation() {\n    const issues = findIssues();\n    return {\n        issues,\n        logs: []\n    };\n}\nfunction getLogFile(name) {\n    const dir = path__WEBPACK_IMPORTED_MODULE_3__.resolve(process.cwd(), process.env.LOG_DIRECTORY ?? \"logs\");\n    if (!fs__WEBPACK_IMPORTED_MODULE_4__.existsSync(dir)) {\n        fs__WEBPACK_IMPORTED_MODULE_4__.mkdirSync(dir, {\n            recursive: true\n        });\n    }\n    return path__WEBPACK_IMPORTED_MODULE_3__.resolve(dir, `${name}.log`);\n}\nconst loggerTransport = [\n    {\n        stream: pino_pretty__WEBPACK_IMPORTED_MODULE_2___default()(),\n        level: \"info\"\n    }\n];\ntry {\n    loggerTransport.push({\n        stream: fs__WEBPACK_IMPORTED_MODULE_4__.createWriteStream(getLogFile(\"debug\"), {\n            flags: \"a\"\n        }),\n        level: \"debug\"\n    });\n} catch (e) {\n    // well, whoops!\n    console.warn(\"No file logs: %O\", e);\n}\nconst multistream = pino__WEBPACK_IMPORTED_MODULE_1___default().multistream(loggerTransport);\nfunction createLogger(name) {\n    return pino__WEBPACK_IMPORTED_MODULE_1___default()({\n        name,\n        level: \"trace\"\n    }, multistream);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./libs/server/debugging.ts\n");

/***/ }),

/***/ "(api)/./libs/server/meta.ts":
/*!*****************************!*\
  !*** ./libs/server/meta.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"jsonToMeta\": () => (/* binding */ jsonToMeta),\n/* harmony export */   \"metaToJson\": () => (/* binding */ metaToJson)\n/* harmony export */ });\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var libs_shared_str__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! libs/shared/str */ \"(api)/./libs/shared/str.ts\");\n/* harmony import */ var libs_shared_meta__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! libs/shared/meta */ \"(api)/./libs/shared/meta.ts\");\n\n\n\nfunction jsonToMeta(meta) {\n    const metaData = {};\n    if (meta) {\n        libs_shared_meta__WEBPACK_IMPORTED_MODULE_2__.PAGE_META_KEY.forEach((key)=>{\n            const value = meta[key];\n            if (!(0,lodash__WEBPACK_IMPORTED_MODULE_0__.isNil)(value)) {\n                metaData[key] = (0,libs_shared_str__WEBPACK_IMPORTED_MODULE_1__.strCompress)(value.toString());\n            }\n        });\n    }\n    return metaData;\n}\nfunction metaToJson(metaData) {\n    const meta = {};\n    if (metaData) {\n        libs_shared_meta__WEBPACK_IMPORTED_MODULE_2__.PAGE_META_KEY.forEach((key)=>{\n            const value = metaData[key];\n            if (!(0,lodash__WEBPACK_IMPORTED_MODULE_0__.isNil)(value)) {\n                const strValue = (0,libs_shared_str__WEBPACK_IMPORTED_MODULE_1__.strDecompress)(value) || null;\n                if (libs_shared_meta__WEBPACK_IMPORTED_MODULE_2__.NUMBER_KEYS.includes(key)) {\n                    meta[key] = (0,lodash__WEBPACK_IMPORTED_MODULE_0__.toNumber)(strValue);\n                } else {\n                    meta[key] = strValue;\n                }\n            } else if (key === \"deleted\") {\n                meta[key] = value ? (0,libs_shared_str__WEBPACK_IMPORTED_MODULE_1__.strDecompress)(value) : libs_shared_meta__WEBPACK_IMPORTED_MODULE_2__.NOTE_DELETED.NORMAL;\n            } else if (key === \"shared\") {\n                meta[key] = value ? (0,libs_shared_str__WEBPACK_IMPORTED_MODULE_1__.strDecompress)(value) : libs_shared_meta__WEBPACK_IMPORTED_MODULE_2__.NOTE_SHARED.PRIVATE;\n            } else if (key === \"pinned\") {\n                meta[key] = value ? (0,libs_shared_str__WEBPACK_IMPORTED_MODULE_1__.strDecompress)(value) : libs_shared_meta__WEBPACK_IMPORTED_MODULE_2__.NOTE_PINNED.UNPINNED;\n            } else if (key === \"editorsize\") {\n                meta[key] = value ? (0,libs_shared_str__WEBPACK_IMPORTED_MODULE_1__.strDecompress)(value) : null;\n            }\n        });\n    }\n    return meta;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./libs/server/meta.ts\n");

/***/ }),

/***/ "(api)/./libs/server/middlewares/auth.ts":
/*!*****************************************!*\
  !*** ./libs/server/middlewares/auth.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"applyAuth\": () => (/* binding */ applyAuth),\n/* harmony export */   \"applyRedirectLogin\": () => (/* binding */ applyRedirectLogin),\n/* harmony export */   \"isLoggedIn\": () => (/* binding */ isLoggedIn),\n/* harmony export */   \"useAuth\": () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var libs_shared_page__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! libs/shared/page */ \"(api)/./libs/shared/page.ts\");\n/* harmony import */ var libs_server_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! libs/server/config */ \"(api)/./libs/server/config.ts\");\n\n\nasync function useAuth(req, res, next) {\n    if (false) {}\n    if (!isLoggedIn(req)) {\n        return res.APIError.NEED_LOGIN.throw();\n    }\n    return next();\n}\nfunction isLoggedIn(req) {\n    const cfg = (0,libs_server_config__WEBPACK_IMPORTED_MODULE_1__.config)();\n    if (cfg.auth.type === \"none\") {\n        return true;\n    }\n    return !!req.session.get(\"user\")?.isLoggedIn;\n}\nconst applyAuth = async (req, _res, next)=>{\n    req.props = {\n        ...req.props,\n        isLoggedIn: isLoggedIn(req),\n        disablePassword: (0,libs_server_config__WEBPACK_IMPORTED_MODULE_1__.config)().auth.type === \"none\",\n        IS_DEMO: false\n    };\n    next();\n};\nconst applyRedirectLogin = (resolvedUrl)=>async (req, _res, next)=>{\n        const redirect = {\n            destination: `/login?redirect=${resolvedUrl}`,\n            permanent: false\n        };\n        if (req.props.pageMode) {\n            if (req.props.pageMode !== libs_shared_page__WEBPACK_IMPORTED_MODULE_0__.PageMode.PUBLIC && !req.props.isLoggedIn) {\n                req.redirect = redirect;\n            }\n        } else if (!req.props.isLoggedIn) {\n            req.redirect = redirect;\n        }\n        next();\n    };\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./libs/server/middlewares/auth.ts\n");

/***/ }),

/***/ "(api)/./libs/server/middlewares/csrf.ts":
/*!*****************************************!*\
  !*** ./libs/server/middlewares/csrf.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"applyCsrf\": () => (/* binding */ applyCsrf),\n/* harmony export */   \"getCsrfToken\": () => (/* binding */ getCsrfToken),\n/* harmony export */   \"useCsrf\": () => (/* binding */ useCsrf),\n/* harmony export */   \"verifyCsrfToken\": () => (/* binding */ verifyCsrfToken)\n/* harmony export */ });\n/* harmony import */ var csrf__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! csrf */ \"csrf\");\n/* harmony import */ var csrf__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(csrf__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var libs_shared_const__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! libs/shared/const */ \"(api)/./libs/shared/const.ts\");\n/* harmony import */ var md5__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! md5 */ \"md5\");\n/* harmony import */ var md5__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(md5__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var libs_server_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! libs/server/config */ \"(api)/./libs/server/config.ts\");\n\n\n\n\nconst tokens = new (csrf__WEBPACK_IMPORTED_MODULE_0___default())();\n// generate CSRF secret\nlet _csrfSecret;\nconst csrfSecret = ()=>_csrfSecret ?? (_csrfSecret = md5__WEBPACK_IMPORTED_MODULE_2___default()(\"CSRF\" + (0,libs_server_config__WEBPACK_IMPORTED_MODULE_3__.config)().auth.password));\nconst getCsrfToken = ()=>tokens.create(csrfSecret());\nconst verifyCsrfToken = (token)=>tokens.verify(csrfSecret(), token);\nconst applyCsrf = async (req, _res, next)=>{\n    req.props = {\n        ...req.props,\n        csrfToken: getCsrfToken()\n    };\n    req.session.set(libs_shared_const__WEBPACK_IMPORTED_MODULE_1__.CSRF_HEADER_KEY, req.props.csrfToken);\n    await req.session.save();\n    next();\n};\nconst ignoredMethods = [\n    \"GET\",\n    \"HEAD\",\n    \"OPTIONS\"\n];\nfunction useCsrf(req, res, next) {\n    if (false) {}\n    const token = req.headers[libs_shared_const__WEBPACK_IMPORTED_MODULE_1__.CSRF_HEADER_KEY];\n    const sessionToken = req.session.get(libs_shared_const__WEBPACK_IMPORTED_MODULE_1__.CSRF_HEADER_KEY);\n    if (ignoredMethods.includes(req.method?.toLocaleUpperCase())) {\n        return next();\n    }\n    if (token && sessionToken && // TODO: sometimes not equal\n    // token === sessionToken &&\n    verifyCsrfToken(token) && verifyCsrfToken(sessionToken)) {\n        next();\n    } else {\n        let message;\n        if (!token) {\n            message = \"Missing CSRF token in headers\";\n        } else if (!sessionToken) {\n            message = \"Missing CSRF token in cookies\";\n        }\n        return res.APIError.INVALID_CSRF_TOKEN.throw(message);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./libs/server/middlewares/csrf.ts\n");

/***/ }),

/***/ "(api)/./libs/server/middlewares/error.ts":
/*!******************************************!*\
  !*** ./libs/server/middlewares/error.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"API\": () => (/* binding */ API),\n/* harmony export */   \"APIError\": () => (/* binding */ APIError),\n/* harmony export */   \"API_ERROR\": () => (/* binding */ API_ERROR),\n/* harmony export */   \"onError\": () => (/* binding */ onError),\n/* harmony export */   \"onErrorWithNext\": () => (/* binding */ onErrorWithNext),\n/* harmony export */   \"useError\": () => (/* binding */ useError)\n/* harmony export */ });\n/* harmony import */ var libs_shared_const__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! libs/shared/const */ \"(api)/./libs/shared/const.ts\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst API_ERROR = {\n    NEED_LOGIN: {\n        status: 401,\n        message: \"Please login first\"\n    },\n    NOT_SUPPORTED: {\n        status: 406,\n        message: \"Not supported\"\n    },\n    NOT_FOUND: {\n        status: 404,\n        message: \"Not found\"\n    },\n    INVALID_CSRF_TOKEN: {\n        status: 401,\n        message: \"Invalid CSRF token\"\n    },\n    IMPORT_FILE_LIMIT_SIZE: {\n        status: 401,\n        message: `File size limit exceeded ${libs_shared_const__WEBPACK_IMPORTED_MODULE_0__.IMPORT_FILE_LIMIT_SIZE}`\n    }\n};\nclass APIError {\n    prefix = \"API_ERR_\";\n    status = 500;\n    name = \"UNKNOWN\";\n    message = \"Something unexpected happened\";\n    constructor(message, properties){\n        this.message = message;\n        if (properties) {\n            this.status = properties.status;\n            this.name = properties.name;\n        }\n    }\n    throw(message) {\n        const error = new Error(message === undefined ? this.message : message);\n        error.name = this.prefix + this.name;\n        error.status = this.status;\n        throw error;\n    }\n}\nconst API = (0,lodash__WEBPACK_IMPORTED_MODULE_1__.mapValues)(API_ERROR, (v, name)=>new APIError(v.message, {\n        status: v.status,\n        name\n    }));\nasync function onError(err, _req, res) {\n    const e = {\n        name: err.name || \"UNKNOWN_ERR\",\n        message: err.message || \"Something unexpected\",\n        status: err.status || 500\n    };\n    console.error({\n        ...e,\n        stack: err.stack\n    });\n    res.status?.(e.status).json?.(e);\n}\nasync function onErrorWithNext(err, _req, res, next) {\n    const e = {\n        name: err.name || \"UNKNOWN_ERR\",\n        message: err.message || \"Something unexpected\",\n        status: err.status || 500\n    };\n    console.error({\n        ...e,\n        stack: err.stack\n    });\n    res.status?.(e.status).json?.(e);\n    next?.();\n}\nasync function useError(_req, res, next) {\n    res.APIError = API;\n    next();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./libs/server/middlewares/error.ts\n");

/***/ }),

/***/ "(api)/./libs/server/middlewares/rate-limit.ts":
/*!***********************************************!*\
  !*** ./libs/server/middlewares/rate-limit.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"authRateLimit\": () => (/* binding */ authRateLimit),\n/* harmony export */   \"createCustomRateLimit\": () => (/* binding */ createCustomRateLimit),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   \"generalRateLimit\": () => (/* binding */ generalRateLimit),\n/* harmony export */   \"notesRateLimit\": () => (/* binding */ notesRateLimit),\n/* harmony export */   \"readRateLimit\": () => (/* binding */ readRateLimit),\n/* harmony export */   \"smartRateLimit\": () => (/* binding */ smartRateLimit)\n/* harmony export */ });\n/* harmony import */ var express_rate_limit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! express-rate-limit */ \"express-rate-limit\");\n/* harmony import */ var express_rate_limit__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(express_rate_limit__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _debugging__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../debugging */ \"(api)/./libs/server/debugging.ts\");\n/**\n * API 限流中间件\n * 根据部署环境智能调整限流策略\n * 保护 Vercel 和 Docker 部署免受恶意攻击\n */ \n\nconst logger = (0,_debugging__WEBPACK_IMPORTED_MODULE_1__.createLogger)(\"rate-limit\");\n// 🎯 环境检测\nconst isServerless = !!(process.env.VERCEL || process.env.AWS_LAMBDA_FUNCTION_NAME || process.env.NETLIFY);\nconst isDocker = !!(process.env.DOCKER || process.env.HOSTNAME === \"0.0.0.0\");\n/**\n * 根据部署环境获取限流配置\n */ function getRateLimitConfig(type) {\n    const baseConfig = {\n        // 使用内存存储，适合单实例部署\n        // 如果需要多实例，可以考虑 Redis 存储\n        standardHeaders: true,\n        legacyHeaders: false,\n        // 自定义错误响应\n        handler: (req, res)=>{\n            logger.warn(\"Rate limit exceeded\", {\n                ip: req.ip,\n                userAgent: req.headers[\"user-agent\"],\n                path: req.url,\n                method: req.method\n            });\n            res.status(429).json({\n                name: \"RATE_LIMIT_EXCEEDED\",\n                message: \"Too many requests, please try again later.\",\n                status: 429\n            });\n        },\n        // 跳过成功的请求计数（可选）\n        skipSuccessfulRequests: false,\n        // 跳过失败的请求计数（可选）\n        skipFailedRequests: false\n    };\n    // 🔧 根据环境和操作类型调整限制\n    switch(type){\n        case \"auth\":\n            // 认证相关：最严格限制\n            return {\n                ...baseConfig,\n                windowMs: 15 * 60 * 1000,\n                max: isServerless ? 5 : isDocker ? 10 : 8,\n                message: \"Too many authentication attempts, please try again in 15 minutes.\"\n            };\n        case \"notes\":\n            // 笔记操作：中等限制\n            return {\n                ...baseConfig,\n                windowMs: 1 * 60 * 1000,\n                max: isServerless ? 20 : isDocker ? 50 : 30,\n                message: \"Too many note operations, please slow down.\"\n            };\n        case \"read\":\n            // 读取操作：宽松限制\n            return {\n                ...baseConfig,\n                windowMs: 1 * 60 * 1000,\n                max: isServerless ? 100 : isDocker ? 200 : 150,\n                message: \"Too many read requests, please slow down.\"\n            };\n        case \"general\":\n        default:\n            // 通用限制：平衡配置\n            return {\n                ...baseConfig,\n                windowMs: 1 * 60 * 1000,\n                max: isServerless ? 60 : isDocker ? 120 : 90,\n                message: \"Too many requests, please slow down.\"\n            };\n    }\n}\n// 🛡️ 创建不同类型的限流器\nconst authRateLimit = express_rate_limit__WEBPACK_IMPORTED_MODULE_0___default()(getRateLimitConfig(\"auth\"));\nconst notesRateLimit = express_rate_limit__WEBPACK_IMPORTED_MODULE_0___default()(getRateLimitConfig(\"notes\"));\nconst readRateLimit = express_rate_limit__WEBPACK_IMPORTED_MODULE_0___default()(getRateLimitConfig(\"read\"));\nconst generalRateLimit = express_rate_limit__WEBPACK_IMPORTED_MODULE_0___default()(getRateLimitConfig(\"general\"));\n/**\n * 智能限流中间件\n * 根据请求路径自动选择合适的限流策略\n */ function smartRateLimit(req, res, next) {\n    const path = req.url || \"\";\n    const method = req.method || \"GET\";\n    // 🔍 根据路径和方法选择限流策略\n    if (path.includes(\"/api/auth\") || path.includes(\"/api/login\")) {\n        // 认证相关\n        return authRateLimit(req, res, next);\n    } else if (method === \"POST\" || method === \"PUT\" || method === \"DELETE\") {\n        // 写操作\n        return notesRateLimit(req, res, next);\n    } else if (method === \"GET\") {\n        // 读操作\n        return readRateLimit(req, res, next);\n    } else {\n        // 其他操作\n        return generalRateLimit(req, res, next);\n    }\n}\n/**\n * 创建自定义限流器\n */ function createCustomRateLimit(options) {\n    return express_rate_limit__WEBPACK_IMPORTED_MODULE_0___default()({\n        ...getRateLimitConfig(\"general\"),\n        ...options\n    });\n}\n// 📊 记录限流配置\nlogger.info(\"Rate limiting configured\", {\n    environment: isServerless ? \"serverless\" : isDocker ? \"docker\" : \"traditional\",\n    authLimit: getRateLimitConfig(\"auth\").max,\n    notesLimit: getRateLimitConfig(\"notes\").max,\n    readLimit: getRateLimitConfig(\"read\").max,\n    generalLimit: getRateLimitConfig(\"general\").max\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (smartRateLimit);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./libs/server/middlewares/rate-limit.ts\n");

/***/ }),

/***/ "(api)/./libs/server/middlewares/session.ts":
/*!********************************************!*\
  !*** ./libs/server/middlewares/session.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"useSession\": () => (/* binding */ useSession)\n/* harmony export */ });\n/* harmony import */ var next_iron_session__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-iron-session */ \"next-iron-session\");\n/* harmony import */ var next_iron_session__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_iron_session__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var md5__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! md5 */ \"md5\");\n/* harmony import */ var md5__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(md5__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var libs_server_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! libs/server/config */ \"(api)/./libs/server/config.ts\");\n\n\n\nconst sessionOptions = ()=>({\n        cookieName: \"notea-auth\",\n        password: md5__WEBPACK_IMPORTED_MODULE_1___default()(\"notea\" + (0,libs_server_config__WEBPACK_IMPORTED_MODULE_2__.config)().auth.password),\n        cookieOptions: {\n            secure: (0,libs_server_config__WEBPACK_IMPORTED_MODULE_2__.config)().server.useSecureCookies\n        }\n    });\nlet _useSession;\nconst useSession = (...args)=>{\n    _useSession ??= (0,next_iron_session__WEBPACK_IMPORTED_MODULE_0__.ironSession)(sessionOptions());\n    _useSession(...args);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9saWJzL3NlcnZlci9taWRkbGV3YXJlcy9zZXNzaW9uLnRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUFnRDtBQUMxQjtBQUM4QztBQUdwRSxNQUFNRyxjQUFjLEdBQUcsSUFBTztRQUMxQkMsVUFBVSxFQUFFLFlBQVk7UUFDeEJDLFFBQVEsRUFBRUosMENBQUcsQ0FBQyxPQUFPLEdBQUcsMERBQU8sRUFBRSxDQUFDSyxJQUFJLENBQTRCRCxRQUFRLENBQUM7UUFDM0VFLGFBQWEsRUFBRTtZQUNYQyxNQUFNLEVBQUVOLDBEQUFNLEVBQUUsQ0FBQ08sTUFBTSxDQUFDQyxnQkFBZ0I7U0FDM0M7S0FDSjtBQUVELElBQUlDLFdBQVc7QUFDUixNQUFNQyxVQUFVLEdBQXlCLENBQUMsR0FBR0MsSUFBSSxHQUE4QjtJQUNsRkYsV0FBVyxLQUFLWCw4REFBVyxDQUFDRyxjQUFjLEVBQUUsQ0FBQyxDQUFDO0lBQzlDUSxXQUFXLElBQUlFLElBQUksQ0FBQyxDQUFDO0FBQ3pCLENBQUMsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL25vdGVhLy4vbGlicy9zZXJ2ZXIvbWlkZGxld2FyZXMvc2Vzc2lvbi50cz9hNGEwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGlyb25TZXNzaW9uIH0gZnJvbSAnbmV4dC1pcm9uLXNlc3Npb24nO1xuaW1wb3J0IG1kNSBmcm9tICdtZDUnO1xuaW1wb3J0IHsgQmFzaWNBdXRoQ29uZmlndXJhdGlvbiwgY29uZmlnIH0gZnJvbSAnbGlicy9zZXJ2ZXIvY29uZmlnJztcbmltcG9ydCB7IE1pZGRsZXdhcmUsIE5leHRIYW5kbGVyIH0gZnJvbSAnbmV4dC1jb25uZWN0JztcblxuY29uc3Qgc2Vzc2lvbk9wdGlvbnMgPSAoKSA9PiAoe1xuICAgIGNvb2tpZU5hbWU6ICdub3RlYS1hdXRoJyxcbiAgICBwYXNzd29yZDogbWQ1KCdub3RlYScgKyAoY29uZmlnKCkuYXV0aCBhcyBCYXNpY0F1dGhDb25maWd1cmF0aW9uKS5wYXNzd29yZCksIFxuICAgIGNvb2tpZU9wdGlvbnM6IHtcbiAgICAgICAgc2VjdXJlOiBjb25maWcoKS5zZXJ2ZXIudXNlU2VjdXJlQ29va2llcyxcbiAgICB9LFxufSk7XG5cbmxldCBfdXNlU2Vzc2lvbjogTWlkZGxld2FyZTxhbnksIGFueT47XG5leHBvcnQgY29uc3QgdXNlU2Vzc2lvbjogTWlkZGxld2FyZTxhbnksIGFueT4gPSAoLi4uYXJnczogW2FueSwgYW55LCBOZXh0SGFuZGxlcl0pID0+IHtcbiAgICBfdXNlU2Vzc2lvbiA/Pz0gaXJvblNlc3Npb24oc2Vzc2lvbk9wdGlvbnMoKSk7XG4gICAgX3VzZVNlc3Npb24oLi4uYXJncyk7XG59O1xuIl0sIm5hbWVzIjpbImlyb25TZXNzaW9uIiwibWQ1IiwiY29uZmlnIiwic2Vzc2lvbk9wdGlvbnMiLCJjb29raWVOYW1lIiwicGFzc3dvcmQiLCJhdXRoIiwiY29va2llT3B0aW9ucyIsInNlY3VyZSIsInNlcnZlciIsInVzZVNlY3VyZUNvb2tpZXMiLCJfdXNlU2Vzc2lvbiIsInVzZVNlc3Npb24iLCJhcmdzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(api)/./libs/server/middlewares/session.ts\n");

/***/ }),

/***/ "(api)/./libs/server/middlewares/store.ts":
/*!******************************************!*\
  !*** ./libs/server/middlewares/store.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"applyStore\": () => (/* binding */ applyStore),\n/* harmony export */   \"useStore\": () => (/* binding */ useStore)\n/* harmony export */ });\n/* harmony import */ var libs_server_store__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! libs/server/store */ \"(api)/./libs/server/store/index.ts\");\n/* harmony import */ var libs_server_store_tree_factory__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! libs/server/store/tree-factory */ \"(api)/./libs/server/store/tree-factory.ts\");\n\n\nconst useStore = async (req, _res, next)=>{\n    applyStore(req);\n    return next();\n};\nfunction applyStore(req) {\n    const store = (0,libs_server_store__WEBPACK_IMPORTED_MODULE_0__.createStore)();\n    const treeStore = (0,libs_server_store_tree_factory__WEBPACK_IMPORTED_MODULE_1__.createTreeStore)(store);\n    req.state = {\n        ...req.state,\n        store,\n        treeStore\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9saWJzL3NlcnZlci9taWRkbGV3YXJlcy9zdG9yZS50cy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWdEO0FBQ2lCO0FBRzFELE1BQU1FLFFBQVEsR0FBa0IsT0FBT0MsR0FBRyxFQUFFQyxJQUFJLEVBQUVDLElBQUksR0FBSztJQUM5REMsVUFBVSxDQUFDSCxHQUFHLENBQUMsQ0FBQztJQUVoQixPQUFPRSxJQUFJLEVBQUUsQ0FBQztBQUNsQixDQUFDLENBQUM7QUFFSyxTQUFTQyxVQUFVLENBQUNILEdBQWUsRUFBRTtJQUN4QyxNQUFNSSxLQUFLLEdBQUdQLDhEQUFXLEVBQUU7SUFDM0IsTUFBTVEsU0FBUyxHQUFHUCwrRUFBZSxDQUFDTSxLQUFLLENBQUM7SUFFeENKLEdBQUcsQ0FBQ00sS0FBSyxHQUFHO1FBQ1IsR0FBR04sR0FBRyxDQUFDTSxLQUFLO1FBQ1pGLEtBQUs7UUFDTEMsU0FBUztLQUNaLENBQUM7QUFDTixDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbm90ZWEvLi9saWJzL3NlcnZlci9taWRkbGV3YXJlcy9zdG9yZS50cz8xYjNkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZVN0b3JlIH0gZnJvbSAnbGlicy9zZXJ2ZXIvc3RvcmUnO1xuaW1wb3J0IHsgY3JlYXRlVHJlZVN0b3JlIH0gZnJvbSAnbGlicy9zZXJ2ZXIvc3RvcmUvdHJlZS1mYWN0b3J5JztcbmltcG9ydCB7IEFwaVJlcXVlc3QsIFNTUk1pZGRsZXdhcmUgfSBmcm9tICcuLi9jb25uZWN0JztcblxuZXhwb3J0IGNvbnN0IHVzZVN0b3JlOiBTU1JNaWRkbGV3YXJlID0gYXN5bmMgKHJlcSwgX3JlcywgbmV4dCkgPT4ge1xuICAgIGFwcGx5U3RvcmUocmVxKTtcblxuICAgIHJldHVybiBuZXh0KCk7XG59O1xuXG5leHBvcnQgZnVuY3Rpb24gYXBwbHlTdG9yZShyZXE6IEFwaVJlcXVlc3QpIHtcbiAgICBjb25zdCBzdG9yZSA9IGNyZWF0ZVN0b3JlKCk7XG4gICAgY29uc3QgdHJlZVN0b3JlID0gY3JlYXRlVHJlZVN0b3JlKHN0b3JlKTtcblxuICAgIHJlcS5zdGF0ZSA9IHtcbiAgICAgICAgLi4ucmVxLnN0YXRlLFxuICAgICAgICBzdG9yZSxcbiAgICAgICAgdHJlZVN0b3JlLFxuICAgIH07XG59XG4iXSwibmFtZXMiOlsiY3JlYXRlU3RvcmUiLCJjcmVhdGVUcmVlU3RvcmUiLCJ1c2VTdG9yZSIsInJlcSIsIl9yZXMiLCJuZXh0IiwiYXBwbHlTdG9yZSIsInN0b3JlIiwidHJlZVN0b3JlIiwic3RhdGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api)/./libs/server/middlewares/store.ts\n");

/***/ }),

/***/ "(api)/./libs/server/note-path.ts":
/*!**********************************!*\
  !*** ./libs/server/note-path.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"getPathFileByName\": () => (/* binding */ getPathFileByName),\n/* harmony export */   \"getPathNoteById\": () => (/* binding */ getPathNoteById),\n/* harmony export */   \"getPathSettings\": () => (/* binding */ getPathSettings),\n/* harmony export */   \"getPathTrash\": () => (/* binding */ getPathTrash),\n/* harmony export */   \"getPathTree\": () => (/* binding */ getPathTree)\n/* harmony export */ });\nfunction getPathTree() {\n    return `tree`;\n}\nfunction getPathTrash() {\n    return `trash`;\n}\nfunction getPathSettings() {\n    return `settings`;\n}\nfunction getPathNoteById(id) {\n    return `notes/${id}`;\n}\nfunction getPathFileByName(name) {\n    return `files/${name}`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9saWJzL3NlcnZlci9ub3RlLXBhdGgudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBTyxTQUFTQSxXQUFXLEdBQUc7SUFDMUIsT0FBTyxDQUFDLElBQUksQ0FBQyxDQUFDO0FBQ2xCLENBQUM7QUFFTSxTQUFTQyxZQUFZLEdBQUc7SUFDM0IsT0FBTyxDQUFDLEtBQUssQ0FBQyxDQUFDO0FBQ25CLENBQUM7QUFFTSxTQUFTQyxlQUFlLEdBQUc7SUFDOUIsT0FBTyxDQUFDLFFBQVEsQ0FBQyxDQUFDO0FBQ3RCLENBQUM7QUFFTSxTQUFTQyxlQUFlLENBQUNDLEVBQVUsRUFBRTtJQUN4QyxPQUFPLENBQUMsTUFBTSxFQUFFQSxFQUFFLENBQUMsQ0FBQyxDQUFDO0FBQ3pCLENBQUM7QUFFTSxTQUFTQyxpQkFBaUIsQ0FBQ0MsSUFBWSxFQUFFO0lBQzVDLE9BQU8sQ0FBQyxNQUFNLEVBQUVBLElBQUksQ0FBQyxDQUFDLENBQUM7QUFDM0IsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL25vdGVhLy4vbGlicy9zZXJ2ZXIvbm90ZS1wYXRoLnRzP2E0ZWIiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIGdldFBhdGhUcmVlKCkge1xuICAgIHJldHVybiBgdHJlZWA7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBnZXRQYXRoVHJhc2goKSB7XG4gICAgcmV0dXJuIGB0cmFzaGA7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBnZXRQYXRoU2V0dGluZ3MoKSB7XG4gICAgcmV0dXJuIGBzZXR0aW5nc2A7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBnZXRQYXRoTm90ZUJ5SWQoaWQ6IHN0cmluZykge1xuICAgIHJldHVybiBgbm90ZXMvJHtpZH1gO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gZ2V0UGF0aEZpbGVCeU5hbWUobmFtZTogc3RyaW5nKSB7XG4gICAgcmV0dXJuIGBmaWxlcy8ke25hbWV9YDtcbn1cbiJdLCJuYW1lcyI6WyJnZXRQYXRoVHJlZSIsImdldFBhdGhUcmFzaCIsImdldFBhdGhTZXR0aW5ncyIsImdldFBhdGhOb3RlQnlJZCIsImlkIiwiZ2V0UGF0aEZpbGVCeU5hbWUiLCJuYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(api)/./libs/server/note-path.ts\n");

/***/ }),

/***/ "(api)/./libs/server/note.ts":
/*!*****************************!*\
  !*** ./libs/server/note.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"createNote\": () => (/* binding */ createNote)\n/* harmony export */ });\n/* harmony import */ var libs_shared_id__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! libs/shared/id */ \"(api)/./libs/shared/id.ts\");\n/* harmony import */ var libs_server_meta__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! libs/server/meta */ \"(api)/./libs/server/meta.ts\");\n/* harmony import */ var libs_server_note_path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! libs/server/note-path */ \"(api)/./libs/server/note-path.ts\");\n\n\n\nconst createNote = async (note, state)=>{\n    const { content =\"\\n\" , ...meta } = note;\n    let noteId = note.id;\n    if (!noteId) {\n        noteId = (0,libs_shared_id__WEBPACK_IMPORTED_MODULE_0__.genId)();\n    }\n    while(await state.store.hasObject((0,libs_server_note_path__WEBPACK_IMPORTED_MODULE_2__.getPathNoteById)(noteId))){\n        noteId = (0,libs_shared_id__WEBPACK_IMPORTED_MODULE_0__.genId)();\n    }\n    const currentTime = new Date().toISOString();\n    const metaWithModel = {\n        ...meta,\n        id: noteId,\n        date: note.date ?? currentTime,\n        updated_at: currentTime\n    };\n    const metaData = (0,libs_server_meta__WEBPACK_IMPORTED_MODULE_1__.jsonToMeta)(metaWithModel);\n    await state.store.putObject((0,libs_server_note_path__WEBPACK_IMPORTED_MODULE_2__.getPathNoteById)(noteId), content, {\n        contentType: \"text/markdown\",\n        meta: metaData\n    });\n    const completeNote = {\n        ...metaWithModel,\n        content\n    };\n    return completeNote;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./libs/server/note.ts\n");

/***/ }),

/***/ "(api)/./libs/server/store/index.ts":
/*!************************************!*\
  !*** ./libs/server/store/index.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"StoreProvider\": () => (/* reexport safe */ _providers_base__WEBPACK_IMPORTED_MODULE_2__.StoreProvider),\n/* harmony export */   \"createStore\": () => (/* binding */ createStore)\n/* harmony export */ });\n/* harmony import */ var _providers_postgresql__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./providers/postgresql */ \"(api)/./libs/server/store/providers/postgresql.ts\");\n/* harmony import */ var libs_server_config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! libs/server/config */ \"(api)/./libs/server/config.ts\");\n/* harmony import */ var _providers_base__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers/base */ \"(api)/./libs/server/store/providers/base.ts\");\n/**\n * Store Factory\n * Based on the open-source project Notea, originally created by qingwei-li<<EMAIL>>.\n * Modified and maintained by waycaan, 2025.\n *\n * Key modifications:\n * - Replaced S3 store creation with PostgreSQL store\n * - Simplified store factory to only support PostgreSQL\n */ \n\nfunction createStore() {\n    const cfg = (0,libs_server_config__WEBPACK_IMPORTED_MODULE_1__.config)().store;\n    return new _providers_postgresql__WEBPACK_IMPORTED_MODULE_0__.StorePostgreSQL({\n        connectionString: cfg.connectionString,\n        prefix: cfg.prefix\n    });\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(api)/./libs/server/store/index.ts\n");

/***/ }),

/***/ "(api)/./libs/server/store/providers/base.ts":
/*!*********************************************!*\
  !*** ./libs/server/store/providers/base.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"StoreProvider\": () => (/* binding */ StoreProvider)\n/* harmony export */ });\nclass StoreProvider {\n    constructor({ prefix  }){\n        this.prefix = prefix?.replace(/\\/$/, \"\");\n        if (this.prefix) {\n            this.prefix += \"/\";\n        }\n    }\n    getPath(...paths) {\n        return this.prefix + paths.join(\"/\");\n    }\n    /**\n     * 🚀 批量获取对象元数据 - 性能优化\n     * 默认实现：降级到单个查询，子类可以重写以提供更高效的实现\n     */ async batchGetObjectMeta(paths) {\n        return Promise.all(paths.map((path)=>this.getObjectMeta(path)));\n    }\n    /**\n     * 🚀 批量获取对象内容和元数据 - 性能优化\n     * 默认实现：降级到单个查询，子类可以重写以提供更高效的实现\n     */ async batchGetObjectAndMeta(paths) {\n        return Promise.all(paths.map((path)=>this.getObjectAndMeta(path)));\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./libs/server/store/providers/base.ts\n");

/***/ }),

/***/ "(api)/./libs/server/store/providers/postgresql.ts":
/*!***************************************************!*\
  !*** ./libs/server/store/providers/postgresql.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"StorePostgreSQL\": () => (/* binding */ StorePostgreSQL)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(pg__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _base__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./base */ \"(api)/./libs/server/store/providers/base.ts\");\n/* harmony import */ var libs_server_debugging__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! libs/server/debugging */ \"(api)/./libs/server/debugging.ts\");\n/**\n * PostgreSQL Store Provider\n *\n * Copyright (c) 2025 waycaan\n * Licensed under the MIT License\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n */ \n\n\nclass StorePostgreSQL extends _base__WEBPACK_IMPORTED_MODULE_1__.StoreProvider {\n    logger = (0,libs_server_debugging__WEBPACK_IMPORTED_MODULE_2__.createLogger)(\"store.postgresql\");\n    tablesInitialized = false;\n    constructor(config){\n        super(config);\n        // 🎯 智能环境检测\n        const isServerless = !!(process.env.VERCEL || process.env.AWS_LAMBDA_FUNCTION_NAME || process.env.NETLIFY);\n        const isDocker = !!(process.env.DOCKER || process.env.HOSTNAME === \"0.0.0.0\");\n        const isProduction = \"development\" === \"production\";\n        // 🔧 根据部署环境智能配置连接池\n        const poolConfig = this.getOptimalPoolConfig(isServerless, isDocker, isProduction);\n        this.pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool({\n            connectionString: config.connectionString,\n            ssl: isProduction && !isDocker ? {\n                rejectUnauthorized: false\n            } : false,\n            ...poolConfig\n        });\n        // 📊 记录配置信息\n        this.logger.info(\"PostgreSQL pool configured:\", {\n            environment: isServerless ? \"serverless\" : isDocker ? \"docker\" : \"traditional\",\n            maxConnections: poolConfig.max,\n            minConnections: poolConfig.min || 0,\n            idleTimeout: poolConfig.idleTimeoutMillis\n        });\n    }\n    /**\n     * 根据部署环境获取最优连接池配置\n     */ getOptimalPoolConfig(isServerless, isDocker, isProduction) {\n        if (isServerless) {\n            // Vercel/Serverless 环境：保守配置，避免超出 Neon 连接限制\n            return {\n                max: 2,\n                min: 0,\n                idleTimeoutMillis: 10000,\n                connectionTimeoutMillis: 5000,\n                statement_timeout: 8000\n            };\n        } else if (isDocker) {\n            // Docker 环境：激进配置，充分利用内建数据库性能\n            return {\n                max: isProduction ? 10 : 6,\n                min: 2,\n                idleTimeoutMillis: 60000,\n                connectionTimeoutMillis: 5000,\n                statement_timeout: 15000\n            };\n        } else {\n            // 传统部署：平衡配置\n            return {\n                max: isProduction ? 6 : 4,\n                min: 1,\n                idleTimeoutMillis: 30000,\n                connectionTimeoutMillis: 5000,\n                statement_timeout: 10000\n            };\n        }\n    }\n    async ensureTablesInitialized() {\n        if (this.tablesInitialized) {\n            return;\n        }\n        const client = await this.pool.connect();\n        try {\n            // Create notes table\n            await client.query(`\n                CREATE TABLE IF NOT EXISTS notes (\n                    id VARCHAR(255) PRIMARY KEY,\n                    path VARCHAR(500) UNIQUE NOT NULL,\n                    content TEXT,\n                    content_type VARCHAR(100) DEFAULT 'text/markdown',\n                    metadata JSONB DEFAULT '{}',\n                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n                );\n            `);\n            // Create tree table for storing tree structure\n            await client.query(`\n                CREATE TABLE IF NOT EXISTS tree_data (\n                    id VARCHAR(255) PRIMARY KEY DEFAULT 'main',\n                    data JSONB NOT NULL,\n                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\n                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\n                );\n            `);\n            // Create performance indexes\n            await this.createPerformanceIndexes(client);\n            this.tablesInitialized = true;\n            this.logger.info(\"Database tables initialized successfully\");\n        } catch (error) {\n            this.logger.error(\"Failed to initialize database tables:\", error);\n            throw error;\n        } finally{\n            client.release();\n        }\n    }\n    async createPerformanceIndexes(client) {\n        const indexes = [\n            \"CREATE INDEX IF NOT EXISTS idx_notes_path ON notes(path)\",\n            \"CREATE INDEX IF NOT EXISTS idx_notes_updated_at ON notes(updated_at DESC)\",\n            \"CREATE INDEX IF NOT EXISTS idx_notes_id ON notes(id)\",\n            \"CREATE INDEX IF NOT EXISTS idx_notes_metadata_gin ON notes USING GIN(metadata)\",\n            \"CREATE INDEX IF NOT EXISTS idx_notes_metadata_pid ON notes((metadata->>'pid'))\",\n            \"CREATE INDEX IF NOT EXISTS idx_notes_metadata_title ON notes((metadata->>'title'))\",\n            \"CREATE INDEX IF NOT EXISTS idx_notes_daily ON notes((metadata->>'isDailyNote')) WHERE metadata->>'isDailyNote' = 'true'\",\n            \"CREATE INDEX IF NOT EXISTS idx_notes_content_search ON notes USING GIN(to_tsvector('english', COALESCE(content, '')))\",\n            \"CREATE INDEX IF NOT EXISTS idx_tree_data_updated_at ON tree_data(updated_at DESC)\", \n        ];\n        for (const indexQuery of indexes){\n            try {\n                await client.query(indexQuery);\n                const indexName = indexQuery.match(/idx_\\w+/)?.[0] || \"unknown\";\n                this.logger.debug(\"Created/verified index:\", indexName);\n            } catch (error) {\n                this.logger.warn(\"Index creation warning:\", error instanceof Error ? error.message : String(error));\n            }\n        }\n    }\n    async getSignUrl(_path, _expires = 600) {\n        return null;\n    }\n    async hasObject(path) {\n        await this.ensureTablesInitialized();\n        const client = await this.pool.connect();\n        try {\n            const result = await client.query(\"SELECT 1 FROM notes WHERE path = $1\", [\n                this.getPath(path)\n            ]);\n            return result.rows.length > 0;\n        } catch (error) {\n            this.logger.error(\"Error checking if object exists:\", error);\n            return false;\n        } finally{\n            client.release();\n        }\n    }\n    async getObject(path, _isCompressed = false) {\n        const client = await this.pool.connect();\n        try {\n            const result = await client.query(\"SELECT content FROM notes WHERE path = $1\", [\n                this.getPath(path)\n            ]);\n            if (result.rows.length === 0) {\n                return undefined;\n            }\n            return result.rows[0].content;\n        } catch (error) {\n            this.logger.error(\"Error getting object:\", error);\n            return undefined;\n        } finally{\n            client.release();\n        }\n    }\n    async getObjectMeta(path) {\n        const client = await this.pool.connect();\n        try {\n            const result = await client.query(\"SELECT metadata FROM notes WHERE path = $1\", [\n                this.getPath(path)\n            ]);\n            if (result.rows.length === 0) {\n                return undefined;\n            }\n            return result.rows[0].metadata || {};\n        } catch (error) {\n            this.logger.error(\"Error getting object metadata:\", error);\n            return undefined;\n        } finally{\n            client.release();\n        }\n    }\n    async getObjectAndMeta(path, _isCompressed = false) {\n        const client = await this.pool.connect();\n        try {\n            const result = await client.query(\"SELECT content, metadata, content_type, updated_at FROM notes WHERE path = $1\", [\n                this.getPath(path)\n            ]);\n            if (result.rows.length === 0) {\n                return {};\n            }\n            const row = result.rows[0];\n            return {\n                content: row.content,\n                meta: row.metadata || {},\n                contentType: row.content_type,\n                updated_at: row.updated_at ? row.updated_at.toISOString() : undefined\n            };\n        } catch (error) {\n            this.logger.error(\"Error getting object and metadata:\", error);\n            return {};\n        } finally{\n            client.release();\n        }\n    }\n    async putObject(path, raw, options, _isCompressed) {\n        await this.ensureTablesInitialized();\n        const client = await this.pool.connect();\n        try {\n            const content = Buffer.isBuffer(raw) ? raw.toString(\"utf-8\") : raw;\n            const fullPath = this.getPath(path);\n            const isNotePath = path.startsWith(\"notes/\");\n            if (isNotePath) {\n                const metadata = options?.meta || {};\n                const noteId = metadata.id;\n                if (!noteId) {\n                    throw new Error(\"Note ID is required in metadata for notes\");\n                }\n                const metadataWithoutId = {\n                    ...metadata\n                };\n                delete metadataWithoutId.id;\n                await client.query(`\n                    INSERT INTO notes (id, path, content, content_type, metadata, updated_at)\n                    VALUES ($1, $2, $3, $4, $5, NOW())\n                    ON CONFLICT (path)\n                    DO UPDATE SET\n                        content = EXCLUDED.content,\n                        content_type = EXCLUDED.content_type,\n                        metadata = EXCLUDED.metadata,\n                        updated_at = NOW()\n                `, [\n                    noteId,\n                    fullPath,\n                    content,\n                    options?.contentType || \"text/markdown\",\n                    JSON.stringify(metadataWithoutId)\n                ]);\n                this.logger.debug(\"Successfully put note:\", fullPath, \"with ID:\", noteId);\n            } else {\n                await client.query(`\n                    INSERT INTO notes (id, path, content, content_type, metadata, updated_at)\n                    VALUES ($1, $2, $3, $4, $5, NOW())\n                    ON CONFLICT (path)\n                    DO UPDATE SET\n                        content = EXCLUDED.content,\n                        content_type = EXCLUDED.content_type,\n                        metadata = EXCLUDED.metadata,\n                        updated_at = NOW()\n                `, [\n                    fullPath,\n                    fullPath,\n                    content,\n                    options?.contentType || \"text/markdown\",\n                    JSON.stringify(options?.meta || {})\n                ]);\n                this.logger.debug(\"Successfully put non-note object:\", fullPath);\n            }\n        } catch (error) {\n            this.logger.error(\"Error putting object:\", error);\n            throw error;\n        } finally{\n            client.release();\n        }\n    }\n    async deleteObject(path) {\n        const client = await this.pool.connect();\n        try {\n            await client.query(\"DELETE FROM notes WHERE path = $1\", [\n                this.getPath(path)\n            ]);\n            this.logger.debug(\"Successfully deleted object:\", this.getPath(path));\n        } catch (error) {\n            this.logger.error(\"Error deleting object:\", error);\n            throw error;\n        } finally{\n            client.release();\n        }\n    }\n    async copyObject(fromPath, toPath, options) {\n        const client = await this.pool.connect();\n        try {\n            const fullFromPath = this.getPath(fromPath);\n            const fullToPath = this.getPath(toPath);\n            const metadata = options.meta || {};\n            const noteId = metadata.id;\n            const metadataWithoutId = {\n                ...metadata\n            };\n            delete metadataWithoutId.id;\n            if (fullFromPath === fullToPath) {\n                await client.query(`\n                    UPDATE notes\n                    SET metadata = $2, content_type = $3, updated_at = NOW()\n                    WHERE path = $1\n                `, [\n                    fullFromPath,\n                    JSON.stringify(metadataWithoutId),\n                    options.contentType || \"text/markdown\"\n                ]);\n            } else {\n                // Copy to new path\n                if (!noteId) {\n                    throw new Error(\"Note ID is required in metadata for copy operation\");\n                }\n                await client.query(`\n                    INSERT INTO notes (id, path, content, content_type, metadata, updated_at)\n                    SELECT $3, $2, content, $4, $5, NOW()\n                    FROM notes WHERE path = $1\n                    ON CONFLICT (path)\n                    DO UPDATE SET\n                        content = EXCLUDED.content,\n                        content_type = EXCLUDED.content_type,\n                        metadata = EXCLUDED.metadata,\n                        updated_at = NOW()\n                `, [\n                    fullFromPath,\n                    fullToPath,\n                    noteId,\n                    options.contentType || \"text/markdown\",\n                    JSON.stringify(metadataWithoutId)\n                ]);\n            }\n            this.logger.debug(\"Successfully copied object from\", fullFromPath, \"to\", fullToPath);\n        } catch (error) {\n            this.logger.error(\"Error copying object:\", error);\n            throw error;\n        } finally{\n            client.release();\n        }\n    }\n    async getTree() {\n        const client = await this.pool.connect();\n        try {\n            const result = await client.query(\"SELECT data FROM tree_data WHERE id = $1\", [\n                \"main\"\n            ]);\n            if (result.rows.length === 0) {\n                return null;\n            }\n            return result.rows[0].data;\n        } catch (error) {\n            this.logger.error(\"Error getting tree:\", error);\n            return null;\n        } finally{\n            client.release();\n        }\n    }\n    async putTree(treeData) {\n        const client = await this.pool.connect();\n        try {\n            await client.query(`\n                INSERT INTO tree_data (id, data, updated_at)\n                VALUES ('main', $1, NOW())\n                ON CONFLICT (id)\n                DO UPDATE SET\n                    data = EXCLUDED.data,\n                    updated_at = NOW()\n            `, [\n                JSON.stringify(treeData)\n            ]);\n            this.logger.debug(\"Successfully updated tree data\");\n        } catch (error) {\n            this.logger.error(\"Error updating tree:\", error);\n            throw error;\n        } finally{\n            client.release();\n        }\n    }\n    /**\n     * 🚀 批量获取对象元数据 - 性能优化\n     * 解决 N+1 查询问题，将多次查询合并为一次\n     */ async batchGetObjectMeta(paths) {\n        if (paths.length === 0) {\n            return [];\n        }\n        const client = await this.pool.connect();\n        try {\n            // 🎯 使用 IN 查询批量获取元数据\n            const placeholders = paths.map((_, index)=>`$${index + 1}`).join(\", \");\n            const fullPaths = paths.map((path)=>this.getPath(path));\n            const result = await client.query(`SELECT path, metadata FROM notes WHERE path IN (${placeholders}) ORDER BY path`, fullPaths);\n            // 📊 创建路径到元数据的映射\n            const metaMap = new Map();\n            result.rows.forEach((row)=>{\n                metaMap.set(row.path, row.metadata || {});\n            });\n            // 🔄 按原始顺序返回结果，缺失的返回 undefined\n            return fullPaths.map((fullPath)=>metaMap.get(fullPath));\n        } catch (error) {\n            this.logger.error(\"Error batch getting object metadata:\", error);\n            // 🛡️ 降级到单个查询\n            return Promise.all(paths.map((path)=>this.getObjectMeta(path)));\n        } finally{\n            client.release();\n        }\n    }\n    /**\n     * 🚀 批量获取对象内容和元数据 - 性能优化\n     */ async batchGetObjectAndMeta(paths) {\n        if (paths.length === 0) {\n            return [];\n        }\n        const client = await this.pool.connect();\n        try {\n            const placeholders = paths.map((_, index)=>`$${index + 1}`).join(\", \");\n            const fullPaths = paths.map((path)=>this.getPath(path));\n            const result = await client.query(`SELECT path, content, metadata, content_type, updated_at\n                 FROM notes\n                 WHERE path IN (${placeholders})\n                 ORDER BY path`, fullPaths);\n            // 📊 创建路径到数据的映射\n            const dataMap = new Map();\n            result.rows.forEach((row)=>{\n                dataMap.set(row.path, {\n                    content: row.content,\n                    meta: row.metadata || {},\n                    contentType: row.content_type,\n                    updated_at: row.updated_at ? row.updated_at.toISOString() : undefined\n                });\n            });\n            // 🔄 按原始顺序返回结果，缺失的返回空对象\n            return fullPaths.map((fullPath)=>dataMap.get(fullPath) || {});\n        } catch (error) {\n            this.logger.error(\"Error batch getting objects and metadata:\", error);\n            // 🛡️ 降级到单个查询\n            return Promise.all(paths.map((path)=>this.getObjectAndMeta(path)));\n        } finally{\n            client.release();\n        }\n    }\n    async close() {\n        await this.pool.end();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./libs/server/store/providers/postgresql.ts\n");

/***/ }),

/***/ "(api)/./libs/server/store/tree-factory.ts":
/*!*******************************************!*\
  !*** ./libs/server/store/tree-factory.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"createTreeStore\": () => (/* binding */ createTreeStore)\n/* harmony export */ });\n/* harmony import */ var libs_server_config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! libs/server/config */ \"(api)/./libs/server/config.ts\");\n/* harmony import */ var _tree_postgresql__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tree-postgresql */ \"(api)/./libs/server/store/tree-postgresql.ts\");\n\n\nfunction createTreeStore(store) {\n    const cfg = (0,libs_server_config__WEBPACK_IMPORTED_MODULE_0__.config)().store;\n    return new _tree_postgresql__WEBPACK_IMPORTED_MODULE_1__.TreeStorePostgreSQL({\n        connectionString: cfg.connectionString\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9saWJzL3NlcnZlci9zdG9yZS90cmVlLWZhY3RvcnkudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTBFO0FBQ2xCO0FBR2pELFNBQVNFLGVBQWUsQ0FBQ0MsS0FBb0IsRUFBdUI7SUFDdkUsTUFBTUMsR0FBRyxHQUFHSiwwREFBTSxFQUFFLENBQUNHLEtBQUs7SUFFMUIsT0FBTyxJQUFJRixpRUFBbUIsQ0FBQztRQUMzQkksZ0JBQWdCLEVBQUVELEdBQUcsQ0FBQ0MsZ0JBQWdCO0tBQ3pDLENBQUMsQ0FBQztBQUNQLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ub3RlYS8uL2xpYnMvc2VydmVyL3N0b3JlL3RyZWUtZmFjdG9yeS50cz8zMjlkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNvbmZpZywgUG9zdGdyZVNRTFN0b3JlQ29uZmlndXJhdGlvbiB9IGZyb20gJ2xpYnMvc2VydmVyL2NvbmZpZyc7XG5pbXBvcnQgeyBUcmVlU3RvcmVQb3N0Z3JlU1FMIH0gZnJvbSAnLi90cmVlLXBvc3RncmVzcWwnO1xuaW1wb3J0IHsgU3RvcmVQcm92aWRlciB9IGZyb20gJy4vcHJvdmlkZXJzL2Jhc2UnO1xuXG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlVHJlZVN0b3JlKHN0b3JlOiBTdG9yZVByb3ZpZGVyKTogVHJlZVN0b3JlUG9zdGdyZVNRTCB7XG4gICAgY29uc3QgY2ZnID0gY29uZmlnKCkuc3RvcmUgYXMgUG9zdGdyZVNRTFN0b3JlQ29uZmlndXJhdGlvbjtcblxuICAgIHJldHVybiBuZXcgVHJlZVN0b3JlUG9zdGdyZVNRTCh7XG4gICAgICAgIGNvbm5lY3Rpb25TdHJpbmc6IGNmZy5jb25uZWN0aW9uU3RyaW5nLFxuICAgIH0pO1xufVxuIl0sIm5hbWVzIjpbImNvbmZpZyIsIlRyZWVTdG9yZVBvc3RncmVTUUwiLCJjcmVhdGVUcmVlU3RvcmUiLCJzdG9yZSIsImNmZyIsImNvbm5lY3Rpb25TdHJpbmciXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api)/./libs/server/store/tree-factory.ts\n");

/***/ }),

/***/ "(api)/./libs/server/store/tree-postgresql.ts":
/*!**********************************************!*\
  !*** ./libs/server/store/tree-postgresql.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"TreeStorePostgreSQL\": () => (/* binding */ TreeStorePostgreSQL)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(pg__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var libs_server_debugging__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! libs/server/debugging */ \"(api)/./libs/server/debugging.ts\");\n/* harmony import */ var libs_shared_tree__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! libs/shared/tree */ \"(api)/./libs/shared/tree.ts\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_3__);\n/**\n * PostgreSQL Tree Store\n *\n * Copyright (c) 2025 waycaan\n * Licensed under the MIT License\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n */ \n\n\n\n\nfunction fixedTree(tree) {\n    (0,lodash__WEBPACK_IMPORTED_MODULE_3__.forEach)(tree.items, (item)=>{\n        if (item.children.find((i)=>i === null || i === item.id || !tree.items[i])) {\n            console.log(\"item.children error\", item);\n            tree.items[item.id] = {\n                ...item,\n                children: (0,lodash__WEBPACK_IMPORTED_MODULE_3__.filter)(item.children, (cid)=>!(0,lodash__WEBPACK_IMPORTED_MODULE_3__.isNil)(cid) && cid !== item.id && !!tree.items[cid])\n            };\n        }\n    });\n    return tree;\n}\nclass TreeStorePostgreSQL {\n    logger = (0,libs_server_debugging__WEBPACK_IMPORTED_MODULE_1__.createLogger)(\"tree-store.postgresql\");\n    constructor(config){\n        this.pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool({\n            connectionString: config.connectionString,\n            ssl:  false ? 0 : false,\n            max: 1,\n            idleTimeoutMillis: 10000,\n            connectionTimeoutMillis: 10000\n        });\n    }\n    async get() {\n        const client = await this.pool.connect();\n        try {\n            const result = await client.query(\"SELECT data FROM tree_data WHERE id = $1\", [\n                \"main\"\n            ]);\n            if (result.rows.length === 0) {\n                const defaultTree = fixedTree(libs_shared_tree__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_TREE);\n                await client.query(`\n                    INSERT INTO tree_data (id, data, updated_at)\n                    VALUES ('main', $1, NOW())\n                `, [\n                    JSON.stringify(defaultTree)\n                ]);\n                this.logger.debug(\"Initialized default tree\");\n                return defaultTree;\n            }\n            const tree = result.rows[0].data;\n            return fixedTree(tree);\n        } catch (error) {\n            this.logger.error(\"Error getting tree:\", error);\n            return fixedTree(libs_shared_tree__WEBPACK_IMPORTED_MODULE_2__.DEFAULT_TREE);\n        } finally{\n            client.release();\n        }\n    }\n    async set(tree) {\n        const newTree = fixedTree(tree);\n        const client = await this.pool.connect();\n        try {\n            await client.query(`\n                INSERT INTO tree_data (id, data, updated_at)\n                VALUES ('main', $1, NOW())\n                ON CONFLICT (id)\n                DO UPDATE SET\n                    data = EXCLUDED.data,\n                    updated_at = NOW()\n            `, [\n                JSON.stringify(newTree)\n            ]);\n            this.logger.debug(\"Successfully updated tree data\");\n            return newTree;\n        } catch (error) {\n            this.logger.error(\"Error updating tree:\", error);\n            throw error;\n        } finally{\n            client.release();\n        }\n    }\n    async addItem(id, parentId = libs_shared_tree__WEBPACK_IMPORTED_MODULE_2__.ROOT_ID) {\n        const tree = await this.get();\n        return await this.set(libs_shared_tree__WEBPACK_IMPORTED_MODULE_2__[\"default\"].addItem(tree, id, parentId));\n    }\n    async addItems(ids, parentId = libs_shared_tree__WEBPACK_IMPORTED_MODULE_2__.ROOT_ID) {\n        let tree = await this.get();\n        ids.forEach((id)=>{\n            tree = libs_shared_tree__WEBPACK_IMPORTED_MODULE_2__[\"default\"].addItem(tree, id, parentId);\n        });\n        return await this.set(tree);\n    }\n    async removeItem(id) {\n        const tree = await this.get();\n        return await this.set(libs_shared_tree__WEBPACK_IMPORTED_MODULE_2__[\"default\"].removeItem(tree, id));\n    }\n    async moveItem(source, destination) {\n        const tree = await this.get();\n        return await this.set(libs_shared_tree__WEBPACK_IMPORTED_MODULE_2__[\"default\"].moveItem(tree, source, destination));\n    }\n    async mutateItem(id, data) {\n        const tree = await this.get();\n        return await this.set(libs_shared_tree__WEBPACK_IMPORTED_MODULE_2__[\"default\"].mutateItem(tree, id, data));\n    }\n    async restoreItem(id, parentId) {\n        const tree = await this.get();\n        return await this.set(libs_shared_tree__WEBPACK_IMPORTED_MODULE_2__[\"default\"].restoreItem(tree, id, parentId));\n    }\n    async deleteItem(id) {\n        const tree = await this.get();\n        return await this.set(libs_shared_tree__WEBPACK_IMPORTED_MODULE_2__[\"default\"].deleteItem(tree, id));\n    }\n    async close() {\n        await this.pool.end();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./libs/server/store/tree-postgresql.ts\n");

/***/ }),

/***/ "(api)/./libs/shared/const.ts":
/*!******************************!*\
  !*** ./libs/shared/const.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"CSRF_HEADER_KEY\": () => (/* binding */ CSRF_HEADER_KEY),\n/* harmony export */   \"DEMO_INJECTION\": () => (/* binding */ DEMO_INJECTION),\n/* harmony export */   \"IMPORT_FILE_LIMIT_SIZE\": () => (/* binding */ IMPORT_FILE_LIMIT_SIZE)\n/* harmony export */ });\nconst CSRF_HEADER_KEY = \"xsrf-token\";\nconst IMPORT_FILE_LIMIT_SIZE = 5 * 1024 * 1024;\nconst DEMO_INJECTION = `<div id=\"cusdis_thread\"\n  data-host=\"https://cusdis.com\"\n  data-app-id=\"61cfba44-ef71-4aa1-aa9b-58632fff9929\"\n  data-page-id=\"{id}\"\n  data-page-url=\"{url}\"\n  data-page-title=\"{title}\"\n></div>\n<script async defer src=\"https://cusdis.com/js/cusdis.es.js\"></script>`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9saWJzL3NoYXJlZC9jb25zdC50cy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBTyxNQUFNQSxlQUFlLEdBQUcsWUFBWSxDQUFDO0FBRXJDLE1BQU1DLHNCQUFzQixHQUFHLENBQUMsR0FBRyxJQUFJLEdBQUcsSUFBSSxDQUFDO0FBRS9DLE1BQU1DLGNBQWMsR0FBRyxDQUFDOzs7Ozs7O3NFQU91QyxDQUFDLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ub3RlYS8uL2xpYnMvc2hhcmVkL2NvbnN0LnRzP2ZlNTEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IENTUkZfSEVBREVSX0tFWSA9ICd4c3JmLXRva2VuJztcblxuZXhwb3J0IGNvbnN0IElNUE9SVF9GSUxFX0xJTUlUX1NJWkUgPSA1ICogMTAyNCAqIDEwMjQ7XG5cbmV4cG9ydCBjb25zdCBERU1PX0lOSkVDVElPTiA9IGA8ZGl2IGlkPVwiY3VzZGlzX3RocmVhZFwiXG4gIGRhdGEtaG9zdD1cImh0dHBzOi8vY3VzZGlzLmNvbVwiXG4gIGRhdGEtYXBwLWlkPVwiNjFjZmJhNDQtZWY3MS00YWExLWFhOWItNTg2MzJmZmY5OTI5XCJcbiAgZGF0YS1wYWdlLWlkPVwie2lkfVwiXG4gIGRhdGEtcGFnZS11cmw9XCJ7dXJsfVwiXG4gIGRhdGEtcGFnZS10aXRsZT1cInt0aXRsZX1cIlxuPjwvZGl2PlxuPHNjcmlwdCBhc3luYyBkZWZlciBzcmM9XCJodHRwczovL2N1c2Rpcy5jb20vanMvY3VzZGlzLmVzLmpzXCI+PC9zY3JpcHQ+YDtcbiJdLCJuYW1lcyI6WyJDU1JGX0hFQURFUl9LRVkiLCJJTVBPUlRfRklMRV9MSU1JVF9TSVpFIiwiREVNT19JTkpFQ1RJT04iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api)/./libs/shared/const.ts\n");

/***/ }),

/***/ "(api)/./libs/shared/debugging.ts":
/*!**********************************!*\
  !*** ./libs/shared/debugging.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"IssueCategory\": () => (/* binding */ IssueCategory),\n/* harmony export */   \"IssueFixRecommendation\": () => (/* binding */ IssueFixRecommendation),\n/* harmony export */   \"IssueSeverity\": () => (/* binding */ IssueSeverity),\n/* harmony export */   \"coerceToValidCause\": () => (/* binding */ coerceToValidCause),\n/* harmony export */   \"getNameFromRecommendation\": () => (/* binding */ getNameFromRecommendation),\n/* harmony export */   \"getNameFromSeverity\": () => (/* binding */ getNameFromSeverity),\n/* harmony export */   \"logLevelToString\": () => (/* binding */ logLevelToString),\n/* harmony export */   \"toErrorLike\": () => (/* binding */ toErrorLike)\n/* harmony export */ });\n/* harmony import */ var pino__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pino */ \"pino\");\n/* harmony import */ var pino__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(pino__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var libs_shared_util__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! libs/shared/util */ \"(api)/./libs/shared/util.ts\");\n\n\nvar IssueCategory;\n(function(IssueCategory) {\n    IssueCategory[\"CONFIG\"] = \"config\";\n    IssueCategory[\"MISC\"] = \"misc\";\n    IssueCategory[\"STORE\"] = \"store\";\n})(IssueCategory || (IssueCategory = {}));\nvar IssueSeverity;\n(function(IssueSeverity) {\n    IssueSeverity[IssueSeverity[/**\n     * Suggestions are issues that suggest things the user can do to improve\n     * their experience with Notea.\n     */ \"SUGGESTION\"] = 0] = \"SUGGESTION\";\n    IssueSeverity[IssueSeverity[/**\n     * Warnings are issues that aren't severe enough to cause major issues by\n     * themselves, but that *could* cause worse issues.\n     */ \"WARNING\"] = 1] = \"WARNING\";\n    IssueSeverity[IssueSeverity[/**\n     * Errors are issues that are severe enough to cause major issues by themselves.\n     * They don't necessarily cause the entire instance to stop working though.\n     */ \"ERROR\"] = 2] = \"ERROR\";\n    IssueSeverity[IssueSeverity[/**\n     * Fatal errors are issues that must be resolved before Notea starts working.\n     */ \"FATAL_ERROR\"] = 3] = \"FATAL_ERROR\";\n})(IssueSeverity || (IssueSeverity = {}));\nfunction getNameFromSeverity(severity, { t  }) {\n    switch(severity){\n        case IssueSeverity.SUGGESTION:\n            return t(\"Suggestion\");\n        case IssueSeverity.WARNING:\n            return t(\"Warning\");\n        case IssueSeverity.ERROR:\n            return t(\"Error\");\n        case IssueSeverity.FATAL_ERROR:\n            return t(\"Fatal error\");\n    }\n}\nvar IssueFixRecommendation;\n(function(IssueFixRecommendation) {\n    IssueFixRecommendation[IssueFixRecommendation[\"NEUTRAL\"] = 0] = \"NEUTRAL\";\n    IssueFixRecommendation[IssueFixRecommendation[\"RECOMMENDED\"] = 1] = \"RECOMMENDED\";\n    IssueFixRecommendation[IssueFixRecommendation[\"NOT_ADVISED\"] = 2] = \"NOT_ADVISED\";\n})(IssueFixRecommendation || (IssueFixRecommendation = {}));\nfunction getNameFromRecommendation(recommendation, { t  }) {\n    switch(recommendation){\n        case IssueFixRecommendation.NEUTRAL:\n            return t(\"Neutral\");\n        case IssueFixRecommendation.RECOMMENDED:\n            return t(\"Recommended\");\n        case IssueFixRecommendation.NOT_ADVISED:\n            return t(\"Not advised\");\n    }\n}\nfunction logLevelToString(level) {\n    return (pino__WEBPACK_IMPORTED_MODULE_0___default().levels.labels)[level];\n}\nfunction coerceToValidCause(e) {\n    if ((0,libs_shared_util__WEBPACK_IMPORTED_MODULE_1__.isProbablyError)(e)) {\n        return toErrorLike(e);\n    }\n    return String(e);\n}\n// NOTE(tecc): This has to be done because otherwise Next just does not play nice\nfunction toErrorLike(e) {\n    return {\n        name: e.name,\n        stack: e.stack,\n        message: e.message\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./libs/shared/debugging.ts\n");

/***/ }),

/***/ "(api)/./libs/shared/env.ts":
/*!****************************!*\
  !*** ./libs/shared/env.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"getEnv\": () => (/* binding */ getEnv),\n/* harmony export */   \"getEnvRaw\": () => (/* binding */ getEnvRaw),\n/* harmony export */   \"parseBool\": () => (/* binding */ parseBool)\n/* harmony export */ });\n/**\n * @deprecated This function should not be used. Prefer the `config()` system.\n */ function getEnv(env, defaultValue, required = false) {\n    const value = process.env[env];\n    if (!value) {\n        if (required && !defaultValue) {\n            throw new Error(`[Notea] \\`${env}\\` is required`);\n        }\n        return defaultValue;\n    }\n    const v = value.toLocaleLowerCase();\n    let result;\n    if (v === \"false\") {\n        result = false;\n    } else if (v === \"true\") {\n        result = true;\n    } else if (/^\\d+$/.test(v)) {\n        result = v * 1;\n    } else {\n        result = value;\n    }\n    return result;\n}\nfunction getEnvRaw(env, required = false) {\n    const value = process.env[env];\n    if (value == null) {\n        if (required) {\n            throw new Error(`[Notea] ${env} is undefined`);\n        } else {\n            return undefined;\n        }\n    }\n    return String(value);\n}\nfunction parseBool(str, invalid) {\n    if (str == null) {\n        return invalid ?? undefined;\n    }\n    switch(str.toLowerCase()){\n        case \"true\":\n        case \"1\":\n        case \"yes\":\n        case \"on\":\n            return true;\n        case \"false\":\n        case \"0\":\n        case \"no\":\n        case \"off\":\n            return false;\n        default:\n            if (invalid == null) throw new Error(\"Invalid boolean: \" + str);\n            else return invalid;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./libs/shared/env.ts\n");

/***/ }),

/***/ "(api)/./libs/shared/id.ts":
/*!***************************!*\
  !*** ./libs/shared/id.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"genFriendlyId\": () => (/* binding */ genFriendlyId),\n/* harmony export */   \"genId\": () => (/* binding */ genId)\n/* harmony export */ });\n/* harmony import */ var nanoid__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! nanoid */ \"nanoid\");\n/* harmony import */ var nanoid__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(nanoid__WEBPACK_IMPORTED_MODULE_0__);\n\nconst genFriendlyId = (0,nanoid__WEBPACK_IMPORTED_MODULE_0__.customAlphabet)(\"23456789abcdefghjkmnpqrstuvwxyz\", 4);\nconst genId = ()=>(0,nanoid__WEBPACK_IMPORTED_MODULE_0__.nanoid)(10);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9saWJzL3NoYXJlZC9pZC50cy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWdEO0FBRXpDLE1BQU1FLGFBQWEsR0FBR0Qsc0RBQWMsQ0FDdkMsaUNBQWlDLEVBQ2pDLENBQUMsQ0FDSixDQUFDO0FBRUssTUFBTUUsS0FBSyxHQUFHLElBQU1ILDhDQUFNLENBQUMsRUFBRSxDQUFDLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ub3RlYS8uL2xpYnMvc2hhcmVkL2lkLnRzP2EwYWIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgbmFub2lkLCBjdXN0b21BbHBoYWJldCB9IGZyb20gJ25hbm9pZCc7XG5cbmV4cG9ydCBjb25zdCBnZW5GcmllbmRseUlkID0gY3VzdG9tQWxwaGFiZXQoXG4gICAgJzIzNDU2Nzg5YWJjZGVmZ2hqa21ucHFyc3R1dnd4eXonLFxuICAgIDRcbik7XG5cbmV4cG9ydCBjb25zdCBnZW5JZCA9ICgpID0+IG5hbm9pZCgxMCk7XG4iXSwibmFtZXMiOlsibmFub2lkIiwiY3VzdG9tQWxwaGFiZXQiLCJnZW5GcmllbmRseUlkIiwiZ2VuSWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api)/./libs/shared/id.ts\n");

/***/ }),

/***/ "(api)/./libs/shared/meta.ts":
/*!*****************************!*\
  !*** ./libs/shared/meta.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"EDITOR_SIZE\": () => (/* binding */ EDITOR_SIZE),\n/* harmony export */   \"NOTE_DELETED\": () => (/* binding */ NOTE_DELETED),\n/* harmony export */   \"NOTE_PINNED\": () => (/* binding */ NOTE_PINNED),\n/* harmony export */   \"NOTE_SHARED\": () => (/* binding */ NOTE_SHARED),\n/* harmony export */   \"NUMBER_KEYS\": () => (/* binding */ NUMBER_KEYS),\n/* harmony export */   \"PAGE_META_KEY\": () => (/* binding */ PAGE_META_KEY)\n/* harmony export */ });\nvar NOTE_DELETED;\n(function(NOTE_DELETED) {\n    NOTE_DELETED[NOTE_DELETED[\"NORMAL\"] = 0] = \"NORMAL\";\n    NOTE_DELETED[NOTE_DELETED[\"DELETED\"] = 1] = \"DELETED\";\n})(NOTE_DELETED || (NOTE_DELETED = {}));\nvar NOTE_SHARED;\n(function(NOTE_SHARED) {\n    NOTE_SHARED[NOTE_SHARED[\"PRIVATE\"] = 0] = \"PRIVATE\";\n    NOTE_SHARED[NOTE_SHARED[\"PUBLIC\"] = 1] = \"PUBLIC\";\n})(NOTE_SHARED || (NOTE_SHARED = {}));\nvar NOTE_PINNED;\n(function(NOTE_PINNED) {\n    NOTE_PINNED[NOTE_PINNED[\"UNPINNED\"] = 0] = \"UNPINNED\";\n    NOTE_PINNED[NOTE_PINNED[\"PINNED\"] = 1] = \"PINNED\";\n})(NOTE_PINNED || (NOTE_PINNED = {}));\nvar EDITOR_SIZE;\n(function(EDITOR_SIZE) {\n    EDITOR_SIZE[EDITOR_SIZE[\"SMALL\"] = 0] = \"SMALL\";\n    EDITOR_SIZE[EDITOR_SIZE[\"LARGE\"] = 1] = \"LARGE\";\n    EDITOR_SIZE[EDITOR_SIZE[\"FULL\"] = 2] = \"FULL\";\n})(EDITOR_SIZE || (EDITOR_SIZE = {}));\nconst PAGE_META_KEY = [\n    \"title\",\n    \"pid\",\n    \"id\",\n    \"shared\",\n    \"pic\",\n    \"date\",\n    \"deleted\",\n    \"pinned\",\n    \"editorsize\", \n];\nconst NUMBER_KEYS = [\n    \"deleted\",\n    \"shared\",\n    \"pinned\",\n    \"editorsize\", \n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./libs/shared/meta.ts\n");

/***/ }),

/***/ "(api)/./libs/shared/page.ts":
/*!*****************************!*\
  !*** ./libs/shared/page.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"PageMode\": () => (/* binding */ PageMode)\n/* harmony export */ });\nvar PageMode;\n(function(PageMode) {\n    PageMode[\"PUBLIC\"] = \"PUBLIC\";\n    PageMode[\"NOTE\"] = \"NOTE\";\n})(PageMode || (PageMode = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9saWJzL3NoYXJlZC9wYWdlLnRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTyxZQUdOO1VBSFdBLFFBQVE7SUFBUkEsUUFBUSxDQUNoQkMsUUFBTSxJQUFOQSxRQUFNO0lBREVELFFBQVEsQ0FFaEJFLE1BQUksSUFBSkEsTUFBSTtHQUZJRixRQUFRLEtBQVJBLFFBQVEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ub3RlYS8uL2xpYnMvc2hhcmVkL3BhZ2UudHM/MmM5MSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZW51bSBQYWdlTW9kZSB7XG4gICAgUFVCTElDID0gJ1BVQkxJQycsXG4gICAgTk9URSA9ICdOT1RFJyxcbn1cbiJdLCJuYW1lcyI6WyJQYWdlTW9kZSIsIlBVQkxJQyIsIk5PVEUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(api)/./libs/shared/page.ts\n");

/***/ }),

/***/ "(api)/./libs/shared/str.ts":
/*!****************************!*\
  !*** ./libs/shared/str.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"strCompress\": () => (/* binding */ strCompress),\n/* harmony export */   \"strDecompress\": () => (/* binding */ strDecompress),\n/* harmony export */   \"toBuffer\": () => (/* binding */ toBuffer),\n/* harmony export */   \"toStr\": () => (/* binding */ toStr),\n/* harmony export */   \"tryJSON\": () => (/* binding */ tryJSON)\n/* harmony export */ });\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var lzutf8__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lzutf8 */ \"lzutf8\");\n/* harmony import */ var lzutf8__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lzutf8__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction toBuffer(raw, compressed = false) {\n    if (!raw) {\n        return Buffer.from(\"\");\n    }\n    const str = (0,lodash__WEBPACK_IMPORTED_MODULE_0__.isString)(raw) ? raw : JSON.stringify(raw);\n    return Buffer.from(compressed ? strCompress(str) : str);\n}\nfunction toStr(bufferOrString, deCompressed = false) {\n    if (!bufferOrString) return;\n    const str = Buffer.isBuffer(bufferOrString) ? bufferOrString.toString(\"utf-8\") : bufferOrString;\n    return deCompressed ? strDecompress(str) : str;\n}\nfunction tryJSON(str) {\n    if ((0,lodash__WEBPACK_IMPORTED_MODULE_0__.isNil)(str)) return null;\n    try {\n        return JSON.parse(str);\n    } catch (e) {\n        console.error(\"parse error\", str);\n        return null;\n    }\n}\nfunction strDecompress(raw) {\n    if ((0,lodash__WEBPACK_IMPORTED_MODULE_0__.isNil)(raw)) return null;\n    return (0,lzutf8__WEBPACK_IMPORTED_MODULE_1__.decompress)(raw, {\n        inputEncoding: \"Base64\"\n    });\n}\nfunction strCompress(str) {\n    return (0,lzutf8__WEBPACK_IMPORTED_MODULE_1__.compress)(str, {\n        outputEncoding: \"Base64\"\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./libs/shared/str.ts\n");

/***/ }),

/***/ "(api)/./libs/shared/tree.ts":
/*!*****************************!*\
  !*** ./libs/shared/tree.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"DEFAULT_TREE\": () => (/* binding */ DEFAULT_TREE),\n/* harmony export */   \"ROOT_ID\": () => (/* binding */ ROOT_ID),\n/* harmony export */   \"cleanItemModel\": () => (/* binding */ cleanItemModel),\n/* harmony export */   \"cleanTreeModel\": () => (/* binding */ cleanTreeModel),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   \"makeHierarchy\": () => (/* binding */ makeHierarchy)\n/* harmony export */ });\n/* harmony import */ var _atlaskit_tree__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @atlaskit/tree */ \"@atlaskit/tree\");\n/* harmony import */ var _atlaskit_tree__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_atlaskit_tree__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst ROOT_ID = \"root\";\nconst DEFAULT_TREE = {\n    rootId: ROOT_ID,\n    items: {\n        root: {\n            id: ROOT_ID,\n            children: []\n        }\n    }\n};\nfunction addItem(tree, id, pid = ROOT_ID) {\n    const newTree = (0,lodash__WEBPACK_IMPORTED_MODULE_1__.cloneDeep)(tree);\n    newTree.items[id] = newTree.items[id] || {\n        id,\n        children: []\n    };\n    const parentItem = newTree.items[pid];\n    if (parentItem) {\n        if (!parentItem.children.includes(id)) {\n            parentItem.children = [\n                ...parentItem.children,\n                id\n            ];\n        }\n    } else {\n        throw new Error(`Parent ID '${pid}' does not refer to a valid item`);\n    }\n    return newTree;\n}\nfunction mutateItem(tree, id, data) {\n    if (data.data) {\n        data.data = {\n            ...tree.items[id]?.data,\n            ...data.data\n        };\n    }\n    return (0,_atlaskit_tree__WEBPACK_IMPORTED_MODULE_0__.mutateTree)(tree, id, data);\n}\nfunction removeItem(tree, id) {\n    (0,lodash__WEBPACK_IMPORTED_MODULE_1__.forEach)(tree.items, (item)=>{\n        if (item.children.includes(id)) {\n            (0,lodash__WEBPACK_IMPORTED_MODULE_1__.pull)(item.children, id);\n            return false;\n        }\n    });\n    return (0,lodash__WEBPACK_IMPORTED_MODULE_1__.cloneDeep)(tree);\n}\nfunction moveItem(tree, source, destination) {\n    if (!destination) {\n        return tree;\n    }\n    return (0,_atlaskit_tree__WEBPACK_IMPORTED_MODULE_0__.moveItemOnTree)(tree, source, destination);\n}\n/**\n * 从原父节点上移除，添加到新的父节点上\n */ function restoreItem(tree, id, pid = ROOT_ID) {\n    tree = removeItem(tree, id);\n    tree = addItem(tree, id, pid);\n    return tree;\n}\nfunction deleteItem(tree, id) {\n    tree = (0,lodash__WEBPACK_IMPORTED_MODULE_1__.cloneDeep)(tree);\n    delete tree.items[id];\n    return tree;\n}\nconst flattenTree = (tree, rootId = tree.rootId)=>{\n    if (!tree.items[rootId]) {\n        return [];\n    }\n    return (0,lodash__WEBPACK_IMPORTED_MODULE_1__.reduce)(tree.items[rootId].children, (accum, itemId)=>{\n        const item = tree.items[itemId];\n        const children = flattenTree({\n            rootId: item.id,\n            items: tree.items\n        });\n        return [\n            ...accum,\n            item,\n            ...children\n        ];\n    }, []);\n};\nfunction makeHierarchy(tree, rootId = tree.rootId) {\n    if (!tree.items[rootId]) {\n        return false;\n    }\n    const root = tree.items[rootId];\n    return {\n        ...root,\n        children: root.children.map((v)=>makeHierarchy(tree, v)).filter((v)=>!!v)\n    };\n}\nfunction cleanItemModel(model) {\n    if (!model.id) throw new Error(\"Missing id on tree model\");\n    const children = model.children ?? [];\n    return {\n        ...model,\n        id: model.id,\n        children,\n        hasChildren: children.length > 0,\n        data: model.data,\n        isExpanded: model.isExpanded ?? false\n    };\n}\nfunction cleanTreeModel(model) {\n    const items = {};\n    if (model.items) {\n        for(const itemId in model.items){\n            const item = model.items[itemId];\n            if (!item) {\n                continue;\n            }\n            const cleanedItem = cleanItemModel(item);\n            const children = [];\n            for (const child of cleanedItem.children){\n                if (child && model.items[child]) {\n                    children.push(child);\n                }\n            }\n            items[itemId] = {\n                ...cleanedItem,\n                children\n            };\n        }\n    }\n    return {\n        ...model,\n        rootId: model.rootId ?? ROOT_ID,\n        items: items\n    };\n}\nconst TreeActions = {\n    addItem,\n    mutateItem,\n    removeItem,\n    moveItem,\n    restoreItem,\n    deleteItem,\n    flattenTree,\n    makeHierarchy,\n    cleanTreeModel,\n    cleanItemModel\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TreeActions);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./libs/shared/tree.ts\n");

/***/ }),

/***/ "(api)/./libs/shared/util.ts":
/*!*****************************!*\
  !*** ./libs/shared/util.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"ensureEndingUnlessEmpty\": () => (/* binding */ ensureEndingUnlessEmpty),\n/* harmony export */   \"errorToString\": () => (/* binding */ errorToString),\n/* harmony export */   \"isProbablyError\": () => (/* binding */ isProbablyError)\n/* harmony export */ });\nfunction isProbablyError(e) {\n    if (!e) return false;\n    if (e instanceof Error) return true;\n    return e && e.stack && e.message;\n}\nfunction errorToString(error) {\n    let str = \"\";\n    if (error.stack) {\n        return error.stack;\n    }\n    if (error.name) {\n        str += error.name;\n    }\n    if (error.message) {\n        str = ensureEndingUnlessEmpty(str, \": \") + error.message;\n    }\n    return str;\n}\nfunction ensureEndingUnlessEmpty(str, ending) {\n    if (str.length > 0 && !str.endsWith(ending)) {\n        return str + ending;\n    }\n    return str;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./libs/shared/util.ts\n");

/***/ }),

/***/ "(api)/./pages/api/notes/index.ts":
/*!**********************************!*\
  !*** ./pages/api/notes/index.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var libs_server_connect__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! libs/server/connect */ \"(api)/./libs/server/connect.ts\");\n/* harmony import */ var libs_server_middlewares_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! libs/server/middlewares/auth */ \"(api)/./libs/server/middlewares/auth.ts\");\n/* harmony import */ var libs_server_middlewares_store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! libs/server/middlewares/store */ \"(api)/./libs/server/middlewares/store.ts\");\n/* harmony import */ var libs_server_note__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! libs/server/note */ \"(api)/./libs/server/note.ts\");\n/* harmony import */ var libs_server_middlewares_rate_limit__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! libs/server/middlewares/rate-limit */ \"(api)/./libs/server/middlewares/rate-limit.ts\");\n\n\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,libs_server_connect__WEBPACK_IMPORTED_MODULE_0__.api)().use(libs_server_middlewares_rate_limit__WEBPACK_IMPORTED_MODULE_4__.notesRateLimit).use(libs_server_middlewares_auth__WEBPACK_IMPORTED_MODULE_1__.useAuth).use(libs_server_middlewares_store__WEBPACK_IMPORTED_MODULE_2__.useStore).post(async (req, res)=>{\n    const note = await (0,libs_server_note__WEBPACK_IMPORTED_MODULE_3__.createNote)(req.body, req.state);\n    await req.state.treeStore.addItem(note.id, note.pid);\n    res.json(note);\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9wYWdlcy9hcGkvbm90ZXMvaW5kZXgudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTBDO0FBQ2E7QUFDRTtBQUNYO0FBQ3NCO0FBRXBFLGlFQUFlQSx3REFBRyxFQUFFLENBQ2ZLLEdBQUcsQ0FBQ0QsOEVBQWMsQ0FBQyxDQUNuQkMsR0FBRyxDQUFDSixpRUFBTyxDQUFDLENBQ1pJLEdBQUcsQ0FBQ0gsbUVBQVEsQ0FBQyxDQUNiSSxJQUFJLENBQUMsT0FBT0MsR0FBRyxFQUFFQyxHQUFHLEdBQUs7SUFDdEIsTUFBTUMsSUFBSSxHQUFHLE1BQU1OLDREQUFVLENBQUNJLEdBQUcsQ0FBQ0csSUFBSSxFQUFFSCxHQUFHLENBQUNJLEtBQUssQ0FBQztJQUNsRCxNQUFNSixHQUFHLENBQUNJLEtBQUssQ0FBQ0MsU0FBUyxDQUFDQyxPQUFPLENBQUNKLElBQUksQ0FBQ0ssRUFBRSxFQUFFTCxJQUFJLENBQUNNLEdBQUcsQ0FBQyxDQUFDO0lBRXJEUCxHQUFHLENBQUNRLElBQUksQ0FBQ1AsSUFBSSxDQUFDLENBQUM7QUFDbkIsQ0FBQyxDQUFDLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ub3RlYS8uL3BhZ2VzL2FwaS9ub3Rlcy9pbmRleC50cz9iNDIwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGFwaSB9IGZyb20gJ2xpYnMvc2VydmVyL2Nvbm5lY3QnO1xuaW1wb3J0IHsgdXNlQXV0aCB9IGZyb20gJ2xpYnMvc2VydmVyL21pZGRsZXdhcmVzL2F1dGgnO1xuaW1wb3J0IHsgdXNlU3RvcmUgfSBmcm9tICdsaWJzL3NlcnZlci9taWRkbGV3YXJlcy9zdG9yZSc7XG5pbXBvcnQgeyBjcmVhdGVOb3RlIH0gZnJvbSAnbGlicy9zZXJ2ZXIvbm90ZSc7XG5pbXBvcnQgeyBub3Rlc1JhdGVMaW1pdCB9IGZyb20gJ2xpYnMvc2VydmVyL21pZGRsZXdhcmVzL3JhdGUtbGltaXQnO1xuXG5leHBvcnQgZGVmYXVsdCBhcGkoKVxuICAgIC51c2Uobm90ZXNSYXRlTGltaXQpXG4gICAgLnVzZSh1c2VBdXRoKVxuICAgIC51c2UodXNlU3RvcmUpXG4gICAgLnBvc3QoYXN5bmMgKHJlcSwgcmVzKSA9PiB7XG4gICAgICAgIGNvbnN0IG5vdGUgPSBhd2FpdCBjcmVhdGVOb3RlKHJlcS5ib2R5LCByZXEuc3RhdGUpO1xuICAgICAgICBhd2FpdCByZXEuc3RhdGUudHJlZVN0b3JlLmFkZEl0ZW0obm90ZS5pZCwgbm90ZS5waWQpO1xuXG4gICAgICAgIHJlcy5qc29uKG5vdGUpO1xuICAgIH0pO1xuIl0sIm5hbWVzIjpbImFwaSIsInVzZUF1dGgiLCJ1c2VTdG9yZSIsImNyZWF0ZU5vdGUiLCJub3Rlc1JhdGVMaW1pdCIsInVzZSIsInBvc3QiLCJyZXEiLCJyZXMiLCJub3RlIiwiYm9keSIsInN0YXRlIiwidHJlZVN0b3JlIiwiYWRkSXRlbSIsImlkIiwicGlkIiwianNvbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/./pages/api/notes/index.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("(api)/./pages/api/notes/index.ts"));
module.exports = __webpack_exports__;

})();