{"version": 3, "names": ["RESERVED_WORDS_ES3_ONLY", "Set", "isValidES3Identifier", "name", "isValidIdentifier", "has"], "sources": ["../../src/validators/isValidES3Identifier.ts"], "sourcesContent": ["import isValidIdentifier from \"./isValidIdentifier\";\n\nconst RESERVED_WORDS_ES3_ONLY: Set<string> = new Set([\n  \"abstract\",\n  \"boolean\",\n  \"byte\",\n  \"char\",\n  \"double\",\n  \"enum\",\n  \"final\",\n  \"float\",\n  \"goto\",\n  \"implements\",\n  \"int\",\n  \"interface\",\n  \"long\",\n  \"native\",\n  \"package\",\n  \"private\",\n  \"protected\",\n  \"public\",\n  \"short\",\n  \"static\",\n  \"synchronized\",\n  \"throws\",\n  \"transient\",\n  \"volatile\",\n]);\n\n/**\n * Check if the input `name` is a valid identifier name according to the ES3 specification.\n *\n * Additional ES3 reserved words are\n */\nexport default function isValidES3Identifier(name: string): boolean {\n  return isValidIdentifier(name) && !RESERVED_WORDS_ES3_ONLY.has(name);\n}\n"], "mappings": ";;;;;;;AAAA;;AAEA,MAAMA,uBAAoC,GAAG,IAAIC,GAAJ,CAAQ,CACnD,UADmD,EAEnD,SAFmD,EAGnD,MAHmD,EAInD,MAJmD,EAKnD,QALmD,EAMnD,MANmD,EAOnD,OAPmD,EAQnD,OARmD,EASnD,MATmD,EAUnD,YAVmD,EAWnD,KAXmD,EAYnD,WAZmD,EAanD,MAbmD,EAcnD,QAdmD,EAenD,SAfmD,EAgBnD,SAhBmD,EAiBnD,WAjBmD,EAkBnD,QAlBmD,EAmBnD,OAnBmD,EAoBnD,QApBmD,EAqBnD,cArBmD,EAsBnD,QAtBmD,EAuBnD,WAvBmD,EAwBnD,UAxBmD,CAAR,CAA7C;;AAgCe,SAASC,oBAAT,CAA8BC,IAA9B,EAAqD;EAClE,OAAO,IAAAC,0BAAA,EAAkBD,IAAlB,KAA2B,CAACH,uBAAuB,CAACK,GAAxB,CAA4BF,IAA5B,CAAnC;AACD"}