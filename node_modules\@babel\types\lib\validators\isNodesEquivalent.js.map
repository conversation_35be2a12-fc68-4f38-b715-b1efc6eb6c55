{"version": 3, "names": ["isNodesEquivalent", "a", "b", "type", "fields", "Object", "keys", "NODE_FIELDS", "visitorKeys", "VISITOR_KEYS", "field", "val_a", "val_b", "Array", "isArray", "length", "i", "includes", "key"], "sources": ["../../src/validators/isNodesEquivalent.ts"], "sourcesContent": ["import { NODE_FIELDS, VISITOR_KEYS } from \"../definitions\";\nimport type * as t from \"..\";\n\n/**\n * Check if two nodes are equivalent\n */\nexport default function isNodesEquivalent<T extends Partial<t.Node>>(\n  a: T,\n  b: any,\n): b is T {\n  if (\n    typeof a !== \"object\" ||\n    typeof b !== \"object\" ||\n    a == null ||\n    b == null\n  ) {\n    return a === b;\n  }\n\n  if (a.type !== b.type) {\n    return false;\n  }\n\n  const fields = Object.keys(NODE_FIELDS[a.type] || a.type);\n  const visitorKeys = VISITOR_KEYS[a.type];\n\n  for (const field of fields) {\n    const val_a =\n      // @ts-expect-error field must present in a\n      a[field];\n    const val_b = b[field];\n    if (typeof val_a !== typeof val_b) {\n      return false;\n    }\n    if (val_a == null && val_b == null) {\n      continue;\n    } else if (val_a == null || val_b == null) {\n      return false;\n    }\n\n    if (Array.isArray(val_a)) {\n      if (!Array.isArray(val_b)) {\n        return false;\n      }\n      if (val_a.length !== val_b.length) {\n        return false;\n      }\n\n      for (let i = 0; i < val_a.length; i++) {\n        if (!isNodesEquivalent(val_a[i], val_b[i])) {\n          return false;\n        }\n      }\n      continue;\n    }\n\n    if (typeof val_a === \"object\" && !visitorKeys?.includes(field)) {\n      for (const key of Object.keys(val_a)) {\n        if (val_a[key] !== val_b[key]) {\n          return false;\n        }\n      }\n      continue;\n    }\n\n    if (!isNodesEquivalent(val_a, val_b)) {\n      return false;\n    }\n  }\n\n  return true;\n}\n"], "mappings": ";;;;;;;AAAA;;AAMe,SAASA,iBAAT,CACbC,CADa,EAEbC,CAFa,EAGL;EACR,IACE,OAAOD,CAAP,KAAa,QAAb,IACA,OAAOC,CAAP,KAAa,QADb,IAEAD,CAAC,IAAI,IAFL,IAGAC,CAAC,IAAI,IAJP,EAKE;IACA,OAAOD,CAAC,KAAKC,CAAb;EACD;;EAED,IAAID,CAAC,CAACE,IAAF,KAAWD,CAAC,CAACC,IAAjB,EAAuB;IACrB,OAAO,KAAP;EACD;;EAED,MAAMC,MAAM,GAAGC,MAAM,CAACC,IAAP,CAAYC,wBAAA,CAAYN,CAAC,CAACE,IAAd,KAAuBF,CAAC,CAACE,IAArC,CAAf;EACA,MAAMK,WAAW,GAAGC,yBAAA,CAAaR,CAAC,CAACE,IAAf,CAApB;;EAEA,KAAK,MAAMO,KAAX,IAAoBN,MAApB,EAA4B;IAC1B,MAAMO,KAAK,GAETV,CAAC,CAACS,KAAD,CAFH;IAGA,MAAME,KAAK,GAAGV,CAAC,CAACQ,KAAD,CAAf;;IACA,IAAI,OAAOC,KAAP,KAAiB,OAAOC,KAA5B,EAAmC;MACjC,OAAO,KAAP;IACD;;IACD,IAAID,KAAK,IAAI,IAAT,IAAiBC,KAAK,IAAI,IAA9B,EAAoC;MAClC;IACD,CAFD,MAEO,IAAID,KAAK,IAAI,IAAT,IAAiBC,KAAK,IAAI,IAA9B,EAAoC;MACzC,OAAO,KAAP;IACD;;IAED,IAAIC,KAAK,CAACC,OAAN,CAAcH,KAAd,CAAJ,EAA0B;MACxB,IAAI,CAACE,KAAK,CAACC,OAAN,CAAcF,KAAd,CAAL,EAA2B;QACzB,OAAO,KAAP;MACD;;MACD,IAAID,KAAK,CAACI,MAAN,KAAiBH,KAAK,CAACG,MAA3B,EAAmC;QACjC,OAAO,KAAP;MACD;;MAED,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGL,KAAK,CAACI,MAA1B,EAAkCC,CAAC,EAAnC,EAAuC;QACrC,IAAI,CAAChB,iBAAiB,CAACW,KAAK,CAACK,CAAD,CAAN,EAAWJ,KAAK,CAACI,CAAD,CAAhB,CAAtB,EAA4C;UAC1C,OAAO,KAAP;QACD;MACF;;MACD;IACD;;IAED,IAAI,OAAOL,KAAP,KAAiB,QAAjB,IAA6B,EAACH,WAAD,YAACA,WAAW,CAAES,QAAb,CAAsBP,KAAtB,CAAD,CAAjC,EAAgE;MAC9D,KAAK,MAAMQ,GAAX,IAAkBb,MAAM,CAACC,IAAP,CAAYK,KAAZ,CAAlB,EAAsC;QACpC,IAAIA,KAAK,CAACO,GAAD,CAAL,KAAeN,KAAK,CAACM,GAAD,CAAxB,EAA+B;UAC7B,OAAO,KAAP;QACD;MACF;;MACD;IACD;;IAED,IAAI,CAAClB,iBAAiB,CAACW,KAAD,EAAQC,KAAR,CAAtB,EAAsC;MACpC,OAAO,KAAP;IACD;EACF;;EAED,OAAO,IAAP;AACD"}