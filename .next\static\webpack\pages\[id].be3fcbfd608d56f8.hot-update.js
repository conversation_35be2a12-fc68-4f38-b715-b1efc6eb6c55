"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./components/editor/plugins/floating-toolbar-plugin.tsx":
/*!***************************************************************!*\
  !*** ./components/editor/plugins/floating-toolbar-plugin.tsx ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ FloatingToolbarPlugin; }\n/* harmony export */ });\n/* harmony import */ var _swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/src/_sliced_to_array.mjs */ \"./node_modules/@swc/helpers/src/_sliced_to_array.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"./node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lexical */ \"./node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var _lexical_link__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @lexical/link */ \"./node_modules/@lexical/link/LexicalLink.dev.mjs\");\n/* harmony import */ var _lexical_list__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @lexical/list */ \"./node_modules/@lexical/list/LexicalList.dev.mjs\");\n/* harmony import */ var _highlight_plugin__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./highlight-plugin */ \"./components/editor/plugins/highlight-plugin.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-dom */ \"./node_modules/react-dom/index.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-themes */ \"./node_modules/next-themes/dist/index.modern.js\");\n/* harmony import */ var _heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @heroicons/react/outline */ \"./node_modules/@heroicons/react/outline/esm/index.js\");\n/**\n * Floating Toolbar Plugin for Lexical\n * Shows formatting options when text is selected\n */ \n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Heroicons\n\nfunction FloatingToolbarPlugin() {\n    var _this = this;\n    _s();\n    var ref = (0,_swc_helpers_src_sliced_to_array_mjs__WEBPACK_IMPORTED_MODULE_5__[\"default\"])((0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_6__.useLexicalComposerContext)(), 1), editor = ref[0];\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isVisible = ref1[0], setIsVisible = ref1[1];\n    var ref2 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        top: 0,\n        left: 0\n    }), position = ref2[0], setPosition = ref2[1];\n    var ref3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isBold = ref3[0], setIsBold = ref3[1];\n    var ref4 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isUnderline = ref4[0], setIsUnderline = ref4[1];\n    var ref5 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isStrikethrough = ref5[0], setIsStrikethrough = ref5[1];\n    var ref6 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isCode = ref6[0], setIsCode = ref6[1];\n    var ref7 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isLink = ref7[0], setIsLink = ref7[1];\n    var ref8 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false), isHighlight = ref8[0], setIsHighlight = ref8[1];\n    var theme = (0,next_themes__WEBPACK_IMPORTED_MODULE_4__.useTheme)().theme;\n    var updateToolbar = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var selection = (0,lexical__WEBPACK_IMPORTED_MODULE_7__.$getSelection)();\n        if ((0,lexical__WEBPACK_IMPORTED_MODULE_7__.$isRangeSelection)(selection)) {\n            var anchorNode = selection.anchor.getNode();\n            var element = anchorNode.getKey() === \"root\" ? anchorNode : anchorNode.getTopLevelElementOrThrow();\n            var elementKey = element.getKey();\n            var elementDOM = editor.getElementByKey(elementKey);\n            // 显示条件：只有当有选中文本时才显示\n            var hasSelection = selection.getTextContent() !== \"\";\n            if (elementDOM !== null && hasSelection) {\n                var nativeSelection = window.getSelection();\n                var rootElement = editor.getRootElement();\n                if (nativeSelection !== null && rootElement !== null && rootElement.contains(nativeSelection.anchorNode)) {\n                    var rangeRect = nativeSelection.getRangeAt(0).getBoundingClientRect();\n                    setPosition({\n                        top: rangeRect.top - 60,\n                        left: rangeRect.left + rangeRect.width / 2 - 150\n                    });\n                    setIsVisible(true);\n                    // Update button states\n                    setIsBold(selection.hasFormat(\"bold\"));\n                    setIsUnderline(selection.hasFormat(\"underline\"));\n                    setIsStrikethrough(selection.hasFormat(\"strikethrough\"));\n                    setIsCode(selection.hasFormat(\"code\"));\n                    // Check if selection contains a link\n                    var node = selection.anchor.getNode();\n                    var parent = node.getParent();\n                    setIsLink((0,_lexical_link__WEBPACK_IMPORTED_MODULE_8__.$isLinkNode)(parent) || (0,_lexical_link__WEBPACK_IMPORTED_MODULE_8__.$isLinkNode)(node));\n                    // Check for highlight using Lexical's built-in format\n                    setIsHighlight(selection.hasFormat(\"highlight\"));\n                } else {\n                    setIsVisible(false);\n                }\n            } else {\n                setIsVisible(false);\n            }\n        } else {\n            setIsVisible(false);\n        }\n    }, [\n        editor\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n        return editor.registerUpdateListener(function(param) {\n            var editorState = param.editorState;\n            editorState.read(function() {\n                updateToolbar();\n            });\n        });\n    }, [\n        editor,\n        updateToolbar\n    ]);\n    var handleFormat = function(format) {\n        editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_7__.FORMAT_TEXT_COMMAND, format);\n    };\n    var handleLink = function() {\n        if (isLink) {\n            editor.dispatchCommand(_lexical_link__WEBPACK_IMPORTED_MODULE_8__.TOGGLE_LINK_COMMAND, null);\n        } else {\n            var url = prompt(\"Enter URL:\");\n            if (url) {\n                editor.dispatchCommand(_lexical_link__WEBPACK_IMPORTED_MODULE_8__.TOGGLE_LINK_COMMAND, url);\n            }\n        }\n    };\n    if (!isVisible) {\n        return null;\n    }\n    var toolbarBg = theme === \"dark\" ? \"border-gray-600\" : \"border-gray-200\";\n    var buttonText = theme === \"dark\" ? \"text-white\" : \"text-gray-700\";\n    var buttonHover = theme === \"dark\" ? \"hover:text-white\" : \"hover:text-gray-900\";\n    var buttonActive = theme === \"dark\" ? \"text-white\" : \"text-gray-900\";\n    var toolbarButtons = [\n        {\n            title: \"Bold\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"font-bold text-sm\",\n                children: \"B\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 139,\n                columnNumber: 19\n            }, this),\n            isActive: isBold,\n            action: function() {\n                return handleFormat(\"bold\");\n            }\n        },\n        {\n            title: \"Strikethrough\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"line-through text-sm\",\n                children: \"S\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 145,\n                columnNumber: 19\n            }, this),\n            isActive: isStrikethrough,\n            action: function() {\n                return handleFormat(\"strikethrough\");\n            }\n        },\n        {\n            title: \"Underline\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"underline text-sm\",\n                children: \"U\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 151,\n                columnNumber: 19\n            }, this),\n            isActive: isUnderline,\n            action: function() {\n                return handleFormat(\"underline\");\n            }\n        },\n        {\n            title: \"Highlight\",\n            icon: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-xs px-1 rounded text-white\",\n                style: {\n                    backgroundColor: \"#3185eb\"\n                },\n                children: \"H\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 158,\n                columnNumber: 19\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-xs px-1 rounded\",\n                style: {\n                    backgroundColor: \"#eab834\"\n                },\n                children: \"H\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 159,\n                columnNumber: 19\n            }, this),\n            isActive: isHighlight,\n            action: function() {\n                return editor.dispatchCommand(_highlight_plugin__WEBPACK_IMPORTED_MODULE_1__.TOGGLE_HIGHLIGHT_COMMAND, undefined);\n            }\n        },\n        {\n            title: \"Code\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__.CodeIcon, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 165,\n                columnNumber: 19\n            }, this),\n            isActive: isCode,\n            action: function() {\n                return handleFormat(\"code\");\n            }\n        },\n        {\n            title: isLink ? \"Remove Link\" : \"Add Link\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__.LinkIcon, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 171,\n                columnNumber: 19\n            }, this),\n            isActive: isLink,\n            action: handleLink\n        },\n        // 分隔符\n        {\n            type: \"separator\"\n        },\n        {\n            title: \"Checklist\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm\",\n                children: \"☑\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 179,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: function() {\n                return editor.dispatchCommand(_lexical_list__WEBPACK_IMPORTED_MODULE_10__.INSERT_CHECK_LIST_COMMAND, undefined);\n            }\n        },\n        {\n            title: \"Bullet List\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm\",\n                children: \"•\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 185,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: function() {\n                return editor.dispatchCommand(_lexical_list__WEBPACK_IMPORTED_MODULE_10__.INSERT_UNORDERED_LIST_COMMAND, undefined);\n            }\n        },\n        {\n            title: \"Numbered List\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"text-sm\",\n                children: \"1.\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 191,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: function() {\n                return editor.dispatchCommand(_lexical_list__WEBPACK_IMPORTED_MODULE_10__.INSERT_ORDERED_LIST_COMMAND, undefined);\n            }\n        },\n        // 分隔符\n        {\n            type: \"separator\"\n        },\n        {\n            title: \"Indent\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__.ArrowRightIcon, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 199,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: function() {\n                editor.update(function() {\n                    var selection = (0,lexical__WEBPACK_IMPORTED_MODULE_7__.$getSelection)();\n                    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_7__.$isRangeSelection)(selection)) {\n                        return;\n                    }\n                    var nodes = selection.getNodes();\n                    var listItemsToIndent = [];\n                    // 收集所有需要缩进的列表项\n                    nodes.forEach(function(node) {\n                        var listItemNode = null;\n                        if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$isListItemNode)(node)) {\n                            listItemNode = node;\n                        } else if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$isListItemNode)(node.getParent())) {\n                            listItemNode = node.getParent();\n                        }\n                        if (listItemNode && (0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$isListItemNode)(listItemNode) && !listItemsToIndent.includes(listItemNode)) {\n                            listItemsToIndent.push(listItemNode);\n                        }\n                    });\n                    if (listItemsToIndent.length > 0) {\n                        console.log(\"\\uD83D\\uDD27 开始处理列表缩进，找到\", listItemsToIndent.length, \"个列表项\");\n                        // 按文档顺序排序列表项\n                        listItemsToIndent.sort(function(a, b) {\n                            var aIndex = a.getIndexWithinParent();\n                            var bIndex = b.getIndexWithinParent();\n                            return aIndex - bIndex;\n                        });\n                        // 处理每个列表项的缩进 - 新方案：添加父项编号前缀\n                        listItemsToIndent.forEach(function(listItemNode, index) {\n                            console.log(\"\\uD83D\\uDD27 处理第\".concat(index + 1, \"个列表项\"));\n                            // 找到前一个兄弟节点作为父系\n                            var previousSibling = listItemNode.getPreviousSibling();\n                            console.log(\"\\uD83D\\uDD27 前一个兄弟节点:\", previousSibling ? \"存在\" : \"不存在\");\n                            if (previousSibling && (0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$isListItemNode)(previousSibling)) {\n                                console.log(\"\\uD83D\\uDD27 前一个兄弟是列表项，开始添加父项前缀\");\n                                // 获取父项的编号\n                                var parentList = previousSibling.getParent();\n                                if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$isListNode)(parentList)) {\n                                    var parentIndex = previousSibling.getIndexWithinParent();\n                                    var parentNumber = parentList.getStart() + parentIndex;\n                                    console.log(\"\\uD83D\\uDD27 父项编号:\", parentNumber);\n                                    // 获取当前列表项的文本内容\n                                    var currentContent = listItemNode.getTextContent();\n                                    console.log(\"\\uD83D\\uDD27 当前内容:\", currentContent);\n                                    // 计算子项符号（第一个子项用 -，后续用 a, b, c...）\n                                    var subSymbol = \"-\";\n                                    // 检查前面是否已经有同级的缩进项\n                                    var subIndex = 0;\n                                    var checkNode = previousSibling.getNextSibling();\n                                    while(checkNode && checkNode !== listItemNode){\n                                        if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_10__.$isListItemNode)(checkNode)) {\n                                            var checkContent = checkNode.getTextContent();\n                                            if (checkContent.startsWith(\"\".concat(parentNumber, \". \"))) {\n                                                subIndex++;\n                                            }\n                                        }\n                                        checkNode = checkNode.getNextSibling();\n                                    }\n                                    // 根据子项索引确定符号\n                                    if (subIndex === 0) {\n                                        subSymbol = \"-\";\n                                    } else {\n                                        // a, b, c, d...\n                                        subSymbol = String.fromCharCode(97 + subIndex - 1) + \".\";\n                                    }\n                                    console.log(\"\\uD83D\\uDD27 子项符号:\", subSymbol);\n                                    // 创建新的文本内容：父项编号 + 子项符号 + 原内容\n                                    var newContent = \"\".concat(parentNumber, \". \").concat(subSymbol, \" \").concat(currentContent);\n                                    console.log(\"\\uD83D\\uDD27 新内容:\", newContent);\n                                    // 清空当前列表项并添加新内容\n                                    listItemNode.clear();\n                                    listItemNode.append($createTextNode(newContent));\n                                    console.log(\"\\uD83D\\uDD27 已添加父项前缀\");\n                                }\n                            } else {\n                                console.log(\"\\uD83D\\uDD27 没有合适的前一个兄弟节点，跳过此项\");\n                            }\n                        });\n                        console.log(\"\\uD83D\\uDD27 列表缩进处理完成\");\n                    } else {\n                        console.log(\"\\uD83D\\uDD27 没有找到列表项，使用通用缩进\");\n                        // 如果没有列表项，使用通用缩进\n                        editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_7__.INDENT_CONTENT_COMMAND, undefined);\n                    }\n                });\n            }\n        },\n        {\n            title: \"Outdent\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_9__.ArrowLeftIcon, {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 310,\n                columnNumber: 19\n            }, this),\n            isActive: false,\n            action: function() {\n                // 暂时使用简单的反缩进逻辑，确保列表符号正常显示\n                editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_7__.OUTDENT_CONTENT_COMMAND, undefined);\n            }\n        }, \n    ];\n    return /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_3__.createPortal)(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed z-50 \".concat(toolbarBg, \" border rounded-lg p-1.5 flex space-x-0.5 shadow-lg\"),\n        style: {\n            top: position.top,\n            left: position.left,\n            transform: \"translateX(-50%)\",\n            backgroundColor: theme === \"dark\" ? \"#3f3f46\" : \"#e4e4e7\"\n        },\n        children: toolbarButtons.map(function(button, index) {\n            if (button.type === \"separator\") {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-px h-6 \".concat(theme === \"dark\" ? \"bg-gray-600\" : \"bg-gray-300\", \" mx-1\")\n                }, index, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                    lineNumber: 333,\n                    columnNumber: 25\n                }, _this);\n            }\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: button.action,\n                title: button.title,\n                className: \"\\n                            px-2.5 py-1.5 rounded transition-colors duration-150 min-w-[30px] h-7 flex items-center justify-center text-sm font-medium\\n                            \".concat(button.isActive ? buttonActive : \"\".concat(buttonText, \" \").concat(buttonHover), \"\\n                        \"),\n                style: {\n                    backgroundColor: button.isActive ? theme === \"dark\" ? \"#3185eb\" : \"#eab834\" : \"transparent\"\n                },\n                onMouseEnter: function(e) {\n                    if (!button.isActive) {\n                        e.currentTarget.style.backgroundColor = theme === \"dark\" ? \"#3185eb\" : \"#eab834\";\n                        if (theme === \"dark\") {\n                            e.currentTarget.style.color = \"white\";\n                        }\n                    }\n                },\n                onMouseLeave: function(e) {\n                    if (!button.isActive) {\n                        e.currentTarget.style.backgroundColor = \"transparent\";\n                        e.currentTarget.style.color = \"\";\n                    }\n                },\n                children: button.icon\n            }, index, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n                lineNumber: 341,\n                columnNumber: 21\n            }, _this);\n        })\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\motea-docker-main\\\\components\\\\editor\\\\plugins\\\\floating-toolbar-plugin.tsx\",\n        lineNumber: 321,\n        columnNumber: 9\n    }, this), document.body);\n}\n_s(FloatingToolbarPlugin, \"FbU03gSrzEPMPysVlTtv8K886Dg=\", false, function() {\n    return [\n        _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_6__.useLexicalComposerContext,\n        next_themes__WEBPACK_IMPORTED_MODULE_4__.useTheme\n    ];\n});\n_c = FloatingToolbarPlugin;\nvar _c;\n$RefreshReg$(_c, \"FloatingToolbarPlugin\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/editor/plugins/floating-toolbar-plugin.tsx\n"));

/***/ })

});