{"version": 3, "names": ["isNode", "node", "VISITOR_KEYS", "type"], "sources": ["../../src/validators/isNode.ts"], "sourcesContent": ["import { VISITOR_KEYS } from \"../definitions\";\nimport type * as t from \"..\";\n\nexport default function isNode(node: any): node is t.Node {\n  return !!(node && VISITOR_KEYS[node.type]);\n}\n"], "mappings": ";;;;;;;AAAA;;AAGe,SAASA,MAAT,CAAgBC,IAAhB,EAA2C;EACxD,OAAO,CAAC,EAAEA,IAAI,IAAIC,yBAAA,CAAaD,IAAI,CAACE,IAAlB,CAAV,CAAR;AACD"}