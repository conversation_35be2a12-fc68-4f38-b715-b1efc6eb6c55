{"version": 3, "names": ["isReactComponent", "buildMatchMemberExpression"], "sources": ["../../../src/validators/react/isReactComponent.ts"], "sourcesContent": ["import buildMatchMemberExpression from \"../buildMatchMemberExpression\";\n\nconst isReactComponent = buildMatchMemberExpression(\"React.Component\");\n\nexport default isReactComponent;\n"], "mappings": ";;;;;;;AAAA;;AAEA,MAAMA,gBAAgB,GAAG,IAAAC,mCAAA,EAA2B,iBAA3B,CAAzB;eAEeD,gB"}